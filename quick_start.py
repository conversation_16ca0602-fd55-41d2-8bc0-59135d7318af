#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للتطبيق
"""

import sys
import os

def main():
    """
    تشغيل سريع للتطبيق
    """
    print("🚀 تشغيل سريع لنظام إدارة المبيعات والمخزون")
    print("=" * 50)
    
    try:
        # استيراد التطبيق
        from PySide6.QtWidgets import QApplication
        from main import النافذة_الرئيسية
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = النافذة_الرئيسية()
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("📋 جميع ميزات فواتير الشراء متاحة الآن!")
        print("📝 ملاحظة: إذا لم تكن قاعدة البيانات معدة، استخدم: python setup.py")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 حل المشكلة: pip install -r requirements.txt")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("💡 للحصول على تفاصيل أكثر، استخدم: python run.py")
        return 1

if __name__ == "__main__":
    sys.exit(main())
