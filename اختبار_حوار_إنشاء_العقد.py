# -*- coding: utf-8 -*-
"""
اختبار حوار إنشاء العقد الشامل
يتضمن اختبار جميع الوظائف المطورة
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
from db import تهيئة_قاعدة_البيانات
from contract_creation_dialog import حوار_إنشاء_عقد

class نافذة_اختبار_حوار_العقد(QMainWindow):
    """
    نافذة اختبار حوار إنشاء العقد
    """
    
    def __init__(self):
        """
        دالة التهيئة لنافذة الاختبار
        """
        super().__init__()
        self.إعداد_النافذة()
        self.إنشاء_الواجهة()
    
    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة الرئيسية
        """
        self.setWindowTitle("اختبار حوار إنشاء العقد - نظام إدارة المبيعات والأقساط")
        self.setGeometry(100, 100, 600, 400)
        self.setLayoutDirection(Qt.RightToLeft)
    
    def إنشاء_الواجهة(self):
        """
        دالة إنشاء واجهة الاختبار
        """
        الواجهة_المركزية = QWidget()
        self.setCentralWidget(الواجهة_المركزية)
        
        التخطيط = QVBoxLayout(الواجهة_المركزية)
        التخطيط.setContentsMargins(50, 50, 50, 50)
        التخطيط.setSpacing(20)
        
        # عنوان الاختبار
        from PySide6.QtWidgets import QLabel
        عنوان = QLabel("اختبار حوار إنشاء العقد الشامل")
        عنوان.setStyleSheet("""
        QLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            border-left: 5px solid #8e44ad;
        }
        """)
        التخطيط.addWidget(عنوان)
        
        # معلومات الاختبار
        معلومات = QLabel("""
المميزات المتاحة في حوار إنشاء العقد:

✅ اقتراح رقم عقد تلقائي مع التحقق من عدم التكرار
✅ اختيار العميل مع البحث المتقدم
✅ ربط فاتورة واحدة أو متعددة بالعقد
✅ حساب المبالغ تلقائياً من الفواتير المحددة
✅ دعم أنواع الأقساط المختلفة (شهري، أسبوعي، ربع سنوي، سنوي)
✅ ربط العقد بالبنك للسداد المصرفي
✅ حساب تواريخ الاستحقاق تلقائياً
✅ إنشاء الأقساط تلقائياً عند إنشاء العقد
✅ معاينة العقد قبل الإنشاء
✅ تصميم متجاوب مع دعم RTL كامل
        """)
        معلومات.setStyleSheet("""
        QLabel {
            font-size: 14px;
            color: #34495e;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bdc3c7;
        }
        """)
        التخطيط.addWidget(معلومات)
        
        # زر فتح حوار إنشاء العقد
        زر_فتح_حوار = QPushButton("فتح حوار إنشاء العقد")
        زر_فتح_حوار.setStyleSheet("""
        QPushButton {
            background-color: #8e44ad;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            min-width: 200px;
        }
        QPushButton:hover {
            background-color: #9b59b6;
        }
        QPushButton:pressed {
            background-color: #7d3c98;
        }
        """)
        زر_فتح_حوار.clicked.connect(self.فتح_حوار_إنشاء_العقد)
        التخطيط.addWidget(زر_فتح_حوار, alignment=Qt.AlignCenter)
        
        التخطيط.addStretch()
    
    def فتح_حوار_إنشاء_العقد(self):
        """
        دالة فتح حوار إنشاء العقد
        """
        try:
            حوار = حوار_إنشاء_عقد(self)
            نتيجة = حوار.exec()
            
            if نتيجة == حوار.Accepted:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self, "نجح", 
                    "تم إنشاء العقد بنجاح!\n"
                    "يمكنك الآن مراجعة العقد في واجهة إدارة العقود."
                )
            else:
                print("تم إلغاء إنشاء العقد")
                
        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self, "خطأ", 
                f"فشل في فتح حوار إنشاء العقد:\n{str(e)}"
            )
            print(f"تفاصيل الخطأ: {str(e)}")

def main():
    """
    دالة تشغيل التطبيق الرئيسية
    """
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تهيئة قاعدة البيانات
    print("جاري تهيئة قاعدة البيانات...")
    try:
        تهيئة_قاعدة_البيانات()
        print("تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
        return
    
    # إنشاء النافذة الرئيسية
    نافذة = نافذة_اختبار_حوار_العقد()
    نافذة.show()
    
    print("=== معلومات حوار إنشاء العقد ===")
    print("التبويبات المتاحة:")
    print("1. معلومات العقد - رقم العقد، العميل، نوع السداد، البنك")
    print("2. ربط الفواتير - اختيار الفواتير المراد ربطها بالعقد")
    print("3. تفاصيل الأقساط - مبلغ العقد، نوع القسط، عدد الأقساط")
    print("\nالمميزات الرئيسية:")
    print("- رقم عقد تلقائي فريد")
    print("- ربط متعدد الفواتير")
    print("- حساب تلقائي للمبالغ والتواريخ")
    print("- دعم جميع أنواع الأقساط")
    print("- ربط بنكي للسداد المصرفي")
    print("- معاينة قبل الإنشاء")
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
