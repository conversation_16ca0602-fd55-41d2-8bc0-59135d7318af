# -*- coding: utf-8 -*-
"""
مساعد الأيقونات - إنشاء أيقونات بسيطة للتطبيق
"""

import os
from PySide6.QtGui import QPixmap, QPainter, QColor, QBrush, QPen
from PySide6.QtCore import Qt

def إنشاء_مجلد_الأيقونات():
    """
    إنشاء مجلد الأيقونات إذا لم يكن موجوداً
    """
    if not os.path.exists("icons"):
        os.makedirs("icons")

def إنشاء_أيقونة_بسيطة(اللون, الحجم=24):
    """
    إنشاء أيقونة بسيطة ملونة
    """
    pixmap = QPixmap(الحجم, الحجم)
    pixmap.fill(Qt.transparent)
    
    painter = QPainter(pixmap)
    painter.setRender<PERSON>int(QPainter.Antialiasing)
    
    # رسم دائرة ملونة
    painter.setBrush(QBrush(QColor(اللون)))
    painter.setPen(QPen(QColor("#ffffff"), 2))
    painter.drawEllipse(2, 2, الحجم-4, الحجم-4)
    
    painter.end()
    return pixmap

def إنشاء_جميع_الأيقونات():
    """
    إنشاء جميع الأيقونات المطلوبة للتطبيق
    """
    إنشاء_مجلد_الأيقونات()
    
    # قائمة الأيقونات والألوان
    الأيقونات = {
        "add_bank.png": "#27ae60",
        "add_branch.png": "#3498db", 
        "edit.png": "#f39c12",
        "delete.png": "#e74c3c",
        "refresh.png": "#9b59b6",
        "save.png": "#27ae60",
        "cancel.png": "#95a5a6",
        "app_icon.png": "#2c3e50"
    }
    
    for اسم_الملف, اللون in الأيقونات.items():
        مسار_الملف = f"icons/{اسم_الملف}"
        if not os.path.exists(مسار_الملف):
            أيقونة = إنشاء_أيقونة_بسيطة(اللون)
            أيقونة.save(مسار_الملف)
    
    print("تم إنشاء جميع الأيقونات بنجاح")

if __name__ == "__main__":
    إنشاء_جميع_الأيقونات()
