# -*- coding: utf-8 -*-
"""
حوار إنشاء العقد الشامل
يتضمن جميع الوظائف المطلوبة لإنشاء عقد متكامل
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime, date, timedelta
from decimal import Decimal, InvalidOperation
from db import قاعدة_البيانات

class حوار_إنشاء_عقد(QDialog):
    """
    حوار إنشاء عقد جديد مع جميع الوظائف المطلوبة
    """
    
    def __init__(self, parent=None):
        """
        دالة التهيئة لحوار إنشاء العقد
        """
        super().__init__(parent)
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.العميل_المحدد = None
        self.الفواتير_المحددة = []
        self.إجمالي_مبلغ_الفواتير = Decimal('0.00')
        
        self.إعداد_الحوار()
        self.تحميل_البيانات_الأولية()
        self.تطبيق_الأنماط()
    
    def إعداد_الحوار(self):
        """
        دالة إعداد حوار إنشاء العقد
        """
        self.setWindowTitle("إنشاء عقد جديد")
        self.setModal(True)
        self.setFixedSize(1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)
        
        # عنوان الحوار
        عنوان = QLabel("إنشاء عقد جديد")
        عنوان.setObjectName("dialog_title")
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # تبويبات الحوار
        self.تبويبات = QTabWidget()
        
        # تبويب معلومات العقد الأساسية
        تبويب_أساسي = QWidget()
        self.إنشاء_تبويب_معلومات_أساسية(تبويب_أساسي)
        self.تبويبات.addTab(تبويب_أساسي, "معلومات العقد")
        
        # تبويب ربط الفواتير
        تبويب_فواتير = QWidget()
        self.إنشاء_تبويب_ربط_الفواتير(تبويب_فواتير)
        self.تبويبات.addTab(تبويب_فواتير, "ربط الفواتير")
        
        # تبويب تفاصيل الأقساط
        تبويب_أقساط = QWidget()
        self.إنشاء_تبويب_تفاصيل_الأقساط(تبويب_أقساط)
        self.تبويبات.addTab(تبويب_أقساط, "تفاصيل الأقساط")
        
        التخطيط_الرئيسي.addWidget(self.تبويبات)
        
        # أزرار الحوار
        self.إنشاء_أزرار_الحوار(التخطيط_الرئيسي)
    
    def إنشاء_تبويب_معلومات_أساسية(self, تبويب):
        """
        دالة إنشاء تبويب المعلومات الأساسية للعقد
        """
        تخطيط = QVBoxLayout(تبويب)
        تخطيط.setContentsMargins(15, 15, 15, 15)
        تخطيط.setSpacing(15)
        
        # قسم رقم العقد
        مجموعة_رقم_عقد = QGroupBox("رقم العقد")
        تخطيط_رقم_عقد = QHBoxLayout(مجموعة_رقم_عقد)
        
        self.حقل_رقم_عقد = QLineEdit()
        self.حقل_رقم_عقد.setPlaceholderText("سيتم اقتراح رقم تلقائي")
        
        زر_اقتراح_رقم = QPushButton("اقتراح رقم تلقائي")
        زر_اقتراح_رقم.clicked.connect(self.اقتراح_رقم_عقد_تلقائي)
        
        زر_التحقق_من_الرقم = QPushButton("التحقق من الرقم")
        زر_التحقق_من_الرقم.clicked.connect(self.التحقق_من_رقم_العقد)
        
        تخطيط_رقم_عقد.addWidget(QLabel("رقم العقد:"))
        تخطيط_رقم_عقد.addWidget(self.حقل_رقم_عقد)
        تخطيط_رقم_عقد.addWidget(زر_اقتراح_رقم)
        تخطيط_رقم_عقد.addWidget(زر_التحقق_من_الرقم)
        
        تخطيط.addWidget(مجموعة_رقم_عقد)
        
        # قسم اختيار العميل
        مجموعة_عميل = QGroupBox("اختيار العميل")
        تخطيط_عميل = QVBoxLayout(مجموعة_عميل)
        
        # شريط البحث عن العميل
        تخطيط_بحث_عميل = QHBoxLayout()
        self.حقل_بحث_عميل = QLineEdit()
        self.حقل_بحث_عميل.setPlaceholderText("البحث عن عميل (الاسم أو رقم الهاتف)")
        self.حقل_بحث_عميل.textChanged.connect(self.البحث_عن_عميل)
        
        تخطيط_بحث_عميل.addWidget(QLabel("البحث:"))
        تخطيط_بحث_عميل.addWidget(self.حقل_بحث_عميل)
        
        تخطيط_عميل.addLayout(تخطيط_بحث_عميل)
        
        # قائمة العملاء
        self.قائمة_عملاء = QComboBox()
        self.قائمة_عملاء.currentTextChanged.connect(self.عند_اختيار_عميل)
        تخطيط_عميل.addWidget(self.قائمة_عملاء)
        
        # معلومات العميل المحدد
        self.تسمية_معلومات_عميل = QLabel("لم يتم اختيار عميل")
        self.تسمية_معلومات_عميل.setStyleSheet("color: #7f8c8d; font-style: italic;")
        تخطيط_عميل.addWidget(self.تسمية_معلومات_عميل)
        
        تخطيط.addWidget(مجموعة_عميل)
        
        # قسم نوع السداد والبنك
        مجموعة_سداد = QGroupBox("نوع السداد والبنك")
        تخطيط_سداد = QVBoxLayout(مجموعة_سداد)
        
        # نوع السداد
        تخطيط_نوع_سداد = QHBoxLayout()
        self.قائمة_نوع_سداد = QComboBox()
        self.قائمة_نوع_سداد.addItems(["نقدي", "أقساط مصرفية", "أقساط نقدية"])
        self.قائمة_نوع_سداد.currentTextChanged.connect(self.عند_تغيير_نوع_السداد)
        
        تخطيط_نوع_سداد.addWidget(QLabel("نوع السداد:"))
        تخطيط_نوع_سداد.addWidget(self.قائمة_نوع_سداد)
        تخطيط_نوع_سداد.addStretch()
        
        تخطيط_سداد.addLayout(تخطيط_نوع_سداد)
        
        # اختيار البنك
        تخطيط_بنك = QHBoxLayout()
        self.قائمة_بنك = QComboBox()
        self.قائمة_بنك.setEnabled(False)  # معطل في البداية
        
        تخطيط_بنك.addWidget(QLabel("البنك:"))
        تخطيط_بنك.addWidget(self.قائمة_بنك)
        تخطيط_بنك.addStretch()
        
        تخطيط_سداد.addLayout(تخطيط_بنك)
        
        تخطيط.addWidget(مجموعة_سداد)
        
        # قسم الموظف المسؤول
        مجموعة_موظف = QGroupBox("الموظف المسؤول")
        تخطيط_موظف = QHBoxLayout(مجموعة_موظف)
        
        self.قائمة_موظف = QComboBox()
        تخطيط_موظف.addWidget(QLabel("الموظف:"))
        تخطيط_موظف.addWidget(self.قائمة_موظف)
        تخطيط_موظف.addStretch()
        
        تخطيط.addWidget(مجموعة_موظف)
        
        تخطيط.addStretch()
    
    def إنشاء_تبويب_ربط_الفواتير(self, تبويب):
        """
        دالة إنشاء تبويب ربط الفواتير بالعقد
        """
        تخطيط = QVBoxLayout(تبويب)
        تخطيط.setContentsMargins(15, 15, 15, 15)
        تخطيط.setSpacing(15)
        
        # تعليمات
        تعليمات = QLabel("اختر الفواتير التي تريد ربطها بالعقد:")
        تعليمات.setStyleSheet("font-weight: bold; color: #2c3e50;")
        تخطيط.addWidget(تعليمات)
        
        # جدول الفواتير المتاحة
        self.جدول_فواتير_متاحة = QTableWidget()
        self.جدول_فواتير_متاحة.setObjectName("available_invoices_table")
        
        الأعمدة = ["اختيار", "رقم الفاتورة", "تاريخ الفاتورة", "المبلغ النهائي", "المبلغ المتبقي", "حالة السداد"]
        self.جدول_فواتير_متاحة.setColumnCount(len(الأعمدة))
        self.جدول_فواتير_متاحة.setHorizontalHeaderLabels(الأعمدة)
        
        # تنسيق الجدول
        self.جدول_فواتير_متاحة.setAlternatingRowColors(True)
        self.جدول_فواتير_متاحة.setSelectionBehavior(QAbstractItemView.SelectRows)
        header = self.جدول_فواتير_متاحة.horizontalHeader()
        header.setStretchLastSection(True)
        
        تخطيط.addWidget(self.جدول_فواتير_متاحة)
        
        # ملخص الفواتير المحددة
        مجموعة_ملخص = QGroupBox("ملخص الفواتير المحددة")
        تخطيط_ملخص = QVBoxLayout(مجموعة_ملخص)
        
        self.تسمية_عدد_فواتير = QLabel("عدد الفواتير المحددة: 0")
        self.تسمية_إجمالي_مبلغ = QLabel("إجمالي المبلغ: 0.00")
        
        تخطيط_ملخص.addWidget(self.تسمية_عدد_فواتير)
        تخطيط_ملخص.addWidget(self.تسمية_إجمالي_مبلغ)
        
        تخطيط.addWidget(مجموعة_ملخص)
    
    def إنشاء_تبويب_تفاصيل_الأقساط(self, تبويب):
        """
        دالة إنشاء تبويب تفاصيل الأقساط
        """
        تخطيط = QVBoxLayout(تبويب)
        تخطيط.setContentsMargins(15, 15, 15, 15)
        تخطيط.setSpacing(15)
        
        # قسم مبلغ العقد
        مجموعة_مبلغ = QGroupBox("مبلغ العقد")
        تخطيط_مبلغ = QHBoxLayout(مجموعة_مبلغ)
        
        self.حقل_مبلغ_عقد = QLineEdit()
        self.حقل_مبلغ_عقد.setPlaceholderText("0.00")
        self.حقل_مبلغ_عقد.textChanged.connect(self.حساب_تفاصيل_الأقساط)
        
        زر_استخدام_مبلغ_فواتير = QPushButton("استخدام مبلغ الفواتير")
        زر_استخدام_مبلغ_فواتير.clicked.connect(self.استخدام_مبلغ_الفواتير)
        
        تخطيط_مبلغ.addWidget(QLabel("مبلغ العقد:"))
        تخطيط_مبلغ.addWidget(self.حقل_مبلغ_عقد)
        تخطيط_مبلغ.addWidget(زر_استخدام_مبلغ_فواتير)
        تخطيط_مبلغ.addStretch()
        
        تخطيط.addWidget(مجموعة_مبلغ)
        
        # قسم تفاصيل الأقساط
        مجموعة_أقساط = QGroupBox("تفاصيل الأقساط")
        تخطيط_أقساط = QVBoxLayout(مجموعة_أقساط)
        
        # نوع القسط
        تخطيط_نوع_قسط = QHBoxLayout()
        self.قائمة_نوع_قسط = QComboBox()
        self.قائمة_نوع_قسط.addItems(["شهري", "أسبوعي", "ربع سنوي", "سنوي"])
        self.قائمة_نوع_قسط.currentTextChanged.connect(self.حساب_تفاصيل_الأقساط)
        
        تخطيط_نوع_قسط.addWidget(QLabel("نوع القسط:"))
        تخطيط_نوع_قسط.addWidget(self.قائمة_نوع_قسط)
        تخطيط_نوع_قسط.addStretch()
        
        تخطيط_أقساط.addLayout(تخطيط_نوع_قسط)
        
        # عدد الأقساط أو مبلغ القسط
        تخطيط_حساب = QHBoxLayout()
        
        # طريقة الحساب
        self.راديو_عدد_أقساط = QRadioButton("تحديد عدد الأقساط")
        self.راديو_مبلغ_قسط = QRadioButton("تحديد مبلغ القسط")
        self.راديو_عدد_أقساط.setChecked(True)
        
        self.راديو_عدد_أقساط.toggled.connect(self.تغيير_طريقة_الحساب)
        self.راديو_مبلغ_قسط.toggled.connect(self.تغيير_طريقة_الحساب)
        
        تخطيط_حساب.addWidget(self.راديو_عدد_أقساط)
        تخطيط_حساب.addWidget(self.راديو_مبلغ_قسط)
        تخطيط_حساب.addStretch()
        
        تخطيط_أقساط.addLayout(تخطيط_حساب)
        
        # حقول الإدخال
        تخطيط_إدخال = QHBoxLayout()
        
        self.حقل_عدد_أقساط = QLineEdit()
        self.حقل_عدد_أقساط.setPlaceholderText("عدد الأقساط")
        self.حقل_عدد_أقساط.textChanged.connect(self.حساب_تفاصيل_الأقساط)
        
        self.حقل_مبلغ_قسط = QLineEdit()
        self.حقل_مبلغ_قسط.setPlaceholderText("مبلغ القسط")
        self.حقل_مبلغ_قسط.setEnabled(False)
        self.حقل_مبلغ_قسط.textChanged.connect(self.حساب_تفاصيل_الأقساط)
        
        تخطيط_إدخال.addWidget(self.حقل_عدد_أقساط)
        تخطيط_إدخال.addWidget(self.حقل_مبلغ_قسط)
        
        تخطيط_أقساط.addLayout(تخطيط_إدخال)
        
        تخطيط.addWidget(مجموعة_أقساط)
        
        # قسم التواريخ
        مجموعة_تواريخ = QGroupBox("تواريخ العقد")
        تخطيط_تواريخ = QHBoxLayout(مجموعة_تواريخ)
        
        self.حقل_تاريخ_بداية = QDateEdit()
        self.حقل_تاريخ_بداية.setDate(QDate.currentDate())
        self.حقل_تاريخ_بداية.dateChanged.connect(self.حساب_تاريخ_الانتهاء)
        
        self.تسمية_تاريخ_انتهاء = QLabel("تاريخ الانتهاء: غير محدد")
        
        تخطيط_تواريخ.addWidget(QLabel("تاريخ البداية:"))
        تخطيط_تواريخ.addWidget(self.حقل_تاريخ_بداية)
        تخطيط_تواريخ.addWidget(self.تسمية_تاريخ_انتهاء)
        تخطيط_تواريخ.addStretch()
        
        تخطيط.addWidget(مجموعة_تواريخ)
        
        # ملخص العقد
        مجموعة_ملخص_عقد = QGroupBox("ملخص العقد")
        تخطيط_ملخص_عقد = QVBoxLayout(مجموعة_ملخص_عقد)
        
        self.تسمية_ملخص_عقد = QLabel("أدخل تفاصيل العقد لعرض الملخص")
        self.تسمية_ملخص_عقد.setStyleSheet("color: #7f8c8d; font-style: italic;")
        
        تخطيط_ملخص_عقد.addWidget(self.تسمية_ملخص_عقد)
        
        تخطيط.addWidget(مجموعة_ملخص_عقد)
        
        تخطيط.addStretch()
    
    def إنشاء_أزرار_الحوار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء أزرار الحوار
        """
        إطار_أزرار = QFrame()
        تخطيط_أزرار = QHBoxLayout(إطار_أزرار)
        تخطيط_أزرار.setContentsMargins(0, 10, 0, 0)
        
        # زر إنشاء العقد
        self.زر_إنشاء_عقد = QPushButton("إنشاء العقد")
        self.زر_إنشاء_عقد.setObjectName("create_button")
        self.زر_إنشاء_عقد.clicked.connect(self.إنشاء_العقد)
        
        # زر معاينة العقد
        زر_معاينة = QPushButton("معاينة العقد")
        زر_معاينة.clicked.connect(self.معاينة_العقد)
        
        # زر إلغاء
        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.clicked.connect(self.reject)
        
        تخطيط_أزرار.addStretch()
        تخطيط_أزرار.addWidget(زر_معاينة)
        تخطيط_أزرار.addWidget(self.زر_إنشاء_عقد)
        تخطيط_أزرار.addWidget(زر_إلغاء)
        
        التخطيط_الرئيسي.addWidget(إطار_أزرار)

    def تحميل_البيانات_الأولية(self):
        """
        دالة تحميل البيانات الأولية للحوار
        """
        # اقتراح رقم عقد تلقائي
        self.اقتراح_رقم_عقد_تلقائي()

        # تحميل العملاء
        self.تحميل_العملاء()

        # تحميل البنوك
        self.تحميل_البنوك()

        # تحميل الموظفين
        self.تحميل_الموظفين()

    def اقتراح_رقم_عقد_تلقائي(self):
        """
        دالة اقتراح رقم عقد تلقائي
        """
        try:
            رقم_مقترح = self.قاعدة_البيانات.إنشاء_رقم_عقد_تلقائي()
            self.حقل_رقم_عقد.setText(رقم_مقترح)
            self.حقل_رقم_عقد.setStyleSheet("background-color: #d5f4e6; border: 2px solid #27ae60;")
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في اقتراح رقم العقد: {str(e)}")

    def التحقق_من_رقم_العقد(self):
        """
        دالة التحقق من صحة رقم العقد
        """
        رقم_العقد = self.حقل_رقم_عقد.text().strip()

        if not رقم_العقد:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم العقد")
            return

        try:
            if self.قاعدة_البيانات.التحقق_من_رقم_العقد(رقم_العقد):
                self.حقل_رقم_عقد.setStyleSheet("background-color: #d5f4e6; border: 2px solid #27ae60;")
                QMessageBox.information(self, "نجح", "رقم العقد متاح للاستخدام")
            else:
                self.حقل_رقم_عقد.setStyleSheet("background-color: #f8d7da; border: 2px solid #dc3545;")
                QMessageBox.warning(self, "تحذير", "رقم العقد مستخدم بالفعل، يرجى اختيار رقم آخر")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في التحقق من رقم العقد: {str(e)}")

    def تحميل_العملاء(self):
        """
        دالة تحميل قائمة العملاء
        """
        try:
            استعلام = """
            SELECT رقم_العميل, CONCAT(الاسم_الأول, ' ', اللقب) as الاسم_الكامل,
                   رقم_الهاتف, رقم_البنك, رقم_الحساب
            FROM العملاء
            WHERE حالة_النشاط = TRUE
            ORDER BY الاسم_الأول, اللقب
            """

            العملاء = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_عملاء.clear()
            self.قائمة_عملاء.addItem("اختر عميل...")

            self.بيانات_العملاء = {}

            if العملاء:
                for عميل in العملاء:
                    رقم_عميل, اسم_كامل, رقم_هاتف, رقم_بنك, رقم_حساب = عميل
                    نص_عرض = f"{اسم_كامل} - {رقم_هاتف or 'بدون هاتف'}"

                    self.قائمة_عملاء.addItem(نص_عرض)
                    self.بيانات_العملاء[نص_عرض] = {
                        'رقم_العميل': رقم_عميل,
                        'الاسم_الكامل': اسم_كامل,
                        'رقم_الهاتف': رقم_هاتف,
                        'رقم_البنك': رقم_بنك,
                        'رقم_الحساب': رقم_حساب
                    }

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل العملاء: {str(e)}")

    def تحميل_البنوك(self):
        """
        دالة تحميل قائمة البنوك
        """
        try:
            استعلام = """
            SELECT رقم_البنك, اسم_البنك, رقم_حساب_الشركة
            FROM البنوك
            WHERE حالة_النشاط = TRUE
            ORDER BY اسم_البنك
            """

            البنوك = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_بنك.clear()
            self.قائمة_بنك.addItem("اختر بنك...")

            if البنوك:
                for بنك in البنوك:
                    رقم_بنك, اسم_بنك, رقم_حساب = بنك
                    نص_عرض = f"{اسم_بنك}"
                    if رقم_حساب:
                        نص_عرض += f" - {رقم_حساب}"

                    self.قائمة_بنك.addItem(نص_عرض)
                    self.قائمة_بنك.setItemData(self.قائمة_بنك.count() - 1, رقم_بنك)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البنوك: {str(e)}")

    def تحميل_الموظفين(self):
        """
        دالة تحميل قائمة الموظفين
        """
        try:
            استعلام = """
            SELECT رقم_الموظف, الاسم_الكامل, المنصب
            FROM الموظفين
            WHERE حالة_النشاط = TRUE
            ORDER BY الاسم_الكامل
            """

            الموظفين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_موظف.clear()
            self.قائمة_موظف.addItem("اختر موظف...")

            if الموظفين:
                for موظف in الموظفين:
                    رقم_موظف, اسم_كامل, منصب = موظف
                    نص_عرض = f"{اسم_كامل}"
                    if منصب:
                        نص_عرض += f" - {منصب}"

                    self.قائمة_موظف.addItem(نص_عرض)
                    self.قائمة_موظف.setItemData(self.قائمة_موظف.count() - 1, رقم_موظف)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الموظفين: {str(e)}")

    def البحث_عن_عميل(self):
        """
        دالة البحث عن عميل
        """
        نص_البحث = self.حقل_بحث_عميل.text().strip().lower()

        if not نص_البحث:
            # إظهار جميع العملاء
            for i in range(self.قائمة_عملاء.count()):
                self.قائمة_عملاء.setItemData(i, True, Qt.UserRole)
            return

        # فلترة العملاء
        for i in range(1, self.قائمة_عملاء.count()):  # تجاهل العنصر الأول
            نص_العنصر = self.قائمة_عملاء.itemText(i).lower()
            if نص_البحث in نص_العنصر:
                self.قائمة_عملاء.setItemData(i, True, Qt.UserRole)
            else:
                self.قائمة_عملاء.setItemData(i, False, Qt.UserRole)

    def عند_اختيار_عميل(self):
        """
        دالة تنفذ عند اختيار عميل
        """
        نص_العميل_المحدد = self.قائمة_عملاء.currentText()

        if نص_العميل_المحدد == "اختر عميل..." or نص_العميل_المحدد not in self.بيانات_العملاء:
            self.العميل_المحدد = None
            self.تسمية_معلومات_عميل.setText("لم يتم اختيار عميل")
            self.تسمية_معلومات_عميل.setStyleSheet("color: #7f8c8d; font-style: italic;")
            return

        # تحديث معلومات العميل
        بيانات_عميل = self.بيانات_العملاء[نص_العميل_المحدد]
        self.العميل_المحدد = بيانات_عميل['رقم_العميل']

        معلومات = f"العميل: {بيانات_عميل['الاسم_الكامل']}"
        if بيانات_عميل['رقم_الهاتف']:
            معلومات += f" | الهاتف: {بيانات_عميل['رقم_الهاتف']}"
        if بيانات_عميل['رقم_البنك']:
            معلومات += f" | له حساب بنكي"

        self.تسمية_معلومات_عميل.setText(معلومات)
        self.تسمية_معلومات_عميل.setStyleSheet("color: #27ae60; font-weight: bold;")

        # تحميل فواتير العميل
        self.تحميل_فواتير_العميل()

        # تحديد البنك تلقائياً إذا كان للعميل حساب بنكي
        if بيانات_عميل['رقم_البنك']:
            for i in range(self.قائمة_بنك.count()):
                if self.قائمة_بنك.itemData(i) == بيانات_عميل['رقم_البنك']:
                    self.قائمة_بنك.setCurrentIndex(i)
                    break

    def تحميل_فواتير_العميل(self):
        """
        دالة تحميل فواتير العميل المحدد
        """
        if not self.العميل_المحدد:
            return

        try:
            استعلام = """
            SELECT رقم_الفاتورة, تاريخ_الفاتورة, المبلغ_النهائي,
                   المبلغ_المتبقي, حالة_السداد
            FROM فواتير_البيع
            WHERE رقم_العميل = %s
            AND حالة_السداد IN ('جزئية', 'غير مسددة')
            AND نوع_البيع IN ('آجل', 'أقساط')
            ORDER BY تاريخ_الفاتورة DESC
            """

            الفواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.العميل_المحدد,))

            self.جدول_فواتير_متاحة.setRowCount(0)

            if الفواتير:
                self.جدول_فواتير_متاحة.setRowCount(len(الفواتير))

                for صف, فاتورة in enumerate(الفواتير):
                    رقم_فاتورة, تاريخ_فاتورة, مبلغ_نهائي, مبلغ_متبقي, حالة_سداد = فاتورة

                    # عمود الاختيار
                    مربع_اختيار = QCheckBox()
                    مربع_اختيار.stateChanged.connect(self.تحديث_ملخص_الفواتير)
                    self.جدول_فواتير_متاحة.setCellWidget(صف, 0, مربع_اختيار)

                    # باقي الأعمدة
                    self.جدول_فواتير_متاحة.setItem(صف, 1, QTableWidgetItem(str(رقم_فاتورة)))
                    self.جدول_فواتير_متاحة.setItem(صف, 2, QTableWidgetItem(str(تاريخ_فاتورة)))
                    self.جدول_فواتير_متاحة.setItem(صف, 3, QTableWidgetItem(f"{مبلغ_نهائي:,.2f}"))
                    self.جدول_فواتير_متاحة.setItem(صف, 4, QTableWidgetItem(f"{مبلغ_متبقي:,.2f}"))

                    # تلوين حالة السداد
                    عنصر_حالة = QTableWidgetItem(حالة_سداد)
                    if حالة_سداد == "جزئية":
                        عنصر_حالة.setBackground(QColor("#fff3cd"))
                    elif حالة_سداد == "غير مسددة":
                        عنصر_حالة.setBackground(QColor("#f8d7da"))

                    self.جدول_فواتير_متاحة.setItem(صف, 5, عنصر_حالة)

            # تحديث الملخص
            self.تحديث_ملخص_الفواتير()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل فواتير العميل: {str(e)}")

    def تحديث_ملخص_الفواتير(self):
        """
        دالة تحديث ملخص الفواتير المحددة
        """
        عدد_فواتير_محددة = 0
        إجمالي_مبلغ = Decimal('0.00')

        for صف in range(self.جدول_فواتير_متاحة.rowCount()):
            مربع_اختيار = self.جدول_فواتير_متاحة.cellWidget(صف, 0)
            if مربع_اختيار and مربع_اختيار.isChecked():
                عدد_فواتير_محددة += 1

                # الحصول على المبلغ المتبقي
                عنصر_مبلغ = self.جدول_فواتير_متاحة.item(صف, 4)
                if عنصر_مبلغ:
                    مبلغ_نص = عنصر_مبلغ.text().replace(',', '')
                    إجمالي_مبلغ += Decimal(مبلغ_نص)

        self.تسمية_عدد_فواتير.setText(f"عدد الفواتير المحددة: {عدد_فواتير_محددة}")
        self.تسمية_إجمالي_مبلغ.setText(f"إجمالي المبلغ: {إجمالي_مبلغ:,.2f}")

        self.إجمالي_مبلغ_الفواتير = إجمالي_مبلغ

    def عند_تغيير_نوع_السداد(self):
        """
        دالة تنفذ عند تغيير نوع السداد
        """
        نوع_السداد = self.قائمة_نوع_سداد.currentText()

        if نوع_السداد == "أقساط مصرفية":
            self.قائمة_بنك.setEnabled(True)
        else:
            self.قائمة_بنك.setEnabled(False)
            self.قائمة_بنك.setCurrentIndex(0)

    def استخدام_مبلغ_الفواتير(self):
        """
        دالة استخدام مبلغ الفواتير كمبلغ العقد
        """
        if self.إجمالي_مبلغ_الفواتير > 0:
            self.حقل_مبلغ_عقد.setText(str(self.إجمالي_مبلغ_الفواتير))
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فواتير أولاً")

    def تغيير_طريقة_الحساب(self):
        """
        دالة تغيير طريقة حساب الأقساط
        """
        if self.راديو_عدد_أقساط.isChecked():
            self.حقل_عدد_أقساط.setEnabled(True)
            self.حقل_مبلغ_قسط.setEnabled(False)
            self.حقل_مبلغ_قسط.clear()
        else:
            self.حقل_عدد_أقساط.setEnabled(False)
            self.حقل_مبلغ_قسط.setEnabled(True)
            self.حقل_عدد_أقساط.clear()

        self.حساب_تفاصيل_الأقساط()

    def حساب_تفاصيل_الأقساط(self):
        """
        دالة حساب تفاصيل الأقساط
        """
        try:
            مبلغ_عقد_نص = self.حقل_مبلغ_عقد.text().strip()
            if not مبلغ_عقد_نص:
                return

            مبلغ_عقد = Decimal(مبلغ_عقد_نص)

            if self.راديو_عدد_أقساط.isChecked():
                # حساب مبلغ القسط من عدد الأقساط
                عدد_أقساط_نص = self.حقل_عدد_أقساط.text().strip()
                if عدد_أقساط_نص:
                    عدد_أقساط = int(عدد_أقساط_نص)
                    if عدد_أقساط > 0:
                        مبلغ_قسط = مبلغ_عقد / عدد_أقساط
                        self.حقل_مبلغ_قسط.setText(f"{مبلغ_قسط:.2f}")
            else:
                # حساب عدد الأقساط من مبلغ القسط
                مبلغ_قسط_نص = self.حقل_مبلغ_قسط.text().strip()
                if مبلغ_قسط_نص:
                    مبلغ_قسط = Decimal(مبلغ_قسط_نص)
                    if مبلغ_قسط > 0:
                        عدد_أقساط = int(مبلغ_عقد / مبلغ_قسط)
                        self.حقل_عدد_أقساط.setText(str(عدد_أقساط))

            # حساب تاريخ الانتهاء
            self.حساب_تاريخ_الانتهاء()

            # تحديث ملخص العقد
            self.تحديث_ملخص_العقد()

        except (ValueError, ZeroDivisionError):
            pass  # تجاهل الأخطاء أثناء الكتابة

    def حساب_تاريخ_الانتهاء(self):
        """
        دالة حساب تاريخ انتهاء العقد
        """
        try:
            تاريخ_بداية = self.حقل_تاريخ_بداية.date().toPython()
            عدد_أقساط_نص = self.حقل_عدد_أقساط.text().strip()
            نوع_قسط = self.قائمة_نوع_قسط.currentText()

            if not عدد_أقساط_نص:
                self.تسمية_تاريخ_انتهاء.setText("تاريخ الانتهاء: غير محدد")
                return

            عدد_أقساط = int(عدد_أقساط_نص)

            # حساب الفترة بناءً على نوع القسط
            if نوع_قسط == "شهري":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(days=30 * عدد_أقساط)
            elif نوع_قسط == "أسبوعي":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(weeks=عدد_أقساط)
            elif نوع_قسط == "ربع سنوي":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(days=90 * عدد_أقساط)
            elif نوع_قسط == "سنوي":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(days=365 * عدد_أقساط)

            self.تسمية_تاريخ_انتهاء.setText(f"تاريخ الانتهاء: {تاريخ_انتهاء.strftime('%Y-%m-%d')}")

        except (ValueError, OverflowError):
            self.تسمية_تاريخ_انتهاء.setText("تاريخ الانتهاء: خطأ في الحساب")

    def تحديث_ملخص_العقد(self):
        """
        دالة تحديث ملخص العقد
        """
        try:
            مبلغ_عقد = self.حقل_مبلغ_عقد.text().strip()
            عدد_أقساط = self.حقل_عدد_أقساط.text().strip()
            مبلغ_قسط = self.حقل_مبلغ_قسط.text().strip()
            نوع_قسط = self.قائمة_نوع_قسط.currentText()

            if not all([مبلغ_عقد, عدد_أقساط, مبلغ_قسط]):
                self.تسمية_ملخص_عقد.setText("أدخل تفاصيل العقد لعرض الملخص")
                return

            ملخص = f"""
ملخص العقد:
• مبلغ العقد: {مبلغ_عقد} ريال
• عدد الأقساط: {عدد_أقساط} قسط {نوع_قسط}
• مبلغ القسط: {مبلغ_قسط} ريال
• إجمالي المبلغ المستحق: {float(مبلغ_عقد):,.2f} ريال
            """.strip()

            self.تسمية_ملخص_عقد.setText(ملخص)
            self.تسمية_ملخص_عقد.setStyleSheet("color: #2c3e50; font-weight: bold;")

        except Exception:
            self.تسمية_ملخص_عقد.setText("خطأ في حساب الملخص")

    def معاينة_العقد(self):
        """
        دالة معاينة العقد قبل الإنشاء
        """
        # التحقق من البيانات
        أخطاء = self.التحقق_من_البيانات()
        if أخطاء:
            QMessageBox.warning(self, "بيانات ناقصة", "\n".join(أخطاء))
            return

        # إنشاء نافذة المعاينة
        معاينة = f"""
=== معاينة العقد ===

رقم العقد: {self.حقل_رقم_عقد.text()}
العميل: {self.قائمة_عملاء.currentText()}
نوع السداد: {self.قائمة_نوع_سداد.currentText()}
البنك: {self.قائمة_بنك.currentText() if self.قائمة_بنك.isEnabled() else 'غير محدد'}

تفاصيل العقد:
• مبلغ العقد: {self.حقل_مبلغ_عقد.text()} ريال
• نوع القسط: {self.قائمة_نوع_قسط.currentText()}
• عدد الأقساط: {self.حقل_عدد_أقساط.text()}
• مبلغ القسط: {self.حقل_مبلغ_قسط.text()} ريال
• تاريخ البداية: {self.حقل_تاريخ_بداية.date().toString('yyyy-MM-dd')}
• {self.تسمية_تاريخ_انتهاء.text()}

الفواتير المربوطة: {self.تسمية_عدد_فواتير.text()}
{self.تسمية_إجمالي_مبلغ.text()}
        """

        QMessageBox.information(self, "معاينة العقد", معاينة)

    def التحقق_من_البيانات(self):
        """
        دالة التحقق من صحة البيانات المدخلة
        """
        أخطاء = []

        # التحقق من رقم العقد
        if not self.حقل_رقم_عقد.text().strip():
            أخطاء.append("• يرجى إدخال رقم العقد")

        # التحقق من العميل
        if not self.العميل_المحدد:
            أخطاء.append("• يرجى اختيار عميل")

        # التحقق من مبلغ العقد
        try:
            مبلغ_عقد = Decimal(self.حقل_مبلغ_عقد.text().strip())
            if مبلغ_عقد <= 0:
                أخطاء.append("• مبلغ العقد يجب أن يكون أكبر من صفر")
        except (ValueError, InvalidOperation):
            أخطاء.append("• يرجى إدخال مبلغ عقد صحيح")

        # التحقق من عدد الأقساط
        try:
            عدد_أقساط = int(self.حقل_عدد_أقساط.text().strip())
            if عدد_أقساط <= 0:
                أخطاء.append("• عدد الأقساط يجب أن يكون أكبر من صفر")
        except ValueError:
            أخطاء.append("• يرجى إدخال عدد أقساط صحيح")

        # التحقق من مبلغ القسط
        try:
            مبلغ_قسط = Decimal(self.حقل_مبلغ_قسط.text().strip())
            if مبلغ_قسط <= 0:
                أخطاء.append("• مبلغ القسط يجب أن يكون أكبر من صفر")
        except (ValueError, InvalidOperation):
            أخطاء.append("• يرجى إدخال مبلغ قسط صحيح")

        # التحقق من البنك (إذا كان السداد مصرفي)
        if self.قائمة_نوع_سداد.currentText() == "أقساط مصرفية":
            if self.قائمة_بنك.currentIndex() == 0:
                أخطاء.append("• يرجى اختيار بنك للأقساط المصرفية")

        # التحقق من الفواتير المحددة
        فواتير_محددة = self.الحصول_على_الفواتير_المحددة()
        if not فواتير_محددة:
            أخطاء.append("• يرجى تحديد فاتورة واحدة على الأقل")

        return أخطاء

    def الحصول_على_الفواتير_المحددة(self):
        """
        دالة الحصول على قائمة الفواتير المحددة
        """
        فواتير_محددة = []

        for صف in range(self.جدول_فواتير_متاحة.rowCount()):
            مربع_اختيار = self.جدول_فواتير_متاحة.cellWidget(صف, 0)
            if مربع_اختيار and مربع_اختيار.isChecked():
                رقم_فاتورة_عنصر = self.جدول_فواتير_متاحة.item(صف, 1)
                مبلغ_متبقي_عنصر = self.جدول_فواتير_متاحة.item(صف, 4)

                if رقم_فاتورة_عنصر and مبلغ_متبقي_عنصر:
                    رقم_فاتورة = int(رقم_فاتورة_عنصر.text())
                    مبلغ_متبقي = Decimal(مبلغ_متبقي_عنصر.text().replace(',', ''))

                    فواتير_محددة.append({
                        'رقم_الفاتورة': رقم_فاتورة,
                        'مبلغ_الفاتورة_في_العقد': مبلغ_متبقي
                    })

        return فواتير_محددة

    def إنشاء_العقد(self):
        """
        دالة إنشاء العقد الجديد
        """
        # التحقق من البيانات
        أخطاء = self.التحقق_من_البيانات()
        if أخطاء:
            QMessageBox.warning(self, "بيانات ناقصة", "\n".join(أخطاء))
            return

        # التأكيد من المستخدم
        رد = QMessageBox.question(
            self, "تأكيد إنشاء العقد",
            f"هل أنت متأكد من إنشاء العقد رقم {self.حقل_رقم_عقد.text()}؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if رد != QMessageBox.Yes:
            return

        try:
            # بدء المعاملة (إذا لم تكن بدأت بالفعل)
            try:
                self.قاعدة_البيانات.connection.start_transaction()
            except Exception:
                # إذا كانت المعاملة بدأت بالفعل، نتجاهل الخطأ
                pass

            # إنشاء العقد
            رقم_عقد = self.حقل_رقم_عقد.text().strip()
            رقم_عميل = self.العميل_المحدد
            نوع_سداد = self.قائمة_نوع_سداد.currentText()

            # الحصول على رقم البنك
            رقم_بنك = None
            if نوع_سداد == "أقساط مصرفية":
                فهرس_بنك = self.قائمة_بنك.currentIndex()
                if فهرس_بنك > 0:
                    رقم_بنك = self.قائمة_بنك.itemData(فهرس_بنك)

            # الحصول على رقم الموظف
            رقم_موظف = None
            فهرس_موظف = self.قائمة_موظف.currentIndex()
            if فهرس_موظف > 0:
                رقم_موظف = self.قائمة_موظف.itemData(فهرس_موظف)

            # بيانات العقد
            مبلغ_عقد = Decimal(self.حقل_مبلغ_عقد.text().strip())
            مبلغ_قسط = Decimal(self.حقل_مبلغ_قسط.text().strip())
            عدد_أقساط = int(self.حقل_عدد_أقساط.text().strip())
            نوع_قسط = self.قائمة_نوع_قسط.currentText()
            تاريخ_بداية = self.حقل_تاريخ_بداية.date().toPython()

            # حساب تاريخ الانتهاء
            if نوع_قسط == "شهري":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(days=30 * عدد_أقساط)
            elif نوع_قسط == "أسبوعي":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(weeks=عدد_أقساط)
            elif نوع_قسط == "ربع سنوي":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(days=90 * عدد_أقساط)
            elif نوع_قسط == "سنوي":
                تاريخ_انتهاء = تاريخ_بداية + timedelta(days=365 * عدد_أقساط)

            # إدراج العقد
            استعلام_عقد = """
            INSERT INTO العقود (
                رقم_العقد, رقم_العميل, رقم_البنك, نوع_السداد,
                مبلغ_العقد, مبلغ_القسط, عدد_الأقساط, نوع_القسط,
                تاريخ_بداية_العقد, تاريخ_انتهاء_العقد, رقم_الموظف
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            self.قاعدة_البيانات.تنفيذ_استعلام_بدون_commit(استعلام_عقد, (
                رقم_عقد, رقم_عميل, رقم_بنك, نوع_سداد,
                مبلغ_عقد, مبلغ_قسط, عدد_أقساط, نوع_قسط,
                تاريخ_بداية, تاريخ_انتهاء, رقم_موظف
            ))

            # ربط الفواتير بالعقد
            فواتير_محددة = self.الحصول_على_الفواتير_المحددة()
            for فاتورة in فواتير_محددة:
                استعلام_ربط = """
                INSERT INTO فواتير_العقود (
                    رقم_العقد, رقم_الفاتورة, مبلغ_الفاتورة_في_العقد
                ) VALUES (%s, %s, %s)
                """

                self.قاعدة_البيانات.تنفيذ_استعلام_بدون_commit(استعلام_ربط, (
                    رقم_عقد, فاتورة['رقم_الفاتورة'], فاتورة['مبلغ_الفاتورة_في_العقد']
                ))

            # إنشاء الأقساط
            self.إنشاء_أقساط_العقد(رقم_عقد, عدد_أقساط, مبلغ_قسط, نوع_قسط, تاريخ_بداية)

            # تأكيد المعاملة
            self.قاعدة_البيانات.connection.commit()

            QMessageBox.information(self, "نجح", f"تم إنشاء العقد رقم {رقم_عقد} بنجاح")
            self.accept()

        except Exception as e:
            # إلغاء المعاملة في حالة الخطأ
            self.قاعدة_البيانات.connection.rollback()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء العقد: {str(e)}")

    def إنشاء_أقساط_العقد(self, رقم_عقد, عدد_أقساط, مبلغ_قسط, نوع_قسط, تاريخ_بداية):
        """
        دالة إنشاء أقساط العقد
        """
        for رقم_قسط in range(1, عدد_أقساط + 1):
            # حساب تاريخ استحقاق القسط
            if نوع_قسط == "شهري":
                تاريخ_استحقاق = تاريخ_بداية + timedelta(days=30 * رقم_قسط)
            elif نوع_قسط == "أسبوعي":
                تاريخ_استحقاق = تاريخ_بداية + timedelta(weeks=رقم_قسط)
            elif نوع_قسط == "ربع سنوي":
                تاريخ_استحقاق = تاريخ_بداية + timedelta(days=90 * رقم_قسط)
            elif نوع_قسط == "سنوي":
                تاريخ_استحقاق = تاريخ_بداية + timedelta(days=365 * رقم_قسط)

            # إدراج القسط
            استعلام_قسط = """
            INSERT INTO الأقساط (
                رقم_العقد, رقم_القسط_في_العقد, مبلغ_القسط, تاريخ_الاستحقاق
            ) VALUES (%s, %s, %s, %s)
            """

            self.قاعدة_البيانات.تنفيذ_استعلام_بدون_commit(استعلام_قسط, (
                رقم_عقد, رقم_قسط, مبلغ_قسط, تاريخ_استحقاق
            ))

    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS للحوار
        """
        نمط = """
        QLabel#dialog_title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #8e44ad;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #8e44ad;
        }

        QPushButton {
            background-color: #8e44ad;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            font-weight: bold;
            min-width: 100px;
        }

        QPushButton:hover {
            background-color: #9b59b6;
        }

        QPushButton:pressed {
            background-color: #7d3c98;
        }

        QPushButton#create_button {
            background-color: #27ae60;
            min-width: 120px;
        }

        QPushButton#create_button:hover {
            background-color: #2ecc71;
        }

        QLineEdit, QComboBox, QDateEdit {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 12px;
        }

        QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
            border-color: #8e44ad;
        }

        QTableWidget#available_invoices_table {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
            gridline-color: #ecf0f1;
        }

        QTableWidget#available_invoices_table::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }

        QTableWidget#available_invoices_table::item:selected {
            background-color: #8e44ad;
            color: white;
        }

        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #ecf0f1;
            color: #2c3e50;
            padding: 8px 15px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-weight: bold;
        }

        QTabBar::tab:selected {
            background-color: #8e44ad;
            color: white;
        }

        QTabBar::tab:hover {
            background-color: #9b59b6;
            color: white;
        }
        """

        self.setStyleSheet(نمط)
