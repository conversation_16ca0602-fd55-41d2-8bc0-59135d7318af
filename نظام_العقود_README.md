# نظام العقود الشامل - نظام إدارة المبيعات والأقساط

## 🎯 نظرة عامة

تم تطوير نظام عقود شامل ومتطور يلبي جميع المتطلبات المطلوبة، بما في ذلك:

- ✅ **رقم عقد تلقائي أو يدوي** مع منع التكرار
- ✅ **ربط العقد بالعميل** مع تحميل فواتيره
- ✅ **ربط فاتورة واحدة أو متعددة** بالعقد
- ✅ **حساب المبالغ تلقائياً** من الفواتير المحددة
- ✅ **أنواع أقساط متنوعة** (شهري، أسبوعي، ربع سنوي، سنوي)
- ✅ **ربط العقد بالبنك** للسداد النقدي والمصرفي
- ✅ **تحميل بيانات البنك** من جدول العميل تلقائياً

## 🏗️ هيكل النظام

### 1. قاعدة البيانات المحدثة

#### جدول العقود الجديد
```sql
CREATE TABLE العقود (
    رقم_العقد VARCHAR(50) PRIMARY KEY,           -- رقم فريد بصيغة CON-YYYY-NNNN
    رقم_العميل INT NOT NULL,                    -- ربط بالعميل
    رقم_البنك INT NULL,                         -- ربط بالبنك (اختياري)
    نوع_السداد ENUM('نقدي', 'أقساط مصرفية', 'أقساط نقدية'),
    مبلغ_العقد DECIMAL(15,2) NOT NULL,
    مبلغ_القسط DECIMAL(15,2) NOT NULL,
    عدد_الأقساط INT NOT NULL,
    نوع_القسط ENUM('شهري', 'أسبوعي', 'ربع سنوي', 'سنوي'),
    تاريخ_بداية_العقد DATE NOT NULL,
    تاريخ_انتهاء_العقد DATE NOT NULL,
    حالة_العقد ENUM('نشط', 'مكتمل', 'ملغي', 'معلق'),
    رقم_الموظف INT NULL,
    ملاحظات TEXT,
    شروط_إضافية TEXT
);
```

#### جدول ربط الفواتير بالعقود (جديد)
```sql
CREATE TABLE فواتير_العقود (
    رقم_الربط INT AUTO_INCREMENT PRIMARY KEY,
    رقم_العقد VARCHAR(50) NOT NULL,
    رقم_الفاتورة INT NOT NULL,
    مبلغ_الفاتورة_في_العقد DECIMAL(15,2) NOT NULL,
    نسبة_الفاتورة_من_العقد DECIMAL(5,2) DEFAULT 100.00
);
```

#### جدول الأقساط المحدث
```sql
CREATE TABLE الأقساط (
    رقم_القسط INT AUTO_INCREMENT PRIMARY KEY,
    رقم_العقد VARCHAR(50) NOT NULL,
    رقم_القسط_في_العقد INT NOT NULL,
    مبلغ_القسط DECIMAL(15,2) NOT NULL,
    تاريخ_الاستحقاق DATE NOT NULL,
    تاريخ_السداد DATE NULL,
    المبلغ_المدفوع DECIMAL(15,2) DEFAULT 0.00,
    طريقة_السداد ENUM('نقدي', 'تحويل بنكي', 'شيك', 'بطاقة ائتمان'),
    رقم_المرجع VARCHAR(100) NULL,
    حالة_السداد ENUM('مسدد', 'متأخر', 'غير مسدد', 'مسدد جزئياً'),
    رقم_الموظف_المحصل INT NULL,
    ملاحظات TEXT
);
```

### 2. حوار إنشاء العقد الشامل

#### التبويب الأول: معلومات العقد
- **رقم العقد:**
  - اقتراح تلقائي بصيغة CON-2024-0001
  - إمكانية الإدخال اليدوي
  - التحقق من عدم التكرار مع تلوين تفاعلي

- **اختيار العميل:**
  - قائمة جميع العملاء النشطين
  - بحث متقدم بالاسم أو الهاتف
  - عرض معلومات العميل المحدد
  - ربط تلقائي بالبنك إذا كان للعميل حساب

- **نوع السداد:**
  - نقدي، أقساط مصرفية، أقساط نقدية
  - تفعيل/تعطيل اختيار البنك حسب النوع

#### التبويب الثاني: ربط الفواتير
- **جدول الفواتير المتاحة:**
  - عرض فواتير العميل غير المسددة
  - مربعات اختيار لتحديد الفواتير
  - تلوين حسب حالة السداد

- **ملخص الفواتير:**
  - عدد الفواتير المحددة
  - إجمالي المبلغ
  - تحديث تلقائي

#### التبويب الثالث: تفاصيل الأقساط
- **مبلغ العقد:**
  - إدخال يدوي أو استخدام مبلغ الفواتير
  - زر لاستخدام إجمالي الفواتير المحددة

- **أنواع الأقساط:**
  - شهري (كل 30 يوم)
  - أسبوعي (كل 7 أيام)
  - ربع سنوي (كل 90 يوم)
  - سنوي (كل 365 يوم)

- **طرق الحساب:**
  - تحديد عدد الأقساط → حساب مبلغ القسط
  - تحديد مبلغ القسط → حساب عدد الأقساط

- **التواريخ:**
  - تاريخ بداية قابل للتعديل
  - حساب تاريخ الانتهاء تلقائياً

## 🚀 كيفية الاستخدام

### 1. الوصول لحوار إنشاء العقد
```bash
# تشغيل النظام الرئيسي
python main.py

# اختر "العقود" من القائمة الجانبية
# اضغط زر "إنشاء عقد"
```

### 2. اختبار الحوار منفرداً
```bash
python اختبار_حوار_إنشاء_العقد.py
```

### 3. اختبار شامل لجميع الواجهات
```bash
python اختبار_الواجهات_الجديدة.py
```

## 📋 خطوات إنشاء العقد

### الخطوة 1: معلومات العقد
1. **رقم العقد:** اضغط "اقتراح رقم تلقائي" أو أدخل رقماً يدوياً
2. **العميل:** ابحث واختر العميل من القائمة
3. **نوع السداد:** اختر نوع السداد المناسب
4. **البنك:** سيتم تحديده تلقائياً أو اختر بنكاً للسداد المصرفي
5. **الموظف:** اختر الموظف المسؤول

### الخطوة 2: ربط الفواتير
1. **تحديد الفواتير:** ستظهر فواتير العميل غير المسددة
2. **اختيار الفواتير:** حدد الفواتير المراد ربطها بالعقد
3. **مراجعة الملخص:** تأكد من إجمالي المبلغ

### الخطوة 3: تفاصيل الأقساط
1. **مبلغ العقد:** أدخل المبلغ أو استخدم مبلغ الفواتير
2. **نوع القسط:** اختر الفترة الزمنية للأقساط
3. **الحساب:** حدد عدد الأقساط أو مبلغ القسط
4. **التواريخ:** حدد تاريخ البداية
5. **المراجعة:** راجع ملخص العقد

### الخطوة 4: الإنشاء
1. **معاينة:** اضغط "معاينة العقد" لمراجعة التفاصيل
2. **إنشاء:** اضغط "إنشاء العقد" لحفظ العقد
3. **تأكيد:** ستظهر رسالة تأكيد النجاح

## 🔧 المميزات التقنية

### أمان البيانات
- **معاملات قاعدة البيانات:** استخدام Transactions لضمان سلامة البيانات
- **التحقق من البيانات:** فحص شامل لجميع المدخلات
- **منع التكرار:** التحقق من عدم تكرار أرقام العقود

### الأداء
- **استعلامات محسنة:** استعلامات SQL محسنة مع Indices
- **تحميل ذكي:** تحميل البيانات عند الحاجة فقط
- **ذاكرة التخزين:** حفظ البيانات المحملة لتجنب الاستعلامات المتكررة

### واجهة المستخدم
- **تصميم متجاوب:** يتكيف مع أحجام الشاشات المختلفة
- **دعم RTL كامل:** تخطيط من اليمين لليسار
- **تلوين تفاعلي:** ألوان مختلفة للحالات المختلفة
- **رسائل واضحة:** رسائل خطأ ونجاح مفصلة

## 📁 الملفات المضافة/المحدثة

```
├── db.py                              # قاعدة البيانات (محدث)
│   ├── جدول العقود المحدث
│   ├── جدول فواتير_العقود الجديد
│   ├── جدول الأقساط المحدث
│   └── وظائف إنشاء الأرقام والتحقق
│
├── contract_creation_dialog.py        # حوار إنشاء العقد (جديد)
│   ├── 3 تبويبات رئيسية
│   ├── 1100+ سطر من الكود
│   └── جميع الوظائف المطلوبة
│
├── other_interfaces.py               # واجهة العقود (محدث)
│   └── ربط زر إنشاء العقد بالحوار الجديد
│
├── اختبار_حوار_إنشاء_العقد.py         # ملف اختبار مخصص (جديد)
├── اختبار_الواجهات_الجديدة.py         # ملف اختبار شامل (محدث)
├── تطوير_نظام_العقود_الشامل.md       # توثيق تفصيلي (جديد)
└── نظام_العقود_README.md            # هذا الملف (جديد)
```

## ✅ المتطلبات المكتملة

- ✅ **رقم عقد تلقائي أو يدوي** مع منع التكرار
- ✅ **ربط العقد بالعميل** مع تحميل فواتيره
- ✅ **ربط فاتورة واحدة أو متعددة** بالعقد
- ✅ **تحديد المبلغ الإجمالي** من الفواتير أو يدوياً
- ✅ **مبلغ القسط وعدد الشهور** مع حساب تلقائي
- ✅ **أنواع أقساط متنوعة** (شهري، أسبوعي، ربع سنوي، سنوي)
- ✅ **ربط العقد بين الشركة والعميل والبنك**
- ✅ **السداد النقدي أو أقساط مصرفية**
- ✅ **تحميل بيانات البنك من جدول العميل**
- ✅ **تحديث قاعدة البيانات** بالجداول والحقول المطلوبة

## 🎉 النتيجة النهائية

تم تطوير نظام عقود شامل ومتكامل يلبي جميع المتطلبات المطلوبة بأعلى معايير الجودة والأمان، مع واجهة مستخدم حديثة ومتجاوبة تدعم اللغة العربية بالكامل.

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025-01-10  
**الإصدار:** 1.0.0
