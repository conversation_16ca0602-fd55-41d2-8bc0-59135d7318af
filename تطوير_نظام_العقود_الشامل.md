# تطوير نظام العقود الشامل - نظام إدارة المبيعات والأقساط

## 📋 ملخص المشروع

تم تطوير نظام عقود شامل ومتطور يتضمن حوار إنشاء عقد متقدم مع جميع الوظائف المطلوبة، بما في ذلك اقتراح رقم تلقائي، ربط الفواتير المتعددة، أنواع الأقساط المختلفة، والربط البنكي.

## 🎯 المكونات المطورة

### 1. تحديث قاعدة البيانات (`db.py`)

**التحديثات المضافة:**

#### جدول العقود المحدث
```sql
CREATE TABLE العقود (
    رقم_العقد VARCHAR(50) PRIMARY KEY,           -- رق<PERSON> العقد الفريد
    رقم_العميل INT NOT NULL,                    -- ربط بالعميل
    رقم_البنك INT NULL,                         -- ربط بالبنك (اختياري)
    نوع_السداد ENUM('نقدي', 'أقساط مصرفية', 'أقساط نقدية'),
    مبلغ_العقد DECIMAL(15,2) NOT NULL,          -- مبلغ العقد الإجمالي
    مبلغ_القسط DECIMAL(15,2) NOT NULL,          -- مبلغ القسط الواحد
    عدد_الأقساط INT NOT NULL,                   -- عدد الأقساط
    نوع_القسط ENUM('شهري', 'أسبوعي', 'ربع سنوي', 'سنوي'),
    تاريخ_بداية_العقد DATE NOT NULL,            -- تاريخ بداية العقد
    تاريخ_انتهاء_العقد DATE NOT NULL,           -- تاريخ انتهاء العقد
    حالة_العقد ENUM('نشط', 'مكتمل', 'ملغي', 'معلق'),
    رقم_الموظف INT NULL,                        -- الموظف المسؤول
    ملاحظات TEXT,                              -- ملاحظات إضافية
    شروط_إضافية TEXT                           -- شروط خاصة
);
```

#### جدول ربط الفواتير بالعقود (جديد)
```sql
CREATE TABLE فواتير_العقود (
    رقم_الربط INT AUTO_INCREMENT PRIMARY KEY,
    رقم_العقد VARCHAR(50) NOT NULL,             -- ربط بالعقد
    رقم_الفاتورة INT NOT NULL,                  -- ربط بالفاتورة
    مبلغ_الفاتورة_في_العقد DECIMAL(15,2),       -- مبلغ الفاتورة في العقد
    نسبة_الفاتورة_من_العقد DECIMAL(5,2),        -- نسبة الفاتورة من العقد
    تاريخ_الربط TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول الأقساط المحدث
```sql
CREATE TABLE الأقساط (
    رقم_القسط INT AUTO_INCREMENT PRIMARY KEY,
    رقم_العقد VARCHAR(50) NOT NULL,             -- ربط بالعقد
    رقم_القسط_في_العقد INT NOT NULL,            -- رقم القسط في العقد
    مبلغ_القسط DECIMAL(15,2) NOT NULL,          -- مبلغ القسط
    تاريخ_الاستحقاق DATE NOT NULL,              -- تاريخ استحقاق القسط
    تاريخ_السداد DATE NULL,                     -- تاريخ السداد الفعلي
    المبلغ_المدفوع DECIMAL(15,2) DEFAULT 0.00,  -- المبلغ المدفوع
    طريقة_السداد ENUM('نقدي', 'تحويل بنكي', 'شيك', 'بطاقة ائتمان'),
    رقم_المرجع VARCHAR(100) NULL,               -- رقم المرجع للسداد
    حالة_السداد ENUM('مسدد', 'متأخر', 'غير مسدد', 'مسدد جزئياً'),
    رقم_الموظف_المحصل INT NULL,                 -- الموظف المحصل
    ملاحظات TEXT
);
```

**الوظائف الجديدة:**
- `إنشاء_رقم_عقد_تلقائي()` - إنشاء رقم عقد فريد بصيغة CON-YYYY-NNNN
- `التحقق_من_رقم_العقد()` - التحقق من عدم تكرار رقم العقد

### 2. حوار إنشاء العقد الشامل (`contract_creation_dialog.py`)

**الملف:** `contract_creation_dialog.py` (ملف جديد - 1100+ سطر)

#### التبويبات الرئيسية:

##### تبويب معلومات العقد
**المميزات:**
- **رقم العقد:**
  - اقتراح رقم تلقائي بصيغة CON-2024-0001
  - إمكانية الإدخال اليدوي
  - التحقق من عدم التكرار
  - تلوين تفاعلي (أخضر للمتاح، أحمر للمكرر)

- **اختيار العميل:**
  - قائمة منسدلة لجميع العملاء النشطين
  - بحث متقدم بالاسم أو رقم الهاتف
  - عرض معلومات العميل المحدد
  - ربط تلقائي بالبنك إذا كان للعميل حساب بنكي

- **نوع السداد والبنك:**
  - نقدي، أقساط مصرفية، أقساط نقدية
  - تفعيل/تعطيل اختيار البنك حسب نوع السداد
  - قائمة البنوك النشطة مع أرقام الحسابات

- **الموظف المسؤول:**
  - قائمة الموظفين النشطين
  - عرض المنصب مع الاسم

##### تبويب ربط الفواتير
**المميزات:**
- **جدول الفواتير المتاحة:**
  - عرض فواتير العميل غير المسددة أو الجزئية
  - مربعات اختيار لتحديد الفواتير
  - تلوين حسب حالة السداد
  - عرض المبالغ النهائية والمتبقية

- **ملخص الفواتير المحددة:**
  - عدد الفواتير المحددة
  - إجمالي المبلغ المحدد
  - تحديث تلقائي عند التغيير

##### تبويب تفاصيل الأقساط
**المميزات:**
- **مبلغ العقد:**
  - إدخال يدوي أو استخدام مبلغ الفواتير
  - زر لاستخدام إجمالي الفواتير المحددة

- **أنواع الأقساط المدعومة:**
  - شهري (كل 30 يوم)
  - أسبوعي (كل 7 أيام)
  - ربع سنوي (كل 90 يوم)
  - سنوي (كل 365 يوم)

- **طرق الحساب:**
  - تحديد عدد الأقساط → حساب مبلغ القسط
  - تحديد مبلغ القسط → حساب عدد الأقساط

- **التواريخ:**
  - تاريخ بداية العقد (قابل للتعديل)
  - حساب تاريخ الانتهاء تلقائياً
  - عرض ملخص العقد المحدث

#### الوظائف المتقدمة:

**التحقق من البيانات:**
- التحقق من صحة جميع الحقول المطلوبة
- التحقق من صحة المبالغ والأرقام
- التحقق من تحديد الفواتير
- التحقق من اختيار البنك للسداد المصرفي

**معاينة العقد:**
- عرض ملخص شامل للعقد قبل الإنشاء
- جميع التفاصيل والمبالغ والتواريخ
- الفواتير المربوطة

**إنشاء العقد:**
- إنشاء العقد في جدول العقود
- ربط الفواتير في جدول فواتير_العقود
- إنشاء جميع الأقساط تلقائياً مع تواريخ الاستحقاق
- استخدام المعاملات (Transactions) لضمان سلامة البيانات

### 3. تحديث واجهة العقود (`other_interfaces.py`)

**التحديثات:**
- ربط زر "إنشاء عقد" بالحوار الجديد
- تحديث البيانات تلقائياً بعد إنشاء العقد
- معالجة الأخطاء المحسنة

## 🎨 التصميم والأنماط

### نمط التصميم الموحد
- **تبويبات منظمة:** 3 تبويبات رئيسية لتنظيم المعلومات
- **مجموعات منطقية:** تجميع الحقول ذات الصلة
- **تلوين تفاعلي:** ألوان مختلفة للحالات المختلفة
- **رسائل واضحة:** رسائل خطأ ونجاح مفصلة

### الألوان والتنسيق
- **اللون الأساسي:** بنفسجي (#8e44ad) للعقود
- **تلوين الحالات:**
  - أخضر: متاح/صحيح
  - أحمر: خطأ/مكرر
  - أصفر: تحذير/جزئي
- **أزرار متدرجة:** ألوان مختلفة حسب الوظيفة

### دعم RTL كامل
- **تخطيط من اليمين لليسار**
- **خطوط عربية واضحة**
- **أزرار وقوائم مناسبة للعربية**
- **تبويبات مرتبة من اليمين**

## 🔧 التكامل مع النظام

### قاعدة البيانات
- **جداول محدثة:** تحديث جداول العقود والأقساط
- **جدول جديد:** فواتير_العقود لربط الفواتير
- **علاقات محكمة:** Foreign Keys وIndices محسنة
- **وظائف مساعدة:** دوال إنشاء الأرقام والتحقق

### الواجهات الموجودة
- **واجهة العقود:** ربط الزر بالحوار الجديد
- **تحديث تلقائي:** تحديث البيانات بعد الإنشاء
- **معالجة أخطاء:** رسائل خطأ واضحة

## 📁 هيكل الملفات

```
├── db.py                              # قاعدة البيانات (محدث)
├── contract_creation_dialog.py        # حوار إنشاء العقد (جديد)
├── other_interfaces.py               # واجهة العقود (محدث)
├── اختبار_حوار_إنشاء_العقد.py         # ملف اختبار مخصص (جديد)
└── تطوير_نظام_العقود_الشامل.md       # هذا الملف (جديد)
```

## 🚀 كيفية التشغيل

### التشغيل العادي
```bash
python main.py
# ثم اختر "العقود" من القائمة الجانبية
# اضغط "إنشاء عقد" لفتح الحوار الجديد
```

### اختبار حوار إنشاء العقد فقط
```bash
python اختبار_حوار_إنشاء_العقد.py
```

## ✅ المميزات المكتملة

- ✅ رقم عقد تلقائي فريد مع التحقق من عدم التكرار
- ✅ اختيار العميل مع البحث المتقدم
- ✅ ربط فاتورة واحدة أو متعددة بالعقد
- ✅ حساب المبالغ تلقائياً من الفواتير المحددة
- ✅ دعم أنواع الأقساط المختلفة (شهري، أسبوعي، ربع سنوي، سنوي)
- ✅ ربط العقد بالبنك للسداد النقدي والأقساط المصرفية
- ✅ حساب تواريخ الاستحقاق تلقائياً
- ✅ إنشاء الأقساط تلقائياً عند إنشاء العقد
- ✅ معاينة العقد قبل الإنشاء
- ✅ تصميم متجاوب مع دعم RTL كامل
- ✅ معاملات قاعدة البيانات لضمان سلامة البيانات
- ✅ تكامل كامل مع النظام الموجود

## 🔮 التطوير المستقبلي

- إضافة تعديل العقود الموجودة
- تطوير تقارير العقود المفصلة
- إضافة إشعارات الأقساط المستحقة
- تطوير نظام الغرامات للتأخير
- إضافة طباعة العقود بتنسيق احترافي
- تطوير لوحة تحكم للعقود

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025-01-10  
**الإصدار:** 1.0.0
