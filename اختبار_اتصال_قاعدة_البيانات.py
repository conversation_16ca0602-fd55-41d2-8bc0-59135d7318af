# -*- coding: utf-8 -*-
"""
اختبار اتصال قاعدة البيانات وإنشاء رقم عقد
"""

import sys
from db import قاعدة_البيانات, تهيئة_قاعدة_البيانات

def اختبار_الاتصال():
    """
    اختبار الاتصال بقاعدة البيانات
    """
    print("=== اختبار اتصال قاعدة البيانات ===")
    
    try:
        # تهيئة قاعدة البيانات
        print("1. تهيئة قاعدة البيانات...")
        تهيئة_قاعدة_البيانات()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # إنشاء اتصال جديد
        print("\n2. إنشاء اتصال جديد...")
        قاعدة = قاعدة_البيانات()
        if قاعدة.اتصال_قاعدة_البيانات():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        # استخدام قاعدة البيانات
        print("\n3. استخدام قاعدة البيانات...")
        قاعدة.cursor.execute(f"USE {قاعدة.database}")
        print("✅ تم استخدام قاعدة البيانات بنجاح")
        
        # اختبار إنشاء رقم عقد
        print("\n4. اختبار إنشاء رقم عقد...")
        رقم_عقد = قاعدة.إنشاء_رقم_عقد_تلقائي()
        print(f"✅ رقم العقد المقترح: {رقم_عقد}")
        
        # اختبار التحقق من رقم العقد
        print("\n5. اختبار التحقق من رقم العقد...")
        متاح = قاعدة.التحقق_من_رقم_العقد(رقم_عقد)
        print(f"✅ رقم العقد متاح: {متاح}")
        
        # اختبار استعلام بسيط
        print("\n6. اختبار استعلام بسيط...")
        استعلام = "SELECT COUNT(*) as عدد_العملاء FROM العملاء"
        نتيجة = قاعدة.تنفيذ_استعلام(استعلام)
        if نتيجة:
            print(f"✅ عدد العملاء في النظام: {نتيجة[0][0]}")
        else:
            print("❌ فشل في تنفيذ الاستعلام")
        
        # اختبار المعاملات
        print("\n7. اختبار المعاملات...")
        try:
            قاعدة.connection.start_transaction()
            print("✅ تم بدء المعاملة بنجاح")
            
            # اختبار استعلام بدون commit
            استعلام_اختبار = "SELECT 1"
            قاعدة.تنفيذ_استعلام_بدون_commit(استعلام_اختبار)
            print("✅ تم تنفيذ استعلام بدون commit")
            
            قاعدة.connection.commit()
            print("✅ تم تأكيد المعاملة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في المعاملات: {str(e)}")
            قاعدة.connection.rollback()
        
        # إغلاق الاتصال
        print("\n8. إغلاق الاتصال...")
        قاعدة.إغلاق_الاتصال()
        print("✅ تم إغلاق الاتصال بنجاح")
        
        print("\n✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        return False

def اختبار_حوار_العقد():
    """
    اختبار فتح حوار إنشاء العقد
    """
    print("\n=== اختبار حوار إنشاء العقد ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from contract_creation_dialog import حوار_إنشاء_عقد
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("1. إنشاء حوار إنشاء العقد...")
        حوار = حوار_إنشاء_عقد()
        print("✅ تم إنشاء الحوار بنجاح")
        
        print("2. عرض الحوار...")
        حوار.show()
        print("✅ تم عرض الحوار بنجاح")
        
        print("\n✅ حوار إنشاء العقد جاهز للاستخدام!")
        print("يمكنك الآن اختبار جميع الوظائف في الحوار")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        print("تأكد من وجود ملف contract_creation_dialog.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الحوار: {str(e)}")
        return False

def main():
    """
    دالة الاختبار الرئيسية
    """
    print("اختبار نظام العقود الشامل")
    print("=" * 50)
    
    # اختبار الاتصال أولاً
    if اختبار_الاتصال():
        print("\n" + "=" * 50)
        
        # سؤال المستخدم عن اختبار الحوار
        جواب = input("\nهل تريد اختبار حوار إنشاء العقد؟ (y/n): ")
        if جواب.lower() in ['y', 'yes', 'نعم', 'ن']:
            اختبار_حوار_العقد()
        else:
            print("\n✅ انتهى الاختبار بنجاح!")
    else:
        print("\n❌ فشل في اختبار الاتصال!")
        print("يرجى التحقق من إعدادات قاعدة البيانات")

if __name__ == "__main__":
    main()
