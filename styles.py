# -*- coding: utf-8 -*-
"""
ملف الأنماط والتصميم للتطبيق
يحتوي على جميع أنماط CSS المطلوبة للواجهات
"""

# الألوان الأساسية للتطبيق
COLORS = {
    'primary': '#2c3e50',
    'secondary': '#34495e', 
    'success': '#27ae60',
    'danger': '#e74c3c',
    'warning': '#f39c12',
    'info': '#3498db',
    'light': '#ecf0f1',
    'dark': '#2c3e50',
    'muted': '#7f8c8d',
    'white': '#ffffff',
    'purple': '#9b59b6',
    'orange': '#e67e22'
}

def get_main_stylesheet():
    """
    دالة الحصول على الأنماط الرئيسية للتطبيق
    """
    return f"""
    /* الأنماط العامة للتطبيق */
    QMainWindow {{
        background-color: {COLORS['light']};
        font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
        font-size: 12px;
        color: {COLORS['dark']};
    }}
    
    /* شريط القوائم العلوي */
    QMenuBar {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        border: none;
        padding: 5px;
        font-weight: bold;
    }}
    
    QMenuBar::item {{
        background-color: transparent;
        padding: 8px 15px;
        margin: 2px;
        border-radius: 4px;
    }}
    
    QMenuBar::item:selected {{
        background-color: {COLORS['secondary']};
    }}
    
    QMenuBar::item:pressed {{
        background-color: {COLORS['info']};
    }}
    
    /* القوائم المنسدلة */
    QMenu {{
        background-color: {COLORS['white']};
        border: 2px solid {COLORS['muted']};
        border-radius: 6px;
        padding: 5px;
    }}
    
    QMenu::item {{
        padding: 8px 20px;
        border-radius: 4px;
        margin: 1px;
    }}
    
    QMenu::item:selected {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
    }}
    
    QMenu::separator {{
        height: 1px;
        background-color: {COLORS['muted']};
        margin: 5px 10px;
    }}
    
    /* القائمة الجانبية */
    QFrame#sidebar {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                   stop:0 {COLORS['primary']}, 
                                   stop:1 {COLORS['secondary']});
        border-right: 3px solid {COLORS['info']};
    }}
    
    QPushButton#toggle_button {{
        background-color: {COLORS['secondary']};
        color: {COLORS['white']};
        border: 2px solid {COLORS['info']};
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        padding: 5px;
    }}
    
    QPushButton#toggle_button:hover {{
        background-color: {COLORS['info']};
        border-color: {COLORS['white']};
    }}
    
    QPushButton#sidebar_button {{
        background-color: rgba(52, 73, 94, 0.8);
        color: {COLORS['white']};
        border: 2px solid transparent;
        border-radius: 10px;
        margin: 3px;
        font-weight: bold;
    }}
    
    QPushButton#sidebar_button:hover {{
        background-color: {COLORS['success']};
        border-color: {COLORS['white']};
        
    }}
    
    QPushButton#sidebar_button:pressed {{
        background-color: {COLORS['info']};
    }}
    
    /* المحتوى الرئيسي */
    QFrame#main_content {{
        background-color: {COLORS['light']};
        border: none;
    }}
    
    /* شريط الفلاتر */
    QFrame#filters_bar {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['white']}, 
                                   stop:1 #f8f9fa);
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        padding: 5px;
    }}
    
    /* شريط الأزرار */
    QFrame#actions_bar {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['white']}, 
                                   stop:1 #f8f9fa);
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        padding: 5px;
    }}
    
    /* منطقة البيانات */
    QStackedWidget#data_area {{
        background-color: {COLORS['white']};
        border: 2px solid #bdc3c7;
        border-radius: 10px;
    }}
    
    /* حقول الإدخال */
    QLineEdit {{
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        padding: 10px;
        font-size: 13px;
        background-color: {COLORS['white']};
    }}
    
    QLineEdit:focus {{
        border-color: {COLORS['info']};
        background-color: #f0f8ff;
    }}
    
    QLineEdit:hover {{
        border-color: {COLORS['secondary']};
    }}
    
    /* القوائم المنسدلة */
    QComboBox {{
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 13px;
        background-color: {COLORS['white']};
        min-width: 100px;
    }}
    
    QComboBox:focus {{
        border-color: {COLORS['info']};
    }}
    
    QComboBox:hover {{
        border-color: {COLORS['secondary']};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 20px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {COLORS['secondary']};
    }}
    
    /* أزرار الإجراءات */
    QPushButton#action_button {{
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        background-color: {COLORS['white']};
        font-weight: bold;
        padding: 5px;
    }}
    
    QPushButton#action_button:hover {{
        
        
    }}
    
    /* الجداول */
    QTableWidget {{
        gridline-color: #dee2e6;
        background-color: {COLORS['white']};
        alternate-background-color: #f8f9fa;
        selection-background-color: {COLORS['info']};
        selection-color: {COLORS['white']};
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        font-size: 12px;
    }}
    
    QTableWidget::item {{
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
    }}
    
    QTableWidget::item:selected {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
    }}
    
    QTableWidget::item:hover {{
        background-color: #e3f2fd;
    }}
    
    QHeaderView::section {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['primary']}, 
                                   stop:1 {COLORS['secondary']});
        color: {COLORS['white']};
        padding: 12px;
        border: none;
        font-weight: bold;
        font-size: 13px;
    }}
    
    QHeaderView::section:hover {{
        background-color: {COLORS['info']};
    }}
    
    /* أشرطة التمرير */
    QScrollBar:vertical {{
        background-color: #f1f1f1;
        width: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COLORS['muted']};
        border-radius: 6px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {COLORS['secondary']};
    }}
    
    QScrollBar:horizontal {{
        background-color: #f1f1f1;
        height: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:horizontal {{
        background-color: {COLORS['muted']};
        border-radius: 6px;
        min-width: 20px;
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background-color: {COLORS['secondary']};
    }}
    
    /* التبويبات */
    QTabWidget::pane {{
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        background-color: {COLORS['white']};
    }}
    
    QTabBar::tab {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 #f0f0f0, 
                                   stop:1 #e0e0e0);
        color: {COLORS['dark']};
        padding: 10px 20px;
        margin: 2px;
        border-radius: 6px;
        font-weight: bold;
    }}
    
    QTabBar::tab:selected {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['info']}, 
                                   stop:1 {COLORS['primary']});
        color: {COLORS['white']};
    }}
    
    QTabBar::tab:hover {{
        background-color: {COLORS['warning']};
        color: {COLORS['white']};
    }}
    
    /* التسميات */
    QLabel {{
        color: {COLORS['dark']};
    }}
    
    QLabel#page_title {{
        font-size: 24px;
        font-weight: bold;
        color: {COLORS['primary']};
        padding: 15px;
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                   stop:0 {COLORS['light']}, 
                                   stop:1 {COLORS['white']});
        border-radius: 10px;
        border-left: 5px solid {COLORS['info']};
    }}
    
    /* مربعات الاختيار */
    QCheckBox {{
        font-size: 13px;
        color: {COLORS['dark']};
    }}
    
    QCheckBox::indicator {{
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        background-color: {COLORS['white']};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {COLORS['success']};
        border-color: {COLORS['success']};
    }}
    
    /* حقول النص المتعددة الأسطر */
    QTextEdit {{
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        padding: 10px;
        font-size: 13px;
        background-color: {COLORS['white']};
    }}
    
    QTextEdit:focus {{
        border-color: {COLORS['info']};
        background-color: #f0f8ff;
    }}
    """

def get_button_style(color, hover_color=None):
    """
    دالة إنشاء نمط زر مخصص
    """
    if not hover_color:
        hover_color = color
    
    return f"""
    QPushButton {{
        background-color: {color};
        color: {COLORS['white']};
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: bold;
        font-size: 13px;
    }}
    
    QPushButton:hover {{
        background-color: {hover_color};
        
    }}
    
    QPushButton:pressed {{
        background-color: {hover_color};
        
    }}
    
    QPushButton:disabled {{
        background-color: {COLORS['muted']};
        color: #bdc3c7;
    }}
    """

def get_responsive_styles():
    """
    دالة الحصول على الأنماط المتجاوبة
    """
    return """
    /* أنماط متجاوبة للشاشات المختلفة */
    QWidget {
        min-width: 1280px;
        min-height: 720px;
    }
    
    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 1366px) {
        QLabel#page_title {
            font-size: 20px;
            padding: 10px;
        }
        
        QPushButton#action_button {
            min-width: 80px;
            min-height: 60px;
        }
    }
    
    /* تحسينات للشاشات الكبيرة */
    @media (min-width: 1920px) {
        QLabel#page_title {
            font-size: 28px;
            padding: 20px;
        }
        
        QPushButton#action_button {
            min-width: 120px;
            min-height: 80px;
        }
    }
    """
