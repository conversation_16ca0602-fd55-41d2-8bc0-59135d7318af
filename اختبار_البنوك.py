# -*- coding: utf-8 -*-
"""
اختبار واجهة البنوك بشكل منفصل
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtCore import Qt
from db import تهيئة_قاعدة_البيانات
from other_interfaces import واجهة_البنوك

class نافذة_اختبار_البنوك(QMainWindow):
    """
    نافذة اختبار واجهة البنوك
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار واجهة البنوك")
        self.setMinimumSize(1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء واجهة البنوك
        واجهة_بنوك = واجهة_البنوك()
        self.setCentralWidget(واجهة_بنوك)

def main():
    """
    دالة تشغيل اختبار واجهة البنوك
    """
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تهيئة قاعدة البيانات
    if not تهيئة_قاعدة_البيانات():
        print("فشل في تهيئة قاعدة البيانات")
        sys.exit(1)
    
    # إنشاء النافذة
    نافذة = نافذة_اختبار_البنوك()
    نافذة.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
