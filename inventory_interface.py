# -*- coding: utf-8 -*-
"""
واجهة إدارة المخزون والمنتجات
تحتوي على إدارة المنتجات وفواتير الشراء والموردين
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime, date
from decimal import Decimal
from db import قاعدة_البيانات

class واجهة_إدارة_المخزون(QWidget):
    """
    واجهة إدارة المخزون الرئيسية
    تحتوي على تبويبات للمنتجات وفواتير الشراء والموردين
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة إدارة المخزون
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة إدارة المخزون
        """
        # التخطيط الرئيسي
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)
        
        # عنوان الصفحة
        عنوان = QLabel("إدارة المخزون والمنتجات")
        عنوان.setObjectName("page_title")
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # التبويبات
        self.تبويبات = QTabWidget()
        self.تبويبات.setObjectName("main_tabs")
        
        # تبويب المنتجات
        self.تبويب_المنتجات = تبويب_المنتجات(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_المنتجات, "المنتجات")
        
        # تبويب فواتير الشراء
        self.تبويب_فواتير_الشراء = تبويب_فواتير_الشراء(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_فواتير_الشراء, "فواتير الشراء")
        
        # تبويب الموردين
        self.تبويب_الموردين = تبويب_الموردين(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_الموردين, "الموردين")
        
        # تبويب الفئات
        self.تبويب_الفئات = تبويب_الفئات(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_الفئات, "الفئات")
        
        التخطيط_الرئيسي.addWidget(self.تبويبات)
    
    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS لواجهة إدارة المخزون
        """
        نمط = """
        QLabel#page_title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #e67e22;
        }
        
        QTabWidget#main_tabs {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }
        
        QTabWidget#main_tabs::pane {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }
        
        QTabWidget#main_tabs::tab-bar {
            alignment: right;
        }
        
        QTabBar::tab {
            background-color: #ecf0f1;
            color: #2c3e50;
            padding: 10px 20px;
            margin: 2px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        QTabBar::tab:selected {
            background-color: #e67e22;
            color: white;
        }
        
        QTabBar::tab:hover {
            background-color: #f39c12;
            color: white;
        }
        """
        
        self.setStyleSheet(نمط)

class تبويب_المنتجات(QWidget):
    """
    تبويب إدارة المنتجات
    """
    
    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب المنتجات
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.المنتج_المحدد = None
        
        self.إعداد_التبويب()
    
    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب المنتجات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)
        
        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)
        
        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)
        
        # جدول المنتجات
        self.إنشاء_جدول_المنتجات(التخطيط_الرئيسي)
    
    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)
        
        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)
        
        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("اسم المنتج، الباركود، أو الفئة...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_المنتجات)
        
        # فلتر الفئة
        تسمية_فئة = QLabel("الفئة:")
        self.قائمة_فئة = QComboBox()
        self.قائمة_فئة.currentTextChanged.connect(self.البحث_عن_المنتجات)
        
        # فلتر الحالة
        تسمية_حالة = QLabel("الحالة:")
        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "متوفر", "نفد", "قريب النفاد", "نشط", "غير نشط"])
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_المنتجات)
        
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث, 2)
        تخطيط_البحث.addWidget(تسمية_فئة)
        تخطيط_البحث.addWidget(self.قائمة_فئة)
        تخطيط_البحث.addWidget(تسمية_حالة)
        تخطيط_البحث.addWidget(self.قائمة_حالة)
        
        التخطيط_الرئيسي.addWidget(إطار_البحث)

        # تطبيق أنماط إطار البحث
        إطار_البحث.setStyleSheet("""
            QFrame#search_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }

            QLineEdit, QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
            }

            QLineEdit:focus, QComboBox:focus {
                border-color: #e67e22;
            }
        """)
    
    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط أزرار الإجراءات
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(80)
        
        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(15)
        
        # أزرار الإجراءات
        أزرار = [
            ("➕", "إضافة منتج", "add_product", "#27ae60", self.إضافة_منتج),
            ("✏️", "تعديل", "edit_product", "#3498db", self.تعديل_منتج),
            ("🗑️", "حذف", "delete_product", "#e74c3c", self.حذف_منتج),
            ("📊", "تقرير المخزون", "inventory_report", "#9b59b6", self.تقرير_المخزون),
            ("⚠️", "تنبيهات المخزون", "stock_alerts", "#f39c12", self.تنبيهات_المخزون),
            ("🖨️", "طباعة", "print_products", "#7f8c8d", self.طباعة_المنتجات)
        ]
        
        self.أزرار_الإجراءات = {}
        
        for أيقونة, نص, مفتاح, لون, وظيفة in أزرار:
            زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
            زر.clicked.connect(وظيفة)
            self.أزرار_الإجراءات[مفتاح] = زر
            تخطيط_الأزرار.addWidget(زر)
        
        تخطيط_الأزرار.addStretch()
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

        # تطبيق أنماط إطار الأزرار
        إطار_الأزرار.setStyleSheet("""
            QFrame#buttons_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
    
    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(100, 60)
        زر.setObjectName("action_button")
        
        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)
        
        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 20px;")
        
        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 9px; font-weight: bold;")
        تسمية_نص.setWordWrap(True)
        
        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)
        
        # تطبيق اللون
        زر.setStyleSheet(f"""
            QPushButton#action_button {{
                border: 2px solid {لون};
                border-radius: 8px;
                background-color: white;
            }}
            QPushButton#action_button:hover {{
                background-color: {لون};
                color: white;
            }}
            QPushButton#action_button:pressed {{
                background-color: {لون};
                border: 3px solid {لون};
            }}
        """)
        
        return زر
    
    def إنشاء_جدول_المنتجات(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول عرض المنتجات
        """
        # جدول المنتجات
        self.جدول_المنتجات = QTableWidget()
        self.جدول_المنتجات.setObjectName("products_table")
        
        # إعداد الأعمدة
        الأعمدة = [
            "الباركود", "الاسم العام", "الاسم التجاري", "الفئة", 
            "سعر الشراء", "سعر البيع", "سعر البيع أقساط", "الكمية الحالية",
            "الحد الأدنى", "الوحدة", "تاريخ الصلاحية", "الحالة"
        ]
        
        self.جدول_المنتجات.setColumnCount(len(الأعمدة))
        self.جدول_المنتجات.setHorizontalHeaderLabels(الأعمدة)
        
        # إعداد خصائص الجدول
        self.جدول_المنتجات.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_المنتجات.setAlternatingRowColors(True)
        self.جدول_المنتجات.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = self.جدول_المنتجات.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الاسم العام
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # الاسم التجاري
        
        # ربط النقر بتحديد المنتج
        self.جدول_المنتجات.itemSelectionChanged.connect(self.تحديد_منتج)
        
        # قائمة سياق
        self.جدول_المنتجات.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_المنتجات.customContextMenuRequested.connect(self.عرض_قائمة_سياق)
        
        التخطيط_الرئيسي.addWidget(self.جدول_المنتجات)
        
        # تطبيق الأنماط
        self.جدول_المنتجات.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
            
            QTableWidget::item {
                padding: 8px;
                text-align: center;
            }
            
            QHeaderView::section {
                background-color: #e67e22;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        


        # تحميل البيانات بعد إنشاء الجدول
        self.تحميل_الفئات()
        self.تحميل_المنتجات()

        # تعطيل أزرار التعديل والحذف في البداية
        self.أزرار_الإجراءات["edit_product"].setEnabled(False)
        self.أزرار_الإجراءات["delete_product"].setEnabled(False)

    def تحميل_الفئات(self):
        """
        دالة تحميل قائمة الفئات
        """
        try:
            استعلام = "SELECT رقم_الفئة, اسم_الفئة FROM الفئات WHERE حالة_النشاط = TRUE ORDER BY اسم_الفئة"
            الفئات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_فئة.clear()
            self.قائمة_فئة.addItem("جميع الفئات", 0)

            if الفئات:
                for فئة in الفئات:
                    self.قائمة_فئة.addItem(فئة[1], فئة[0])

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الفئات: {str(e)}")

    def تحميل_المنتجات(self):
        """
        دالة تحميل جميع المنتجات
        """
        try:
            استعلام = """
            SELECT م.الباركود, م.الاسم_العام, م.الاسم_التجاري, ف.اسم_الفئة,
                   م.سعر_الشراء, م.سعر_البيع, م.سعر_البيع_أقساط, م.الكمية_الحالية,
                   م.الحد_الأدنى, م.الوحدة, م.تاريخ_الصلاحية,
                   CASE
                       WHEN م.حالة_النشاط = 0 THEN 'غير نشط'
                       WHEN م.الكمية_الحالية = 0 THEN 'نفد'
                       WHEN م.الكمية_الحالية <= م.الحد_الأدنى THEN 'قريب النفاد'
                       ELSE 'متوفر'
                   END as الحالة
            FROM المنتجات م
            LEFT JOIN الفئات ف ON م.رقم_الفئة = ف.رقم_الفئة
            ORDER BY م.الاسم_العام
            """

            المنتجات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if المنتجات:
                self.جدول_المنتجات.setRowCount(len(المنتجات))

                for صف, منتج in enumerate(المنتجات):
                    for عمود, قيمة in enumerate(منتج):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)

                        # تلوين الكمية حسب الحالة
                        if عمود == 7:  # عمود الكمية الحالية
                            كمية = int(قيمة) if قيمة else 0
                            حد_أدنى = int(منتج[8]) if منتج[8] else 0

                            if كمية == 0:
                                عنصر.setForeground(QColor("#e74c3c"))  # أحمر للنفاد
                            elif كمية <= حد_أدنى:
                                عنصر.setForeground(QColor("#f39c12"))  # برتقالي للقريب من النفاد
                            else:
                                عنصر.setForeground(QColor("#27ae60"))  # أخضر للمتوفر

                        # تلوين الحالة
                        if عمود == 11:  # عمود الحالة
                            if قيمة == "متوفر":
                                عنصر.setForeground(QColor("#27ae60"))
                            elif قيمة == "قريب النفاد":
                                عنصر.setForeground(QColor("#f39c12"))
                            elif قيمة == "نفد":
                                عنصر.setForeground(QColor("#e74c3c"))
                            else:
                                عنصر.setForeground(QColor("#7f8c8d"))

                        self.جدول_المنتجات.setItem(صف, عمود, عنصر)
            else:
                self.جدول_المنتجات.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المنتجات: {str(e)}")

    def البحث_عن_المنتجات(self):
        """
        دالة البحث عن المنتجات
        """
        نص_البحث = self.حقل_البحث.text().strip()
        فئة_محددة = self.قائمة_فئة.currentData()
        حالة_محددة = self.قائمة_حالة.currentText()

        try:
            شرط_البحث = "1=1"
            معاملات = []

            # شرط البحث النصي
            if نص_البحث:
                شرط_البحث += """ AND (
                    م.الاسم_العام LIKE %s OR م.الاسم_التجاري LIKE %s OR
                    م.الباركود LIKE %s OR ف.اسم_الفئة LIKE %s
                )"""
                معاملات.extend([f"%{نص_البحث}%"] * 4)

            # شرط فلتر الفئة
            if فئة_محددة and فئة_محددة != 0:
                شرط_البحث += " AND م.رقم_الفئة = %s"
                معاملات.append(فئة_محددة)

            # شرط فلتر الحالة
            if حالة_محددة != "الكل":
                if حالة_محددة == "متوفر":
                    شرط_البحث += " AND م.الكمية_الحالية > م.الحد_الأدنى AND م.حالة_النشاط = 1"
                elif حالة_محددة == "نفد":
                    شرط_البحث += " AND م.الكمية_الحالية = 0"
                elif حالة_محددة == "قريب النفاد":
                    شرط_البحث += " AND م.الكمية_الحالية > 0 AND م.الكمية_الحالية <= م.الحد_الأدنى"
                elif حالة_محددة == "نشط":
                    شرط_البحث += " AND م.حالة_النشاط = 1"
                elif حالة_محددة == "غير نشط":
                    شرط_البحث += " AND م.حالة_النشاط = 0"

            استعلام = f"""
            SELECT م.الباركود, م.الاسم_العام, م.الاسم_التجاري, ف.اسم_الفئة,
                   م.سعر_الشراء, م.سعر_البيع, م.سعر_البيع_أقساط, م.الكمية_الحالية,
                   م.الحد_الأدنى, م.الوحدة, م.تاريخ_الصلاحية,
                   CASE
                       WHEN م.حالة_النشاط = 0 THEN 'غير نشط'
                       WHEN م.الكمية_الحالية = 0 THEN 'نفد'
                       WHEN م.الكمية_الحالية <= م.الحد_الأدنى THEN 'قريب النفاد'
                       ELSE 'متوفر'
                   END as الحالة
            FROM المنتجات م
            LEFT JOIN الفئات ف ON م.رقم_الفئة = ف.رقم_الفئة
            WHERE {شرط_البحث}
            ORDER BY م.الاسم_العام
            """

            المنتجات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_المنتجات.setRowCount(0)

            if المنتجات:
                self.جدول_المنتجات.setRowCount(len(المنتجات))

                for صف, منتج in enumerate(المنتجات):
                    for عمود, قيمة in enumerate(منتج):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)

                        # تلوين الكمية حسب الحالة
                        if عمود == 7:  # عمود الكمية الحالية
                            كمية = int(قيمة) if قيمة else 0
                            حد_أدنى = int(منتج[8]) if منتج[8] else 0

                            if كمية == 0:
                                عنصر.setForeground(QColor("#e74c3c"))
                            elif كمية <= حد_أدنى:
                                عنصر.setForeground(QColor("#f39c12"))
                            else:
                                عنصر.setForeground(QColor("#27ae60"))

                        # تلوين الحالة
                        if عمود == 11:  # عمود الحالة
                            if قيمة == "متوفر":
                                عنصر.setForeground(QColor("#27ae60"))
                            elif قيمة == "قريب النفاد":
                                عنصر.setForeground(QColor("#f39c12"))
                            elif قيمة == "نفد":
                                عنصر.setForeground(QColor("#e74c3c"))
                            else:
                                عنصر.setForeground(QColor("#7f8c8d"))

                        self.جدول_المنتجات.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_منتج(self):
        """
        دالة تحديد المنتج المختار
        """
        صف_محدد = self.جدول_المنتجات.currentRow()

        if صف_محدد >= 0:
            باركود = self.جدول_المنتجات.item(صف_محدد, 0).text()
            self.المنتج_المحدد = باركود

            # تفعيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_product"].setEnabled(True)
            self.أزرار_الإجراءات["delete_product"].setEnabled(True)
        else:
            self.المنتج_المحدد = None

            # تعطيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_product"].setEnabled(False)
            self.أزرار_الإجراءات["delete_product"].setEnabled(False)

    def عرض_قائمة_سياق(self, موضع):
        """
        دالة عرض قائمة السياق
        """
        if self.جدول_المنتجات.itemAt(موضع):
            قائمة = QMenu(self)

            إجراء_تعديل = قائمة.addAction("تعديل المنتج")
            إجراء_تعديل.triggered.connect(self.تعديل_منتج)

            إجراء_حذف = قائمة.addAction("حذف المنتج")
            إجراء_حذف.triggered.connect(self.حذف_منتج)

            قائمة.addSeparator()

            إجراء_تفاصيل = قائمة.addAction("تفاصيل المنتج")
            إجراء_تفاصيل.triggered.connect(self.تفاصيل_منتج)

            قائمة.exec_(self.جدول_المنتجات.mapToGlobal(موضع))

    # دوال الإجراءات
    def إضافة_منتج(self):
        """دالة إضافة منتج جديد"""
        نافذة_إضافة = نافذة_منتج(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_المنتجات()

    def تعديل_منتج(self):
        """دالة تعديل المنتج المحدد"""
        if not self.المنتج_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
            return

        نافذة_تعديل = نافذة_منتج(self, "تعديل", self.المنتج_المحدد)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_المنتجات()

    def حذف_منتج(self):
        """دالة حذف المنتج المحدد"""
        if not self.المنتج_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
            return
        QMessageBox.information(self, "معلومات", "وظيفة حذف المنتج قيد التطوير")

    def تفاصيل_منتج(self):
        """دالة عرض تفاصيل المنتج"""
        QMessageBox.information(self, "معلومات", "وظيفة تفاصيل المنتج قيد التطوير")

    def تقرير_المخزون(self):
        """دالة إنشاء تقرير المخزون"""
        QMessageBox.information(self, "معلومات", "وظيفة تقرير المخزون قيد التطوير")

    def تنبيهات_المخزون(self):
        """دالة عرض تنبيهات المخزون"""
        QMessageBox.information(self, "معلومات", "وظيفة تنبيهات المخزون قيد التطوير")

    def طباعة_المنتجات(self):
        """دالة طباعة قائمة المنتجات"""
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")

# تبويبات أخرى (مبسطة للآن)
class تبويب_فواتير_الشراء(QWidget):
    """تبويب فواتير الشراء"""

    def __init__(self, قاعدة_البيانات):
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.الفاتورة_المحددة = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب فواتير الشراء
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول فواتير الشراء
        self.إنشاء_جدول_الفواتير(التخطيط_الرئيسي)

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("رقم الفاتورة، المورد، أو التاريخ...")

        # فلتر المورد
        تسمية_مورد = QLabel("المورد:")
        self.قائمة_مورد = QComboBox()

        # فلتر التاريخ
        تسمية_من = QLabel("من:")
        self.تاريخ_من = QDateEdit()
        self.تاريخ_من.setDate(QDate.currentDate().addMonths(-1))

        تسمية_إلى = QLabel("إلى:")
        self.تاريخ_إلى = QDateEdit()
        self.تاريخ_إلى.setDate(QDate.currentDate())

        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث, 2)
        تخطيط_البحث.addWidget(تسمية_مورد)
        تخطيط_البحث.addWidget(self.قائمة_مورد)
        تخطيط_البحث.addWidget(تسمية_من)
        تخطيط_البحث.addWidget(self.تاريخ_من)
        تخطيط_البحث.addWidget(تسمية_إلى)
        تخطيط_البحث.addWidget(self.تاريخ_إلى)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

        # تحميل قائمة الموردين
        self.تحميل_قائمة_الموردين()

        # تطبيق أنماط إطار البحث
        إطار_البحث.setStyleSheet("""
            QFrame#search_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط أزرار الإجراءات
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(80)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(15)

        # أزرار الإجراءات
        أزرار = [
            ("➕", "فاتورة جديدة", "new_invoice", "#27ae60", self.فاتورة_جديدة),
            ("👁️", "عرض الفاتورة", "view_invoice", "#3498db", self.عرض_الفاتورة),
            ("✏️", "تعديل", "edit_invoice", "#f39c12", self.تعديل_الفاتورة),
            ("🗑️", "حذف", "delete_invoice", "#e74c3c", self.حذف_الفاتورة),
            ("🖨️", "طباعة", "print_invoice", "#9b59b6", self.طباعة_الفاتورة),
            ("📊", "تقرير", "invoice_report", "#7f8c8d", self.تقرير_الفواتير)
        ]

        self.أزرار_الإجراءات = {}

        for أيقونة, نص, مفتاح, لون, وظيفة in أزرار:
            زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
            زر.clicked.connect(وظيفة)
            self.أزرار_الإجراءات[مفتاح] = زر
            تخطيط_الأزرار.addWidget(زر)

        تخطيط_الأزرار.addStretch()
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

        # تطبيق أنماط إطار الأزرار
        إطار_الأزرار.setStyleSheet("""
            QFrame#buttons_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(100, 60)
        زر.setObjectName("action_button")

        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)

        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 20px;")

        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 9px; font-weight: bold;")
        تسمية_نص.setWordWrap(True)

        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)

        # تطبيق اللون
        زر.setStyleSheet(f"""
            QPushButton#action_button {{
                border: 2px solid {لون};
                border-radius: 8px;
                background-color: white;
            }}
            QPushButton#action_button:hover {{
                background-color: {لون};
                color: white;
            }}
            QPushButton#action_button:pressed {{
                background-color: {لون};
                border: 3px solid {لون};
            }}
        """)

        return زر

    def إنشاء_جدول_الفواتير(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول عرض فواتير الشراء
        """
        # جدول الفواتير
        self.جدول_الفواتير = QTableWidget()
        self.جدول_الفواتير.setObjectName("invoices_table")

        # إعداد الأعمدة
        الأعمدة = [
            "رقم الفاتورة", "المورد", "تاريخ الفاتورة", "إجمالي الفاتورة",
            "المدفوع", "المتبقي", "الحالة", "ملاحظات"
        ]

        self.جدول_الفواتير.setColumnCount(len(الأعمدة))
        self.جدول_الفواتير.setHorizontalHeaderLabels(الأعمدة)

        # إعداد خصائص الجدول
        self.جدول_الفواتير.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الفواتير.setAlternatingRowColors(True)
        self.جدول_الفواتير.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.جدول_الفواتير.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # المورد

        # ربط النقر بتحديد الفاتورة
        self.جدول_الفواتير.itemSelectionChanged.connect(self.تحديد_فاتورة)

        # قائمة سياق
        self.جدول_الفواتير.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_الفواتير.customContextMenuRequested.connect(self.عرض_قائمة_سياق)

        التخطيط_الرئيسي.addWidget(self.جدول_الفواتير)

        # تطبيق الأنماط
        self.جدول_الفواتير.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }

            QTableWidget::item {
                padding: 8px;
                text-align: center;
            }

            QHeaderView::section {
                background-color: #e67e22;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        # تعطيل أزرار التعديل والحذف في البداية
        self.أزرار_الإجراءات["view_invoice"].setEnabled(False)
        self.أزرار_الإجراءات["edit_invoice"].setEnabled(False)
        self.أزرار_الإجراءات["delete_invoice"].setEnabled(False)
        self.أزرار_الإجراءات["print_invoice"].setEnabled(False)

        # ربط أحداث البحث بعد إنشاء الجدول
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الفواتير)
        self.قائمة_مورد.currentTextChanged.connect(self.البحث_عن_الفواتير)
        self.تاريخ_من.dateChanged.connect(self.البحث_عن_الفواتير)
        self.تاريخ_إلى.dateChanged.connect(self.البحث_عن_الفواتير)

        # تحميل البيانات بعد إنشاء الجدول
        self.تحميل_قائمة_الموردين()
        self.تحميل_فواتير_الشراء()

    def تحميل_قائمة_الموردين(self):
        """
        دالة تحميل قائمة الموردين للفلتر
        """
        try:
            استعلام = "SELECT رقم_المورد, اسم_المورد FROM الموردين WHERE حالة_النشاط = 'نشط' ORDER BY اسم_المورد"
            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_مورد.clear()
            self.قائمة_مورد.addItem("جميع الموردين", 0)

            if الموردين:
                for مورد in الموردين:
                    self.قائمة_مورد.addItem(مورد[1], مورد[0])

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين: {str(e)}")

    def تحميل_فواتير_الشراء(self):
        """
        دالة تحميل جميع فواتير الشراء
        """
        try:
            استعلام = """
            SELECT f.رقم_الفاتورة, m.اسم_المورد,
                   DATE_FORMAT(f.تاريخ_الفاتورة, '%Y-%m-%d') as تاريخ_الفاتورة,
                   f.إجمالي_الفاتورة, f.المبلغ_المدفوع,
                   (f.إجمالي_الفاتورة - f.المبلغ_المدفوع) as المتبقي,
                   f.حالة_الدفع, f.ملاحظات
            FROM فواتير_الشراء f
            LEFT JOIN الموردين m ON f.رقم_المورد = m.رقم_المورد
            ORDER BY f.تاريخ_الفاتورة DESC
            """

            الفواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if الفواتير:
                self.جدول_الفواتير.setRowCount(len(الفواتير))

                for صف, فاتورة in enumerate(الفواتير):
                    for عمود, قيمة in enumerate(فاتورة):
                        if عمود in [3, 4, 5]:  # أعمدة المبالغ
                            مبلغ = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{مبلغ:.2f}")
                            if عمود == 5 and مبلغ > 0:  # المتبقي
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 6:  # عمود الحالة
                            حالة = قيمة or "غير مدفوع"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "مدفوع":
                                عنصر.setForeground(QColor("#27ae60"))
                            elif حالة == "مدفوع جزئياً":
                                عنصر.setForeground(QColor("#f39c12"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الفواتير.setItem(صف, عمود, عنصر)
            else:
                self.جدول_الفواتير.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل فواتير الشراء: {str(e)}")

    def البحث_عن_الفواتير(self):
        """
        دالة البحث عن فواتير الشراء
        """
        نص_البحث = self.حقل_البحث.text().strip()
        رقم_المورد = self.قائمة_مورد.currentData()
        تاريخ_من = self.تاريخ_من.date().toString("yyyy-MM-dd")
        تاريخ_إلى = self.تاريخ_إلى.date().toString("yyyy-MM-dd")

        try:
            شرط_البحث = "1=1"
            معاملات = []

            # شرط البحث النصي
            if نص_البحث:
                شرط_البحث += """ AND (
                    f.رقم_الفاتورة LIKE %s OR m.اسم_المورد LIKE %s OR
                    f.ملاحظات LIKE %s
                )"""
                معاملات.extend([f"%{نص_البحث}%"] * 3)

            # شرط فلتر المورد
            if رقم_المورد and رقم_المورد != 0:
                شرط_البحث += " AND f.رقم_المورد = %s"
                معاملات.append(رقم_المورد)

            # شرط فلتر التاريخ
            شرط_البحث += " AND f.تاريخ_الفاتورة BETWEEN %s AND %s"
            معاملات.extend([تاريخ_من, تاريخ_إلى])

            استعلام = f"""
            SELECT f.رقم_الفاتورة, m.اسم_المورد,
                   DATE_FORMAT(f.تاريخ_الفاتورة, '%Y-%m-%d') as تاريخ_الفاتورة,
                   f.إجمالي_الفاتورة, f.المبلغ_المدفوع,
                   (f.إجمالي_الفاتورة - f.المبلغ_المدفوع) as المتبقي,
                   f.حالة_الدفع, f.ملاحظات
            FROM فواتير_الشراء f
            LEFT JOIN الموردين m ON f.رقم_المورد = m.رقم_المورد
            WHERE {شرط_البحث}
            ORDER BY f.تاريخ_الفاتورة DESC
            """

            الفواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_الفواتير.setRowCount(0)

            if الفواتير:
                self.جدول_الفواتير.setRowCount(len(الفواتير))

                for صف, فاتورة in enumerate(الفواتير):
                    for عمود, قيمة in enumerate(فاتورة):
                        if عمود in [3, 4, 5]:  # أعمدة المبالغ
                            مبلغ = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{مبلغ:.2f}")
                            if عمود == 5 and مبلغ > 0:  # المتبقي
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 6:  # عمود الحالة
                            حالة = قيمة or "غير مدفوع"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "مدفوع":
                                عنصر.setForeground(QColor("#27ae60"))
                            elif حالة == "مدفوع جزئياً":
                                عنصر.setForeground(QColor("#f39c12"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الفواتير.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_فاتورة(self):
        """
        دالة تحديد الفاتورة المختارة
        """
        صف_محدد = self.جدول_الفواتير.currentRow()

        if صف_محدد >= 0:
            رقم_الفاتورة = self.جدول_الفواتير.item(صف_محدد, 0).text()
            self.الفاتورة_المحددة = int(رقم_الفاتورة)

            # تفعيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["view_invoice"].setEnabled(True)
            self.أزرار_الإجراءات["edit_invoice"].setEnabled(True)
            self.أزرار_الإجراءات["delete_invoice"].setEnabled(True)
            self.أزرار_الإجراءات["print_invoice"].setEnabled(True)
        else:
            self.الفاتورة_المحددة = None

            # تعطيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["view_invoice"].setEnabled(False)
            self.أزرار_الإجراءات["edit_invoice"].setEnabled(False)
            self.أزرار_الإجراءات["delete_invoice"].setEnabled(False)
            self.أزرار_الإجراءات["print_invoice"].setEnabled(False)

    def عرض_قائمة_سياق(self, موضع):
        """
        دالة عرض قائمة السياق
        """
        if self.جدول_الفواتير.itemAt(موضع):
            قائمة = QMenu(self)

            إجراء_عرض = قائمة.addAction("عرض الفاتورة")
            إجراء_عرض.triggered.connect(self.عرض_الفاتورة)

            إجراء_تعديل = قائمة.addAction("تعديل الفاتورة")
            إجراء_تعديل.triggered.connect(self.تعديل_الفاتورة)

            إجراء_حذف = قائمة.addAction("حذف الفاتورة")
            إجراء_حذف.triggered.connect(self.حذف_الفاتورة)

            قائمة.addSeparator()

            إجراء_طباعة = قائمة.addAction("طباعة الفاتورة")
            إجراء_طباعة.triggered.connect(self.طباعة_الفاتورة)

            قائمة.exec_(self.جدول_الفواتير.mapToGlobal(موضع))

    # دوال الإجراءات
    def فاتورة_جديدة(self):
        """دالة إنشاء فاتورة شراء جديدة"""
        نافذة_إضافة = نافذة_فاتورة_شراء(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_فواتير_الشراء()

    def عرض_الفاتورة(self):
        """دالة عرض تفاصيل الفاتورة"""
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للعرض")
            return

        نافذة_عرض = نافذة_عرض_فاتورة_شراء(self, self.الفاتورة_المحددة)
        نافذة_عرض.exec_()

    def تعديل_الفاتورة(self):
        """دالة تعديل الفاتورة المحددة"""
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للتعديل")
            return

        نافذة_تعديل = نافذة_فاتورة_شراء(self, "تعديل", self.الفاتورة_المحددة)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_فواتير_الشراء()

    def حذف_الفاتورة(self):
        """دالة حذف الفاتورة المحددة"""
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للحذف")
            return

        رد = QMessageBox.question(self, "تأكيد الحذف",
                                  f"هل تريد حذف الفاتورة رقم {self.الفاتورة_المحددة}؟\n"
                                  "تحذير: سيتم حذف جميع تفاصيل الفاتورة",
                                  QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                # حذف تفاصيل الفاتورة أولاً
                استعلام1 = "DELETE FROM تفاصيل_فواتير_الشراء WHERE رقم_الفاتورة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام1, (self.الفاتورة_المحددة,))

                # حذف الفاتورة
                استعلام2 = "DELETE FROM فواتير_الشراء WHERE رقم_الفاتورة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام2, (self.الفاتورة_المحددة,))

                QMessageBox.information(self, "نجح", "تم حذف الفاتورة بنجاح")
                self.تحميل_فواتير_الشراء()
                self.الفاتورة_المحددة = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الفاتورة: {str(e)}")

    def طباعة_الفاتورة(self):
        """دالة طباعة الفاتورة المحددة"""
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
            return

        نافذة_عرض = نافذة_عرض_فاتورة_شراء(self, self.الفاتورة_المحددة)
        نافذة_عرض.exec_()

    def تقرير_الفواتير(self):
        """دالة إنشاء تقرير الفواتير"""
        QMessageBox.information(self, "معلومات", "وظيفة التقرير قيد التطوير")

class تبويب_الموردين(QWidget):
    """تبويب الموردين"""

    def __init__(self, قاعدة_البيانات):
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.المورد_المحدد = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب الموردين
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول الموردين
        self.إنشاء_جدول_الموردين(التخطيط_الرئيسي)

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("اسم المورد، رقم الهاتف، أو العنوان...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الموردين)

        # فلتر الحالة
        تسمية_حالة = QLabel("الحالة:")
        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "نشط", "غير نشط"])
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_الموردين)

        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث, 2)
        تخطيط_البحث.addWidget(تسمية_حالة)
        تخطيط_البحث.addWidget(self.قائمة_حالة)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

        # تطبيق أنماط إطار البحث
        إطار_البحث.setStyleSheet("""
            QFrame#search_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط أزرار الإجراءات
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(80)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(15)

        # أزرار الإجراءات
        أزرار = [
            ("➕", "إضافة مورد", "add_supplier", "#27ae60", self.إضافة_مورد),
            ("✏️", "تعديل", "edit_supplier", "#3498db", self.تعديل_مورد),
            ("🗑️", "حذف", "delete_supplier", "#e74c3c", self.حذف_مورد),
            ("👁️", "عرض التفاصيل", "view_details", "#9b59b6", self.عرض_تفاصيل_مورد),
            ("📊", "تقرير المورد", "supplier_report", "#f39c12", self.تقرير_مورد),
            ("🖨️", "طباعة", "print_suppliers", "#7f8c8d", self.طباعة_الموردين)
        ]

        self.أزرار_الإجراءات = {}

        for أيقونة, نص, مفتاح, لون, وظيفة in أزرار:
            زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
            زر.clicked.connect(وظيفة)
            self.أزرار_الإجراءات[مفتاح] = زر
            تخطيط_الأزرار.addWidget(زر)

        تخطيط_الأزرار.addStretch()
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

        # تطبيق أنماط إطار الأزرار
        إطار_الأزرار.setStyleSheet("""
            QFrame#buttons_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(100, 60)
        زر.setObjectName("action_button")

        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)

        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 20px;")

        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 9px; font-weight: bold;")
        تسمية_نص.setWordWrap(True)

        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)

        # تطبيق اللون
        زر.setStyleSheet(f"""
            QPushButton#action_button {{
                border: 2px solid {لون};
                border-radius: 8px;
                background-color: white;
            }}
            QPushButton#action_button:hover {{
                background-color: {لون};
                color: white;
            }}
            QPushButton#action_button:pressed {{
                background-color: {لون};
                border: 3px solid {لون};
            }}
        """)

        return زر

    def إنشاء_جدول_الموردين(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول عرض الموردين
        """
        # جدول الموردين
        self.جدول_الموردين = QTableWidget()
        self.جدول_الموردين.setObjectName("suppliers_table")

        # إعداد الأعمدة
        الأعمدة = [
            "رقم المورد", "اسم المورد", "العنوان", "رقم الهاتف",
            "البريد الإلكتروني", "الرصيد الحالي", "الحالة", "تاريخ الإضافة"
        ]

        self.جدول_الموردين.setColumnCount(len(الأعمدة))
        self.جدول_الموردين.setHorizontalHeaderLabels(الأعمدة)

        # إعداد خصائص الجدول
        self.جدول_الموردين.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الموردين.setAlternatingRowColors(True)
        self.جدول_الموردين.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.جدول_الموردين.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المورد
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # العنوان

        # ربط النقر بتحديد المورد
        self.جدول_الموردين.itemSelectionChanged.connect(self.تحديد_مورد)

        # قائمة سياق
        self.جدول_الموردين.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_الموردين.customContextMenuRequested.connect(self.عرض_قائمة_سياق)

        التخطيط_الرئيسي.addWidget(self.جدول_الموردين)

        # تطبيق الأنماط
        self.جدول_الموردين.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }

            QTableWidget::item {
                padding: 8px;
                text-align: center;
            }

            QHeaderView::section {
                background-color: #e67e22;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        # تعطيل أزرار التعديل والحذف في البداية
        self.أزرار_الإجراءات["edit_supplier"].setEnabled(False)
        self.أزرار_الإجراءات["delete_supplier"].setEnabled(False)
        self.أزرار_الإجراءات["view_details"].setEnabled(False)
        self.أزرار_الإجراءات["supplier_report"].setEnabled(False)

        # تحميل البيانات بعد إنشاء الجدول
        self.تحميل_الموردين()

    def تحميل_الموردين(self):
        """
        دالة تحميل جميع الموردين
        """
        try:
            استعلام = """
            SELECT رقم_المورد, اسم_المورد, العنوان, رقم_الهاتف,
                   البريد_الإلكتروني, الرصيد_الحالي, حالة_النشاط,
                   DATE_FORMAT(تاريخ_الإضافة, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الموردين
            ORDER BY تاريخ_الإضافة DESC
            """

            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if الموردين:
                self.جدول_الموردين.setRowCount(len(الموردين))

                for صف, مورد in enumerate(الموردين):
                    for عمود, قيمة in enumerate(مورد):
                        if عمود == 6:  # عمود الحالة
                            حالة = "نشط" if قيمة == "نشط" else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 5:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{رصيد:.2f}")
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))  # أحمر للدين
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))  # أخضر للرصيد الموجب
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الموردين.setItem(صف, عمود, عنصر)
            else:
                self.جدول_الموردين.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين: {str(e)}")

    def البحث_عن_الموردين(self):
        """
        دالة البحث عن الموردين
        """
        نص_البحث = self.حقل_البحث.text().strip()
        حالة_الفلتر = self.قائمة_حالة.currentText()

        try:
            شرط_البحث = "1=1"
            معاملات = []

            # شرط البحث النصي
            if نص_البحث:
                شرط_البحث += """ AND (
                    اسم_المورد LIKE %s OR العنوان LIKE %s OR
                    رقم_الهاتف LIKE %s OR البريد_الإلكتروني LIKE %s
                )"""
                معاملات.extend([f"%{نص_البحث}%"] * 4)

            # شرط فلتر الحالة
            if حالة_الفلتر != "الكل":
                شرط_البحث += " AND حالة_النشاط = %s"
                معاملات.append(حالة_الفلتر)

            استعلام = f"""
            SELECT رقم_المورد, اسم_المورد, العنوان, رقم_الهاتف,
                   البريد_الإلكتروني, الرصيد_الحالي, حالة_النشاط,
                   DATE_FORMAT(تاريخ_الإضافة, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الموردين
            WHERE {شرط_البحث}
            ORDER BY تاريخ_الإضافة DESC
            """

            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_الموردين.setRowCount(0)

            if الموردين:
                self.جدول_الموردين.setRowCount(len(الموردين))

                for صف, مورد in enumerate(الموردين):
                    for عمود, قيمة in enumerate(مورد):
                        if عمود == 6:  # عمود الحالة
                            حالة = "نشط" if قيمة == "نشط" else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 5:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{رصيد:.2f}")
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الموردين.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_مورد(self):
        """
        دالة تحديد المورد المختار
        """
        صف_محدد = self.جدول_الموردين.currentRow()

        if صف_محدد >= 0:
            رقم_المورد = self.جدول_الموردين.item(صف_محدد, 0).text()
            self.المورد_المحدد = int(رقم_المورد)

            # تفعيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_supplier"].setEnabled(True)
            self.أزرار_الإجراءات["delete_supplier"].setEnabled(True)
            self.أزرار_الإجراءات["view_details"].setEnabled(True)
            self.أزرار_الإجراءات["supplier_report"].setEnabled(True)
        else:
            self.المورد_المحدد = None

            # تعطيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_supplier"].setEnabled(False)
            self.أزرار_الإجراءات["delete_supplier"].setEnabled(False)
            self.أزرار_الإجراءات["view_details"].setEnabled(False)
            self.أزرار_الإجراءات["supplier_report"].setEnabled(False)

    def عرض_قائمة_سياق(self, موضع):
        """
        دالة عرض قائمة السياق
        """
        if self.جدول_الموردين.itemAt(موضع):
            قائمة = QMenu(self)

            إجراء_تعديل = قائمة.addAction("تعديل المورد")
            إجراء_تعديل.triggered.connect(self.تعديل_مورد)

            إجراء_حذف = قائمة.addAction("حذف المورد")
            إجراء_حذف.triggered.connect(self.حذف_مورد)

            قائمة.addSeparator()

            إجراء_تفاصيل = قائمة.addAction("عرض التفاصيل")
            إجراء_تفاصيل.triggered.connect(self.عرض_تفاصيل_مورد)

            إجراء_تقرير = قائمة.addAction("تقرير المورد")
            إجراء_تقرير.triggered.connect(self.تقرير_مورد)

            قائمة.exec_(self.جدول_الموردين.mapToGlobal(موضع))

    # دوال الإجراءات
    def إضافة_مورد(self):
        """دالة إضافة مورد جديد"""
        نافذة_إضافة = نافذة_مورد(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_الموردين()

    def تعديل_مورد(self):
        """دالة تعديل المورد المحدد"""
        if not self.المورد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return

        نافذة_تعديل = نافذة_مورد(self, "تعديل", self.المورد_المحدد)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_الموردين()

    def حذف_مورد(self):
        """دالة حذف المورد المحدد"""
        if not self.المورد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return

        # الحصول على اسم المورد
        صف_محدد = self.جدول_الموردين.currentRow()
        اسم_مورد = self.جدول_الموردين.item(صف_محدد, 1).text()

        رد = QMessageBox.question(self, "تأكيد الحذف",
                                  f"هل تريد حذف المورد '{اسم_مورد}'؟\n"
                                  "تحذير: سيتم حذف جميع البيانات المرتبطة بهذا المورد",
                                  QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM الموردين WHERE رقم_المورد = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.المورد_المحدد,))

                QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح")
                self.تحميل_الموردين()
                self.المورد_المحدد = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المورد: {str(e)}")

    def عرض_تفاصيل_مورد(self):
        """دالة عرض تفاصيل المورد"""
        QMessageBox.information(self, "معلومات", "وظيفة تفاصيل المورد قيد التطوير")

    def تقرير_مورد(self):
        """دالة إنشاء تقرير المورد"""
        QMessageBox.information(self, "معلومات", "وظيفة تقرير المورد قيد التطوير")

    def طباعة_الموردين(self):
        """دالة طباعة قائمة الموردين"""
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")

class نافذة_منتج(QDialog):
    """
    نافذة إضافة أو تعديل منتج
    """

    def __init__(self, parent, نوع_العملية, باركود_المنتج=None):
        """
        دالة التهيئة لنافذة المنتج
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.باركود_المنتج = باركود_المنتج

        self.إعداد_النافذة()
        self.إنشاء_النموذج()
        self.تحميل_الفئات()

        if نوع_العملية == "تعديل" and باركود_المنتج:
            self.تحميل_بيانات_المنتج()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} منتج")
        self.setFixedSize(600, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج بيانات المنتج
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} منتج")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # نموذج البيانات
        نموذج = QFormLayout()
        نموذج.setSpacing(10)

        # الباركود
        self.حقل_الباركود = QLineEdit()
        self.حقل_الباركود.setPlaceholderText("سيتم إنشاؤه تلقائياً أو أدخله يدوياً")
        نموذج.addRow("الباركود:", self.حقل_الباركود)

        # الاسم العام
        self.حقل_الاسم_العام = QLineEdit()
        self.حقل_الاسم_العام.setPlaceholderText("أدخل الاسم العام للمنتج")
        نموذج.addRow("الاسم العام *:", self.حقل_الاسم_العام)

        # الاسم التجاري
        self.حقل_الاسم_التجاري = QLineEdit()
        self.حقل_الاسم_التجاري.setPlaceholderText("أدخل الاسم التجاري")
        نموذج.addRow("الاسم التجاري:", self.حقل_الاسم_التجاري)

        # الفئة
        self.قائمة_الفئة = QComboBox()
        نموذج.addRow("الفئة:", self.قائمة_الفئة)

        # الوحدة
        self.قائمة_الوحدة = QComboBox()
        self.قائمة_الوحدة.addItems(["قطعة", "صندوق", "كيلو", "لتر", "متر", "عبوة", "حبة"])
        self.قائمة_الوحدة.setEditable(True)
        نموذج.addRow("الوحدة:", self.قائمة_الوحدة)

        # الأسعار
        self.حقل_سعر_الشراء = QDoubleSpinBox()
        self.حقل_سعر_الشراء.setMaximum(999999.99)
        self.حقل_سعر_الشراء.setSuffix(" ريال")
        نموذج.addRow("سعر الشراء:", self.حقل_سعر_الشراء)

        self.حقل_سعر_البيع = QDoubleSpinBox()
        self.حقل_سعر_البيع.setMaximum(999999.99)
        self.حقل_سعر_البيع.setSuffix(" ريال")
        نموذج.addRow("سعر البيع *:", self.حقل_سعر_البيع)

        self.حقل_سعر_البيع_أقساط = QDoubleSpinBox()
        self.حقل_سعر_البيع_أقساط.setMaximum(999999.99)
        self.حقل_سعر_البيع_أقساط.setSuffix(" ريال")
        نموذج.addRow("سعر البيع أقساط:", self.حقل_سعر_البيع_أقساط)

        # الكميات
        self.حقل_الكمية_الحالية = QSpinBox()
        self.حقل_الكمية_الحالية.setMaximum(999999)
        نموذج.addRow("الكمية الحالية:", self.حقل_الكمية_الحالية)

        self.حقل_الحد_الأدنى = QSpinBox()
        self.حقل_الحد_الأدنى.setMaximum(999999)
        نموذج.addRow("الحد الأدنى:", self.حقل_الحد_الأدنى)

        # الصلاحية
        self.مربع_يخضع_للصلاحية = QCheckBox("المنتج يخضع للصلاحية")
        self.مربع_يخضع_للصلاحية.toggled.connect(self.تبديل_الصلاحية)
        نموذج.addRow("", self.مربع_يخضع_للصلاحية)

        self.حقل_تاريخ_الصلاحية = QDateEdit()
        self.حقل_تاريخ_الصلاحية.setDate(QDate.currentDate().addYears(1))
        self.حقل_تاريخ_الصلاحية.setEnabled(False)
        نموذج.addRow("تاريخ الصلاحية:", self.حقل_تاريخ_الصلاحية)

        # حالة النشاط
        self.مربع_نشط = QCheckBox("منتج نشط")
        self.مربع_نشط.setChecked(True)
        نموذج.addRow("الحالة:", self.مربع_نشط)

        التخطيط_الرئيسي.addLayout(نموذج)

        # أزرار الحفظ والإلغاء
        تخطيط_أزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        زر_حفظ.clicked.connect(self.حفظ_المنتج)

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        زر_إلغاء.clicked.connect(self.reject)

        تخطيط_أزرار.addWidget(زر_حفظ)
        تخطيط_أزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_أزرار)

    def تحميل_الفئات(self):
        """
        دالة تحميل قائمة الفئات
        """
        try:
            استعلام = "SELECT رقم_الفئة, اسم_الفئة FROM الفئات WHERE حالة_النشاط = TRUE ORDER BY اسم_الفئة"
            الفئات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_الفئة.clear()
            self.قائمة_الفئة.addItem("اختر الفئة...", 0)

            if الفئات:
                for فئة in الفئات:
                    self.قائمة_الفئة.addItem(فئة[1], فئة[0])

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الفئات: {str(e)}")

    def تبديل_الصلاحية(self, checked):
        """
        دالة تبديل حالة الصلاحية
        """
        self.حقل_تاريخ_الصلاحية.setEnabled(checked)

    def تحميل_بيانات_المنتج(self):
        """
        دالة تحميل بيانات المنتج للتعديل
        """
        try:
            استعلام = """
            SELECT الباركود, الاسم_العام, الاسم_التجاري, رقم_الفئة, الوحدة,
                   سعر_الشراء, سعر_البيع, سعر_البيع_أقساط, الكمية_الحالية,
                   الحد_الأدنى, يخضع_للصلاحية, تاريخ_الصلاحية, حالة_النشاط
            FROM المنتجات
            WHERE الباركود = %s
            """

            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.باركود_المنتج,))

            if نتيجة:
                بيانات = نتيجة[0]

                self.حقل_الباركود.setText(بيانات[0] or "")
                self.حقل_الاسم_العام.setText(بيانات[1] or "")
                self.حقل_الاسم_التجاري.setText(بيانات[2] or "")

                # تحديد الفئة
                if بيانات[3]:
                    فهرس = self.قائمة_الفئة.findData(بيانات[3])
                    if فهرس >= 0:
                        self.قائمة_الفئة.setCurrentIndex(فهرس)

                # تحديد الوحدة
                if بيانات[4]:
                    فهرس = self.قائمة_الوحدة.findText(بيانات[4])
                    if فهرس >= 0:
                        self.قائمة_الوحدة.setCurrentIndex(فهرس)
                    else:
                        self.قائمة_الوحدة.setCurrentText(بيانات[4])

                self.حقل_سعر_الشراء.setValue(float(بيانات[5]) if بيانات[5] else 0)
                self.حقل_سعر_البيع.setValue(float(بيانات[6]) if بيانات[6] else 0)
                self.حقل_سعر_البيع_أقساط.setValue(float(بيانات[7]) if بيانات[7] else 0)
                self.حقل_الكمية_الحالية.setValue(int(بيانات[8]) if بيانات[8] else 0)
                self.حقل_الحد_الأدنى.setValue(int(بيانات[9]) if بيانات[9] else 0)

                self.مربع_يخضع_للصلاحية.setChecked(bool(بيانات[10]))
                if بيانات[11]:
                    self.حقل_تاريخ_الصلاحية.setDate(QDate.fromString(str(بيانات[11]), "yyyy-MM-dd"))

                self.مربع_نشط.setChecked(bool(بيانات[12]))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المنتج: {str(e)}")

    def إنشاء_باركود_جديد(self):
        """
        دالة إنشاء باركود جديد
        """
        try:
            استعلام = "SELECT MAX(CAST(الباركود AS UNSIGNED)) FROM المنتجات WHERE الباركود REGEXP '^[0-9]+$'"
            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if نتيجة and نتيجة[0][0]:
                آخر_رقم = int(نتيجة[0][0])
                باركود_جديد = str(آخر_رقم + 1).zfill(4)
            else:
                باركود_جديد = "1001"

            return باركود_جديد

        except:
            return "1001"

    def حفظ_المنتج(self):
        """
        دالة حفظ بيانات المنتج
        """
        # التحقق من البيانات المطلوبة
        if not self.حقل_الاسم_العام.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم العام للمنتج")
            self.حقل_الاسم_العام.setFocus()
            return

        if self.حقل_سعر_البيع.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر البيع")
            self.حقل_سعر_البيع.setFocus()
            return

        try:
            # جمع البيانات
            باركود = self.حقل_الباركود.text().strip()
            if not باركود:
                باركود = self.إنشاء_باركود_جديد()

            الاسم_العام = self.حقل_الاسم_العام.text().strip()
            الاسم_التجاري = self.حقل_الاسم_التجاري.text().strip() or None
            رقم_الفئة = self.قائمة_الفئة.currentData() if self.قائمة_الفئة.currentData() != 0 else None
            الوحدة = self.قائمة_الوحدة.currentText()
            سعر_الشراء = self.حقل_سعر_الشراء.value()
            سعر_البيع = self.حقل_سعر_البيع.value()
            سعر_البيع_أقساط = self.حقل_سعر_البيع_أقساط.value() or None
            الكمية_الحالية = self.حقل_الكمية_الحالية.value()
            الحد_الأدنى = self.حقل_الحد_الأدنى.value()
            يخضع_للصلاحية = self.مربع_يخضع_للصلاحية.isChecked()
            تاريخ_الصلاحية = self.حقل_تاريخ_الصلاحية.date().toString("yyyy-MM-dd") if يخضع_للصلاحية else None
            حالة_النشاط = self.مربع_نشط.isChecked()

            if self.نوع_العملية == "إضافة":
                # التحقق من عدم تكرار الباركود
                استعلام_تحقق = "SELECT COUNT(*) FROM المنتجات WHERE الباركود = %s"
                نتيجة_تحقق = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تحقق, (باركود,))

                if نتيجة_تحقق and نتيجة_تحقق[0][0] > 0:
                    QMessageBox.warning(self, "تحذير", f"الباركود {باركود} موجود مسبقاً")
                    self.حقل_الباركود.setFocus()
                    return

                # إضافة منتج جديد
                استعلام = """
                INSERT INTO المنتجات
                (الباركود, الاسم_العام, الاسم_التجاري, رقم_الفئة, الوحدة,
                 سعر_الشراء, سعر_البيع, سعر_البيع_أقساط, الكمية_الحالية,
                 الحد_الأدنى, يخضع_للصلاحية, تاريخ_الصلاحية, حالة_النشاط)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                معاملات = (باركود, الاسم_العام, الاسم_التجاري, رقم_الفئة, الوحدة,
                          سعر_الشراء, سعر_البيع, سعر_البيع_أقساط, الكمية_الحالية,
                          الحد_الأدنى, يخضع_للصلاحية, تاريخ_الصلاحية, حالة_النشاط)

            else:
                # تعديل منتج موجود
                استعلام = """
                UPDATE المنتجات SET
                الباركود = %s, الاسم_العام = %s, الاسم_التجاري = %s, رقم_الفئة = %s,
                الوحدة = %s, سعر_الشراء = %s, سعر_البيع = %s, سعر_البيع_أقساط = %s,
                الكمية_الحالية = %s, الحد_الأدنى = %s, يخضع_للصلاحية = %s,
                تاريخ_الصلاحية = %s, حالة_النشاط = %s
                WHERE الباركود = %s
                """

                معاملات = (باركود, الاسم_العام, الاسم_التجاري, رقم_الفئة, الوحدة,
                          سعر_الشراء, سعر_البيع, سعر_البيع_أقساط, الكمية_الحالية,
                          الحد_الأدنى, يخضع_للصلاحية, تاريخ_الصلاحية, حالة_النشاط,
                          self.باركود_المنتج)

            self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            QMessageBox.information(self, "نجح", f"تم {self.نوع_العملية} المنتج بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المنتج: {str(e)}")

class نافذة_مورد(QDialog):
    """
    نافذة إضافة أو تعديل مورد
    """

    def __init__(self, parent, نوع_العملية, رقم_المورد=None):
        """
        دالة التهيئة لنافذة المورد
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_المورد = رقم_المورد

        self.إعداد_النافذة()
        self.إنشاء_النموذج()

        if نوع_العملية == "تعديل" and رقم_المورد:
            self.تحميل_بيانات_المورد()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} مورد")
        self.setFixedSize(500, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج بيانات المورد
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} مورد")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # نموذج البيانات
        نموذج = QFormLayout()
        نموذج.setSpacing(10)

        # اسم المورد
        self.حقل_اسم_المورد = QLineEdit()
        self.حقل_اسم_المورد.setPlaceholderText("أدخل اسم المورد")
        نموذج.addRow("اسم المورد *:", self.حقل_اسم_المورد)

        # العنوان
        self.حقل_العنوان = QLineEdit()
        self.حقل_العنوان.setPlaceholderText("أدخل عنوان المورد")
        نموذج.addRow("العنوان:", self.حقل_العنوان)

        # رقم الهاتف
        self.حقل_رقم_الهاتف = QLineEdit()
        self.حقل_رقم_الهاتف.setPlaceholderText("05xxxxxxxx")
        نموذج.addRow("رقم الهاتف:", self.حقل_رقم_الهاتف)

        # البريد الإلكتروني
        self.حقل_البريد_الإلكتروني = QLineEdit()
        self.حقل_البريد_الإلكتروني.setPlaceholderText("<EMAIL>")
        نموذج.addRow("البريد الإلكتروني:", self.حقل_البريد_الإلكتروني)

        # الرصيد الحالي
        self.حقل_الرصيد_الحالي = QDoubleSpinBox()
        self.حقل_الرصيد_الحالي.setRange(-999999.99, 999999.99)
        self.حقل_الرصيد_الحالي.setSuffix(" ريال")
        self.حقل_الرصيد_الحالي.setToolTip("الرصيد الموجب يعني دين على الشركة، والرصيد السالب يعني دين للشركة")
        نموذج.addRow("الرصيد الحالي:", self.حقل_الرصيد_الحالي)

        # حالة النشاط
        self.مربع_نشط = QCheckBox("مورد نشط")
        self.مربع_نشط.setChecked(True)
        نموذج.addRow("الحالة:", self.مربع_نشط)



        التخطيط_الرئيسي.addLayout(نموذج)

        # أزرار الحفظ والإلغاء
        تخطيط_أزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        زر_حفظ.clicked.connect(self.حفظ_المورد)

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        زر_إلغاء.clicked.connect(self.reject)

        تخطيط_أزرار.addWidget(زر_حفظ)
        تخطيط_أزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_أزرار)

    def تحميل_بيانات_المورد(self):
        """
        دالة تحميل بيانات المورد للتعديل
        """
        try:
            استعلام = """
            SELECT اسم_المورد, العنوان, رقم_الهاتف, البريد_الإلكتروني,
                   الرصيد_الحالي, حالة_النشاط
            FROM الموردين
            WHERE رقم_المورد = %s
            """

            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.رقم_المورد,))

            if نتيجة:
                بيانات = نتيجة[0]

                self.حقل_اسم_المورد.setText(بيانات[0] or "")
                self.حقل_العنوان.setText(بيانات[1] or "")
                self.حقل_رقم_الهاتف.setText(بيانات[2] or "")
                self.حقل_البريد_الإلكتروني.setText(بيانات[3] or "")
                self.حقل_الرصيد_الحالي.setValue(float(بيانات[4]) if بيانات[4] else 0)
                self.مربع_نشط.setChecked(بيانات[5] == "نشط")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المورد: {str(e)}")

    def حفظ_المورد(self):
        """
        دالة حفظ بيانات المورد
        """
        # التحقق من البيانات المطلوبة
        if not self.حقل_اسم_المورد.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المورد")
            self.حقل_اسم_المورد.setFocus()
            return

        try:
            # جمع البيانات
            اسم_المورد = self.حقل_اسم_المورد.text().strip()
            العنوان = self.حقل_العنوان.text().strip() or None
            رقم_الهاتف = self.حقل_رقم_الهاتف.text().strip() or None
            البريد_الإلكتروني = self.حقل_البريد_الإلكتروني.text().strip() or None
            الرصيد_الحالي = self.حقل_الرصيد_الحالي.value()
            حالة_النشاط = "نشط" if self.مربع_نشط.isChecked() else "غير نشط"

            if self.نوع_العملية == "إضافة":
                # إضافة مورد جديد
                استعلام = """
                INSERT INTO الموردين
                (اسم_المورد, العنوان, رقم_الهاتف, البريد_الإلكتروني,
                 الرصيد_الحالي, حالة_النشاط, تاريخ_الإضافة)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """

                معاملات = (اسم_المورد, العنوان, رقم_الهاتف, البريد_الإلكتروني,
                          الرصيد_الحالي, حالة_النشاط)

            else:
                # تعديل مورد موجود
                استعلام = """
                UPDATE الموردين SET
                اسم_المورد = %s, العنوان = %s, رقم_الهاتف = %s,
                البريد_الإلكتروني = %s, الرصيد_الحالي = %s,
                حالة_النشاط = %s
                WHERE رقم_المورد = %s
                """

                معاملات = (اسم_المورد, العنوان, رقم_الهاتف, البريد_الإلكتروني,
                          الرصيد_الحالي, حالة_النشاط, self.رقم_المورد)

            self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            QMessageBox.information(self, "نجح", f"تم {self.نوع_العملية} المورد بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المورد: {str(e)}")

class نافذة_فئة(QDialog):
    """
    نافذة إضافة أو تعديل فئة
    """

    def __init__(self, parent, نوع_العملية, رقم_الفئة=None):
        """
        دالة التهيئة لنافذة الفئة
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_الفئة = رقم_الفئة

        self.إعداد_النافذة()
        self.إنشاء_النموذج()

        if نوع_العملية == "تعديل" and رقم_الفئة:
            self.تحميل_بيانات_الفئة()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} فئة")
        self.setFixedSize(400, 350)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج بيانات الفئة
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} فئة")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # نموذج البيانات
        نموذج = QFormLayout()
        نموذج.setSpacing(10)

        # اسم الفئة
        self.حقل_اسم_الفئة = QLineEdit()
        self.حقل_اسم_الفئة.setPlaceholderText("أدخل اسم الفئة")
        نموذج.addRow("اسم الفئة *:", self.حقل_اسم_الفئة)

        # الوصف
        self.حقل_الوصف = QTextEdit()
        self.حقل_الوصف.setMaximumHeight(100)
        self.حقل_الوصف.setPlaceholderText("وصف الفئة (اختياري)")
        نموذج.addRow("الوصف:", self.حقل_الوصف)

        # حالة النشاط
        self.مربع_نشط = QCheckBox("فئة نشطة")
        self.مربع_نشط.setChecked(True)
        نموذج.addRow("الحالة:", self.مربع_نشط)

        التخطيط_الرئيسي.addLayout(نموذج)

        # أزرار الحفظ والإلغاء
        تخطيط_أزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        زر_حفظ.clicked.connect(self.حفظ_الفئة)

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        زر_إلغاء.clicked.connect(self.reject)

        تخطيط_أزرار.addWidget(زر_حفظ)
        تخطيط_أزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_أزرار)

    def تحميل_بيانات_الفئة(self):
        """
        دالة تحميل بيانات الفئة للتعديل
        """
        try:
            استعلام = """
            SELECT اسم_الفئة, وصف_الفئة, حالة_النشاط
            FROM الفئات
            WHERE رقم_الفئة = %s
            """

            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.رقم_الفئة,))

            if نتيجة:
                بيانات = نتيجة[0]

                self.حقل_اسم_الفئة.setText(بيانات[0] or "")
                self.حقل_الوصف.setPlainText(بيانات[1] or "")
                self.مربع_نشط.setChecked(bool(بيانات[2]))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الفئة: {str(e)}")

    def حفظ_الفئة(self):
        """
        دالة حفظ بيانات الفئة
        """
        # التحقق من البيانات المطلوبة
        if not self.حقل_اسم_الفئة.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الفئة")
            self.حقل_اسم_الفئة.setFocus()
            return

        try:
            # جمع البيانات
            اسم_الفئة = self.حقل_اسم_الفئة.text().strip()
            وصف_الفئة = self.حقل_الوصف.toPlainText().strip() or None
            حالة_النشاط = self.مربع_نشط.isChecked()

            if self.نوع_العملية == "إضافة":
                # التحقق من عدم تكرار اسم الفئة
                استعلام_تحقق = "SELECT COUNT(*) FROM الفئات WHERE اسم_الفئة = %s"
                نتيجة_تحقق = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تحقق, (اسم_الفئة,))

                if نتيجة_تحقق and نتيجة_تحقق[0][0] > 0:
                    QMessageBox.warning(self, "تحذير", f"اسم الفئة '{اسم_الفئة}' موجود مسبقاً")
                    self.حقل_اسم_الفئة.setFocus()
                    return

                # إضافة فئة جديدة
                استعلام = """
                INSERT INTO الفئات
                (اسم_الفئة, وصف_الفئة, حالة_النشاط, تاريخ_الإنشاء)
                VALUES (%s, %s, %s, NOW())
                """

                معاملات = (اسم_الفئة, وصف_الفئة, حالة_النشاط)

            else:
                # تعديل فئة موجودة
                استعلام = """
                UPDATE الفئات SET
                اسم_الفئة = %s, وصف_الفئة = %s, حالة_النشاط = %s
                WHERE رقم_الفئة = %s
                """

                معاملات = (اسم_الفئة, وصف_الفئة, حالة_النشاط, self.رقم_الفئة)

            self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            QMessageBox.information(self, "نجح", f"تم {self.نوع_العملية} الفئة بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الفئة: {str(e)}")

class نافذة_فاتورة_شراء(QDialog):
    """
    نافذة إضافة أو تعديل فاتورة شراء
    """

    def __init__(self, parent, نوع_العملية, رقم_الفاتورة=None):
        """
        دالة التهيئة لنافذة فاتورة الشراء
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_الفاتورة = رقم_الفاتورة
        self.تفاصيل_الفاتورة = []

        self.إعداد_النافذة()
        self.إنشاء_النموذج()
        self.تحميل_الموردين()

        if نوع_العملية == "تعديل" and رقم_الفاتورة:
            self.تحميل_بيانات_الفاتورة()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} فاتورة شراء")
        self.setFixedSize(900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج فاتورة الشراء
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} فاتورة شراء")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # معلومات الفاتورة الأساسية
        إطار_معلومات = QFrame()
        إطار_معلومات.setStyleSheet("border: 1px solid #bdc3c7; border-radius: 8px; padding: 10px;")
        تخطيط_معلومات = QFormLayout(إطار_معلومات)

        # المورد
        self.قائمة_المورد = QComboBox()
        self.قائمة_المورد.setFixedHeight(35)
        تخطيط_معلومات.addRow("المورد *:", self.قائمة_المورد)

        # تاريخ الفاتورة
        self.تاريخ_الفاتورة = QDateEdit()
        self.تاريخ_الفاتورة.setDate(QDate.currentDate())
        self.تاريخ_الفاتورة.setFixedHeight(35)
        تخطيط_معلومات.addRow("تاريخ الفاتورة:", self.تاريخ_الفاتورة)

        # ملاحظات
        self.حقل_ملاحظات = QTextEdit()
        self.حقل_ملاحظات.setMaximumHeight(80)
        self.حقل_ملاحظات.setPlaceholderText("ملاحظات الفاتورة...")
        تخطيط_معلومات.addRow("ملاحظات:", self.حقل_ملاحظات)

        التخطيط_الرئيسي.addWidget(إطار_معلومات)

        # تفاصيل الفاتورة
        تسمية_تفاصيل = QLabel("تفاصيل الفاتورة:")
        تسمية_تفاصيل.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50;")
        التخطيط_الرئيسي.addWidget(تسمية_تفاصيل)

        # أزرار إضافة المنتجات
        إطار_أزرار_منتجات = QFrame()
        تخطيط_أزرار_منتجات = QHBoxLayout(إطار_أزرار_منتجات)

        زر_إضافة_منتج = QPushButton("➕ إضافة منتج")
        زر_إضافة_منتج.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        زر_إضافة_منتج.clicked.connect(self.إضافة_منتج_للفاتورة)

        زر_حذف_منتج = QPushButton("🗑️ حذف منتج")
        زر_حذف_منتج.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        زر_حذف_منتج.clicked.connect(self.حذف_منتج_من_الفاتورة)

        تخطيط_أزرار_منتجات.addWidget(زر_إضافة_منتج)
        تخطيط_أزرار_منتجات.addWidget(زر_حذف_منتج)
        تخطيط_أزرار_منتجات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_أزرار_منتجات)

        # جدول تفاصيل الفاتورة
        self.جدول_التفاصيل = QTableWidget()
        self.جدول_التفاصيل.setColumnCount(5)
        self.جدول_التفاصيل.setHorizontalHeaderLabels([
            "الباركود", "اسم المنتج", "الكمية", "سعر الوحدة", "الإجمالي"
        ])

        header = self.جدول_التفاصيل.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.جدول_التفاصيل.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_التفاصيل.setAlternatingRowColors(True)

        التخطيط_الرئيسي.addWidget(self.جدول_التفاصيل)

        # إجمالي الفاتورة
        إطار_إجمالي = QFrame()
        إطار_إجمالي.setStyleSheet("border: 1px solid #bdc3c7; border-radius: 8px; padding: 10px; background-color: #f8f9fa;")
        تخطيط_إجمالي = QHBoxLayout(إطار_إجمالي)

        تسمية_إجمالي = QLabel("إجمالي الفاتورة:")
        تسمية_إجمالي.setStyleSheet("font-weight: bold; font-size: 16px;")

        self.تسمية_المبلغ = QLabel("0.00 ريال")
        self.تسمية_المبلغ.setStyleSheet("font-weight: bold; font-size: 18px; color: #e67e22;")

        تخطيط_إجمالي.addWidget(تسمية_إجمالي)
        تخطيط_إجمالي.addStretch()
        تخطيط_إجمالي.addWidget(self.تسمية_المبلغ)

        التخطيط_الرئيسي.addWidget(إطار_إجمالي)

        # أزرار الحفظ والإلغاء
        تخطيط_أزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ الفاتورة")
        زر_حفظ.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 25px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        زر_حفظ.clicked.connect(self.حفظ_الفاتورة)

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 25px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        زر_إلغاء.clicked.connect(self.reject)

        تخطيط_أزرار.addWidget(زر_حفظ)
        تخطيط_أزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_أزرار)

    def تحميل_الموردين(self):
        """
        دالة تحميل قائمة الموردين
        """
        try:
            استعلام = "SELECT رقم_المورد, اسم_المورد FROM الموردين WHERE حالة_النشاط = 'نشط' ORDER BY اسم_المورد"
            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_المورد.clear()
            self.قائمة_المورد.addItem("اختر المورد...", 0)

            if الموردين:
                for مورد in الموردين:
                    self.قائمة_المورد.addItem(مورد[1], مورد[0])

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين: {str(e)}")

    def إضافة_منتج_للفاتورة(self):
        """
        دالة إضافة منتج لتفاصيل الفاتورة
        """
        نافذة_اختيار = نافذة_اختيار_منتج(self)
        if نافذة_اختيار.exec_() == QDialog.Accepted:
            منتج = نافذة_اختيار.المنتج_المختار
            كمية = نافذة_اختيار.الكمية_المختارة
            سعر = نافذة_اختيار.السعر_المختار

            if منتج and كمية > 0 and سعر > 0:
                # التحقق من عدم تكرار المنتج
                for تفصيل in self.تفاصيل_الفاتورة:
                    if تفصيل['الباركود'] == منتج['الباركود']:
                        QMessageBox.warning(self, "تحذير", "هذا المنتج موجود مسبقاً في الفاتورة")
                        return

                إجمالي = كمية * سعر
                تفصيل = {
                    'الباركود': منتج['الباركود'],
                    'اسم_المنتج': منتج['الاسم_العام'],
                    'الكمية': كمية,
                    'سعر_الوحدة': سعر,
                    'الإجمالي': إجمالي
                }

                self.تفاصيل_الفاتورة.append(تفصيل)
                self.تحديث_جدول_التفاصيل()
                self.حساب_الإجمالي()

    def حذف_منتج_من_الفاتورة(self):
        """
        دالة حذف منتج من تفاصيل الفاتورة
        """
        صف_محدد = self.جدول_التفاصيل.currentRow()

        if صف_محدد >= 0:
            رد = QMessageBox.question(self, "تأكيد الحذف",
                                      "هل تريد حذف هذا المنتج من الفاتورة؟",
                                      QMessageBox.Yes | QMessageBox.No)

            if رد == QMessageBox.Yes:
                del self.تفاصيل_الفاتورة[صف_محدد]
                self.تحديث_جدول_التفاصيل()
                self.حساب_الإجمالي()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")

    def تحديث_جدول_التفاصيل(self):
        """
        دالة تحديث جدول تفاصيل الفاتورة
        """
        self.جدول_التفاصيل.setRowCount(len(self.تفاصيل_الفاتورة))

        for صف, تفصيل in enumerate(self.تفاصيل_الفاتورة):
            self.جدول_التفاصيل.setItem(صف, 0, QTableWidgetItem(تفصيل['الباركود']))
            self.جدول_التفاصيل.setItem(صف, 1, QTableWidgetItem(تفصيل['اسم_المنتج']))
            self.جدول_التفاصيل.setItem(صف, 2, QTableWidgetItem(str(تفصيل['الكمية'])))
            self.جدول_التفاصيل.setItem(صف, 3, QTableWidgetItem(f"{تفصيل['سعر_الوحدة']:.2f}"))
            self.جدول_التفاصيل.setItem(صف, 4, QTableWidgetItem(f"{تفصيل['الإجمالي']:.2f}"))

            # توسيط النص
            for عمود in range(5):
                if self.جدول_التفاصيل.item(صف, عمود):
                    self.جدول_التفاصيل.item(صف, عمود).setTextAlignment(Qt.AlignCenter)

    def حساب_الإجمالي(self):
        """
        دالة حساب إجمالي الفاتورة
        """
        إجمالي = sum(تفصيل['الإجمالي'] for تفصيل in self.تفاصيل_الفاتورة)
        self.تسمية_المبلغ.setText(f"{إجمالي:.2f} ريال")

    def حفظ_الفاتورة(self):
        """
        دالة حفظ فاتورة الشراء
        """
        # التحقق من البيانات
        if self.قائمة_المورد.currentData() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            return

        if not self.تفاصيل_الفاتورة:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة منتجات للفاتورة")
            return

        try:
            رقم_المورد = self.قائمة_المورد.currentData()
            تاريخ_الفاتورة = self.تاريخ_الفاتورة.date().toString("yyyy-MM-dd")
            إجمالي_الفاتورة = sum(تفصيل['الإجمالي'] for تفصيل in self.تفاصيل_الفاتورة)
            ملاحظات = self.حقل_ملاحظات.toPlainText().strip() or None

            if self.نوع_العملية == "إضافة":
                # إضافة فاتورة جديدة
                استعلام_فاتورة = """
                INSERT INTO فواتير_الشراء
                (رقم_المورد, تاريخ_الفاتورة, إجمالي_الفاتورة, ملاحظات)
                VALUES (%s, %s, %s, %s)
                """

                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة,
                    (رقم_المورد, تاريخ_الفاتورة, إجمالي_الفاتورة, ملاحظات))

                # الحصول على رقم الفاتورة الجديد
                رقم_الفاتورة = self.قاعدة_البيانات.cursor.lastrowid

            else:
                # تعديل فاتورة موجودة
                استعلام_فاتورة = """
                UPDATE فواتير_الشراء SET
                رقم_المورد = %s, تاريخ_الفاتورة = %s,
                إجمالي_الفاتورة = %s, ملاحظات = %s
                WHERE رقم_الفاتورة = %s
                """

                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة,
                    (رقم_المورد, تاريخ_الفاتورة, إجمالي_الفاتورة, ملاحظات, self.رقم_الفاتورة))

                رقم_الفاتورة = self.رقم_الفاتورة

                # حذف التفاصيل القديمة
                استعلام_حذف = "DELETE FROM تفاصيل_فواتير_الشراء WHERE رقم_الفاتورة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_حذف, (رقم_الفاتورة,))

            # إضافة تفاصيل الفاتورة
            for تفصيل in self.تفاصيل_الفاتورة:
                استعلام_تفصيل = """
                INSERT INTO تفاصيل_فواتير_الشراء
                (رقم_الفاتورة, الباركود, الكمية, سعر_الوحدة, الإجمالي)
                VALUES (%s, %s, %s, %s, %s)
                """

                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تفصيل, (
                    رقم_الفاتورة,
                    تفصيل['الباركود'],
                    تفصيل['الكمية'],
                    تفصيل['سعر_الوحدة'],
                    تفصيل['الإجمالي']
                ))

            QMessageBox.information(self, "نجح", f"تم {self.نوع_العملية} فاتورة الشراء بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الفاتورة: {str(e)}")

    def تحميل_بيانات_الفاتورة(self):
        """
        دالة تحميل بيانات الفاتورة للتعديل
        """
        try:
            # تحميل بيانات الفاتورة الأساسية
            استعلام_فاتورة = """
            SELECT رقم_المورد, تاريخ_الفاتورة, ملاحظات
            FROM فواتير_الشراء
            WHERE رقم_الفاتورة = %s
            """

            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة, (self.رقم_الفاتورة,))

            if نتيجة:
                بيانات = نتيجة[0]

                # تحديد المورد
                فهرس = self.قائمة_المورد.findData(بيانات[0])
                if فهرس >= 0:
                    self.قائمة_المورد.setCurrentIndex(فهرس)

                # تحديد التاريخ
                self.تاريخ_الفاتورة.setDate(QDate.fromString(str(بيانات[1]), "yyyy-MM-dd"))

                # الملاحظات
                if بيانات[2]:
                    self.حقل_ملاحظات.setPlainText(بيانات[2])

            # تحميل تفاصيل الفاتورة
            استعلام_تفاصيل = """
            SELECT d.الباركود, p.الاسم_العام, d.الكمية, d.سعر_الوحدة, d.الإجمالي
            FROM تفاصيل_فواتير_الشراء d
            LEFT JOIN المنتجات p ON d.الباركود = p.الباركود
            WHERE d.رقم_الفاتورة = %s
            """

            التفاصيل = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تفاصيل, (self.رقم_الفاتورة,))

            if التفاصيل:
                for تفصيل in التفاصيل:
                    تفصيل_فاتورة = {
                        'الباركود': تفصيل[0],
                        'اسم_المنتج': تفصيل[1] or "منتج محذوف",
                        'الكمية': int(تفصيل[2]),
                        'سعر_الوحدة': float(تفصيل[3]),
                        'الإجمالي': float(تفصيل[4])
                    }
                    self.تفاصيل_الفاتورة.append(تفصيل_فاتورة)

                self.تحديث_جدول_التفاصيل()
                self.حساب_الإجمالي()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الفاتورة: {str(e)}")

class نافذة_اختيار_منتج(QDialog):
    """
    نافذة اختيار منتج لإضافته لفاتورة الشراء
    """

    def __init__(self, parent):
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.المنتج_المختار = None
        self.الكمية_المختارة = 0
        self.السعر_المختار = 0

        self.إعداد_النافذة()
        self.إنشاء_النموذج()
        self.تحميل_المنتجات()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle("اختيار منتج")
        self.setFixedSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج اختيار المنتج
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel("اختيار منتج للفاتورة")
        عنوان.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 10px;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # البحث عن المنتج
        إطار_بحث = QFrame()
        تخطيط_بحث = QHBoxLayout(إطار_بحث)

        تسمية_بحث = QLabel("البحث:")
        self.حقل_بحث = QLineEdit()
        self.حقل_بحث.setPlaceholderText("اسم المنتج أو الباركود...")
        self.حقل_بحث.textChanged.connect(self.البحث_عن_المنتجات)

        تخطيط_بحث.addWidget(تسمية_بحث)
        تخطيط_بحث.addWidget(self.حقل_بحث)

        التخطيط_الرئيسي.addWidget(إطار_بحث)

        # جدول المنتجات
        self.جدول_المنتجات = QTableWidget()
        self.جدول_المنتجات.setColumnCount(4)
        self.جدول_المنتجات.setHorizontalHeaderLabels([
            "الباركود", "اسم المنتج", "الكمية المتاحة", "سعر الشراء"
        ])

        header = self.جدول_المنتجات.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.جدول_المنتجات.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_المنتجات.itemSelectionChanged.connect(self.تحديد_منتج)

        التخطيط_الرئيسي.addWidget(self.جدول_المنتجات)

        # تفاصيل المنتج المختار
        إطار_تفاصيل = QFrame()
        إطار_تفاصيل.setStyleSheet("border: 1px solid #bdc3c7; border-radius: 8px; padding: 10px;")
        تخطيط_تفاصيل = QFormLayout(إطار_تفاصيل)

        # الكمية
        self.حقل_الكمية = QSpinBox()
        self.حقل_الكمية.setMinimum(1)
        self.حقل_الكمية.setMaximum(9999)
        self.حقل_الكمية.setValue(1)
        تخطيط_تفاصيل.addRow("الكمية:", self.حقل_الكمية)

        # سعر الوحدة
        self.حقل_السعر = QDoubleSpinBox()
        self.حقل_السعر.setMaximum(999999.99)
        self.حقل_السعر.setSuffix(" ريال")
        تخطيط_تفاصيل.addRow("سعر الوحدة:", self.حقل_السعر)

        التخطيط_الرئيسي.addWidget(إطار_تفاصيل)

        # أزرار الإضافة والإلغاء
        تخطيط_أزرار = QHBoxLayout()

        زر_إضافة = QPushButton("إضافة للفاتورة")
        زر_إضافة.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        زر_إضافة.clicked.connect(self.إضافة_منتج)

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        زر_إلغاء.clicked.connect(self.reject)

        تخطيط_أزرار.addWidget(زر_إضافة)
        تخطيط_أزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_أزرار)

    def تحميل_المنتجات(self):
        """
        دالة تحميل جميع المنتجات
        """
        try:
            استعلام = """
            SELECT الباركود, الاسم_العام, الكمية_الحالية, سعر_الشراء
            FROM المنتجات
            WHERE حالة_النشاط = TRUE
            ORDER BY الاسم_العام
            """

            المنتجات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if المنتجات:
                self.جدول_المنتجات.setRowCount(len(المنتجات))

                for صف, منتج in enumerate(المنتجات):
                    for عمود, قيمة in enumerate(منتج):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_المنتجات.setItem(صف, عمود, عنصر)
            else:
                self.جدول_المنتجات.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المنتجات: {str(e)}")

    def البحث_عن_المنتجات(self):
        """
        دالة البحث عن المنتجات
        """
        نص_البحث = self.حقل_بحث.text().strip()

        try:
            if نص_البحث:
                استعلام = """
                SELECT الباركود, الاسم_العام, الكمية_الحالية, سعر_الشراء
                FROM المنتجات
                WHERE حالة_النشاط = TRUE AND (
                    الباركود LIKE %s OR الاسم_العام LIKE %s OR الاسم_التجاري LIKE %s
                )
                ORDER BY الاسم_العام
                """
                معاملات = [f"%{نص_البحث}%"] * 3
            else:
                استعلام = """
                SELECT الباركود, الاسم_العام, الكمية_الحالية, سعر_الشراء
                FROM المنتجات
                WHERE حالة_النشاط = TRUE
                ORDER BY الاسم_العام
                """
                معاملات = []

            المنتجات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_المنتجات.setRowCount(0)

            if المنتجات:
                self.جدول_المنتجات.setRowCount(len(المنتجات))

                for صف, منتج in enumerate(المنتجات):
                    for عمود, قيمة in enumerate(منتج):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_المنتجات.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_منتج(self):
        """
        دالة تحديد المنتج المختار
        """
        صف_محدد = self.جدول_المنتجات.currentRow()

        if صف_محدد >= 0:
            باركود = self.جدول_المنتجات.item(صف_محدد, 0).text()
            اسم_منتج = self.جدول_المنتجات.item(صف_محدد, 1).text()
            كمية_متاحة = int(self.جدول_المنتجات.item(صف_محدد, 2).text() or 0)
            سعر_شراء = float(self.جدول_المنتجات.item(صف_محدد, 3).text() or 0)

            self.المنتج_المختار = {
                'الباركود': باركود,
                'الاسم_العام': اسم_منتج,
                'الكمية_المتاحة': كمية_متاحة,
                'سعر_الشراء': سعر_شراء
            }

            # تحديث الحد الأقصى للكمية
            self.حقل_الكمية.setMaximum(max(1, كمية_متاحة + 1000))  # السماح بزيادة المخزون

            # تحديث السعر الافتراضي
            self.حقل_السعر.setValue(سعر_شراء)

    def إضافة_منتج(self):
        """
        دالة إضافة المنتج المختار
        """
        if not self.المنتج_المختار:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج")
            return

        if self.حقل_الكمية.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية صحيحة")
            return

        if self.حقل_السعر.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح")
            return

        self.الكمية_المختارة = self.حقل_الكمية.value()
        self.السعر_المختار = self.حقل_السعر.value()

        self.accept()

class نافذة_عرض_فاتورة_شراء(QDialog):
    """
    نافذة عرض تفاصيل فاتورة الشراء
    """

    def __init__(self, parent, رقم_الفاتورة):
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.رقم_الفاتورة = رقم_الفاتورة

        self.إعداد_النافذة()
        self.إنشاء_النموذج()
        self.تحميل_بيانات_الفاتورة()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"عرض فاتورة شراء رقم {self.رقم_الفاتورة}")
        self.setFixedSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج عرض الفاتورة
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان الفاتورة
        عنوان = QLabel(f"فاتورة شراء رقم {self.رقم_الفاتورة}")
        عنوان.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; padding: 15px; text-align: center;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # معلومات الفاتورة
        إطار_معلومات = QFrame()
        إطار_معلومات.setStyleSheet("""
            QFrame {
                border: 2px solid #3498db;
                border-radius: 10px;
                padding: 15px;
                background-color: #f8f9fa;
            }
        """)
        تخطيط_معلومات = QGridLayout(إطار_معلومات)

        # تسميات المعلومات
        self.تسمية_المورد = QLabel()
        self.تسمية_التاريخ = QLabel()
        self.تسمية_الإجمالي = QLabel()
        self.تسمية_المدفوع = QLabel()
        self.تسمية_المتبقي = QLabel()
        self.تسمية_الحالة = QLabel()
        self.تسمية_الملاحظات = QLabel()

        # إضافة التسميات للشبكة
        تخطيط_معلومات.addWidget(QLabel("المورد:"), 0, 0)
        تخطيط_معلومات.addWidget(self.تسمية_المورد, 0, 1)

        تخطيط_معلومات.addWidget(QLabel("تاريخ الفاتورة:"), 0, 2)
        تخطيط_معلومات.addWidget(self.تسمية_التاريخ, 0, 3)

        تخطيط_معلومات.addWidget(QLabel("إجمالي الفاتورة:"), 1, 0)
        تخطيط_معلومات.addWidget(self.تسمية_الإجمالي, 1, 1)

        تخطيط_معلومات.addWidget(QLabel("المبلغ المدفوع:"), 1, 2)
        تخطيط_معلومات.addWidget(self.تسمية_المدفوع, 1, 3)

        تخطيط_معلومات.addWidget(QLabel("المبلغ المتبقي:"), 2, 0)
        تخطيط_معلومات.addWidget(self.تسمية_المتبقي, 2, 1)

        تخطيط_معلومات.addWidget(QLabel("حالة الدفع:"), 2, 2)
        تخطيط_معلومات.addWidget(self.تسمية_الحالة, 2, 3)

        تخطيط_معلومات.addWidget(QLabel("ملاحظات:"), 3, 0)
        تخطيط_معلومات.addWidget(self.تسمية_الملاحظات, 3, 1, 1, 3)

        التخطيط_الرئيسي.addWidget(إطار_معلومات)

        # تفاصيل الفاتورة
        تسمية_تفاصيل = QLabel("تفاصيل الفاتورة:")
        تسمية_تفاصيل.setStyleSheet("font-weight: bold; font-size: 16px; color: #2c3e50; margin-top: 10px;")
        التخطيط_الرئيسي.addWidget(تسمية_تفاصيل)

        # جدول التفاصيل
        self.جدول_التفاصيل = QTableWidget()
        self.جدول_التفاصيل.setColumnCount(5)
        self.جدول_التفاصيل.setHorizontalHeaderLabels([
            "الباركود", "اسم المنتج", "الكمية", "سعر الوحدة", "الإجمالي"
        ])

        header = self.جدول_التفاصيل.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.جدول_التفاصيل.setAlternatingRowColors(True)
        self.جدول_التفاصيل.setEditTriggers(QAbstractItemView.NoEditTriggers)

        التخطيط_الرئيسي.addWidget(self.جدول_التفاصيل)

        # أزرار الإجراءات
        تخطيط_أزرار = QHBoxLayout()

        زر_طباعة = QPushButton("🖨️ طباعة")
        زر_طباعة.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        زر_طباعة.clicked.connect(self.طباعة_الفاتورة)

        زر_إغلاق = QPushButton("إغلاق")
        زر_إغلاق.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        زر_إغلاق.clicked.connect(self.accept)

        تخطيط_أزرار.addWidget(زر_طباعة)
        تخطيط_أزرار.addStretch()
        تخطيط_أزرار.addWidget(زر_إغلاق)

        التخطيط_الرئيسي.addLayout(تخطيط_أزرار)

    def تحميل_بيانات_الفاتورة(self):
        """
        دالة تحميل بيانات الفاتورة
        """
        try:
            # تحميل بيانات الفاتورة الأساسية
            استعلام_فاتورة = """
            SELECT f.رقم_الفاتورة, s.اسم_المورد, f.تاريخ_الفاتورة,
                   f.إجمالي_الفاتورة, f.المبلغ_المدفوع, f.حالة_الدفع, f.ملاحظات
            FROM فواتير_الشراء f
            LEFT JOIN الموردين s ON f.رقم_المورد = s.رقم_المورد
            WHERE f.رقم_الفاتورة = %s
            """

            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة, (self.رقم_الفاتورة,))

            if نتيجة:
                بيانات = نتيجة[0]

                self.تسمية_المورد.setText(بيانات[1] or "غير محدد")
                self.تسمية_التاريخ.setText(str(بيانات[2]))
                self.تسمية_الإجمالي.setText(f"{float(بيانات[3]):.2f} ريال")
                self.تسمية_المدفوع.setText(f"{float(بيانات[4] or 0):.2f} ريال")

                متبقي = float(بيانات[3]) - float(بيانات[4] or 0)
                self.تسمية_المتبقي.setText(f"{متبقي:.2f} ريال")

                حالة = بيانات[5] or "غير مدفوع"
                لون_حالة = {
                    "مدفوع": "#27ae60",
                    "مدفوع جزئياً": "#f39c12",
                    "غير مدفوع": "#e74c3c"
                }.get(حالة, "#95a5a6")

                self.تسمية_الحالة.setText(حالة)
                self.تسمية_الحالة.setStyleSheet(f"color: {لون_حالة}; font-weight: bold;")

                self.تسمية_الملاحظات.setText(بيانات[6] or "لا توجد ملاحظات")

            # تحميل تفاصيل الفاتورة
            استعلام_تفاصيل = """
            SELECT d.الباركود, p.الاسم_العام, d.الكمية, d.سعر_الوحدة, d.الإجمالي
            FROM تفاصيل_فواتير_الشراء d
            LEFT JOIN المنتجات p ON d.الباركود = p.الباركود
            WHERE d.رقم_الفاتورة = %s
            ORDER BY d.رقم_التفصيل
            """

            التفاصيل = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تفاصيل, (self.رقم_الفاتورة,))

            if التفاصيل:
                self.جدول_التفاصيل.setRowCount(len(التفاصيل))

                for صف, تفصيل in enumerate(التفاصيل):
                    self.جدول_التفاصيل.setItem(صف, 0, QTableWidgetItem(تفصيل[0]))
                    self.جدول_التفاصيل.setItem(صف, 1, QTableWidgetItem(تفصيل[1] or "منتج محذوف"))
                    self.جدول_التفاصيل.setItem(صف, 2, QTableWidgetItem(str(تفصيل[2])))
                    self.جدول_التفاصيل.setItem(صف, 3, QTableWidgetItem(f"{float(تفصيل[3]):.2f}"))
                    self.جدول_التفاصيل.setItem(صف, 4, QTableWidgetItem(f"{float(تفصيل[4]):.2f}"))

                    # توسيط النص
                    for عمود in range(5):
                        if self.جدول_التفاصيل.item(صف, عمود):
                            self.جدول_التفاصيل.item(صف, عمود).setTextAlignment(Qt.AlignCenter)
            else:
                self.جدول_التفاصيل.setRowCount(0)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الفاتورة: {str(e)}")

    def طباعة_الفاتورة(self):
        """
        دالة طباعة الفاتورة
        """
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")

class تبويب_الفئات(QWidget):
    """تبويب الفئات"""

    def __init__(self, قاعدة_البيانات):
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.الفئة_المحددة = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب الفئات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # شريط البحث
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول الفئات
        self.إنشاء_جدول_الفئات(التخطيط_الرئيسي)

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("اسم الفئة أو الوصف...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الفئات)

        # فلتر الحالة
        تسمية_حالة = QLabel("الحالة:")
        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "نشط", "غير نشط"])
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_الفئات)

        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث, 2)
        تخطيط_البحث.addWidget(تسمية_حالة)
        تخطيط_البحث.addWidget(self.قائمة_حالة)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

        # تطبيق أنماط إطار البحث
        إطار_البحث.setStyleSheet("""
            QFrame#search_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط أزرار الإجراءات
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(80)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(15)

        # أزرار الإجراءات
        أزرار = [
            ("➕", "إضافة فئة", "add_category", "#27ae60", self.إضافة_فئة),
            ("✏️", "تعديل", "edit_category", "#3498db", self.تعديل_فئة),
            ("🗑️", "حذف", "delete_category", "#e74c3c", self.حذف_فئة),
            ("📊", "إحصائيات", "category_stats", "#9b59b6", self.إحصائيات_فئة),
            ("🖨️", "طباعة", "print_categories", "#7f8c8d", self.طباعة_الفئات)
        ]

        self.أزرار_الإجراءات = {}

        for أيقونة, نص, مفتاح, لون, وظيفة in أزرار:
            زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
            زر.clicked.connect(وظيفة)
            self.أزرار_الإجراءات[مفتاح] = زر
            تخطيط_الأزرار.addWidget(زر)

        تخطيط_الأزرار.addStretch()
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

        # تطبيق أنماط إطار الأزرار
        إطار_الأزرار.setStyleSheet("""
            QFrame#buttons_frame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(100, 60)
        زر.setObjectName("action_button")

        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)

        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 20px;")

        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 9px; font-weight: bold;")
        تسمية_نص.setWordWrap(True)

        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)

        # تطبيق اللون
        زر.setStyleSheet(f"""
            QPushButton#action_button {{
                border: 2px solid {لون};
                border-radius: 8px;
                background-color: white;
            }}
            QPushButton#action_button:hover {{
                background-color: {لون};
                color: white;
            }}
            QPushButton#action_button:pressed {{
                background-color: {لون};
                border: 3px solid {لون};
            }}
        """)

        return زر

    def إنشاء_جدول_الفئات(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول عرض الفئات
        """
        # جدول الفئات
        self.جدول_الفئات = QTableWidget()
        self.جدول_الفئات.setObjectName("categories_table")

        # إعداد الأعمدة
        الأعمدة = [
            "رقم الفئة", "اسم الفئة", "الوصف", "عدد المنتجات",
            "الحالة", "تاريخ الإضافة"
        ]

        self.جدول_الفئات.setColumnCount(len(الأعمدة))
        self.جدول_الفئات.setHorizontalHeaderLabels(الأعمدة)

        # إعداد خصائص الجدول
        self.جدول_الفئات.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الفئات.setAlternatingRowColors(True)
        self.جدول_الفئات.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.جدول_الفئات.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الفئة
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # الوصف

        # ربط النقر بتحديد الفئة
        self.جدول_الفئات.itemSelectionChanged.connect(self.تحديد_فئة)

        # قائمة سياق
        self.جدول_الفئات.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_الفئات.customContextMenuRequested.connect(self.عرض_قائمة_سياق)

        التخطيط_الرئيسي.addWidget(self.جدول_الفئات)

        # تطبيق الأنماط
        self.جدول_الفئات.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }

            QTableWidget::item {
                padding: 8px;
                text-align: center;
            }

            QHeaderView::section {
                background-color: #e67e22;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        # تعطيل أزرار التعديل والحذف في البداية
        self.أزرار_الإجراءات["edit_category"].setEnabled(False)
        self.أزرار_الإجراءات["delete_category"].setEnabled(False)
        self.أزرار_الإجراءات["category_stats"].setEnabled(False)

        # تحميل البيانات بعد إنشاء الجدول
        self.تحميل_الفئات()

    def تحميل_الفئات(self):
        """
        دالة تحميل جميع الفئات
        """
        try:
            استعلام = """
            SELECT f.رقم_الفئة, f.اسم_الفئة, f.وصف_الفئة,
                   COUNT(p.الباركود) as عدد_المنتجات,
                   f.حالة_النشاط,
                   DATE_FORMAT(f.تاريخ_الإنشاء, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الفئات f
            LEFT JOIN المنتجات p ON f.رقم_الفئة = p.رقم_الفئة
            GROUP BY f.رقم_الفئة, f.اسم_الفئة, f.وصف_الفئة, f.حالة_النشاط, f.تاريخ_الإنشاء
            ORDER BY f.تاريخ_الإنشاء DESC
            """

            الفئات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if الفئات:
                self.جدول_الفئات.setRowCount(len(الفئات))

                for صف, فئة in enumerate(الفئات):
                    for عمود, قيمة in enumerate(فئة):
                        if عمود == 4:  # عمود الحالة
                            حالة = "نشط" if قيمة else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 3:  # عدد المنتجات
                            عدد = int(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(str(عدد))
                            if عدد > 0:
                                عنصر.setForeground(QColor("#3498db"))
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الفئات.setItem(صف, عمود, عنصر)
            else:
                self.جدول_الفئات.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الفئات: {str(e)}")

    def البحث_عن_الفئات(self):
        """
        دالة البحث عن الفئات
        """
        نص_البحث = self.حقل_البحث.text().strip()
        حالة_الفلتر = self.قائمة_حالة.currentText()

        try:
            شرط_البحث = "1=1"
            معاملات = []

            # شرط البحث النصي
            if نص_البحث:
                شرط_البحث += " AND (f.اسم_الفئة LIKE %s OR f.وصف_الفئة LIKE %s)"
                معاملات.extend([f"%{نص_البحث}%"] * 2)

            # شرط فلتر الحالة
            if حالة_الفلتر == "نشط":
                شرط_البحث += " AND f.حالة_النشاط = TRUE"
            elif حالة_الفلتر == "غير نشط":
                شرط_البحث += " AND f.حالة_النشاط = FALSE"

            استعلام = f"""
            SELECT f.رقم_الفئة, f.اسم_الفئة, f.وصف_الفئة,
                   COUNT(p.الباركود) as عدد_المنتجات,
                   f.حالة_النشاط,
                   DATE_FORMAT(f.تاريخ_الإنشاء, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الفئات f
            LEFT JOIN المنتجات p ON f.رقم_الفئة = p.رقم_الفئة
            WHERE {شرط_البحث}
            GROUP BY f.رقم_الفئة, f.اسم_الفئة, f.وصف_الفئة, f.حالة_النشاط, f.تاريخ_الإنشاء
            ORDER BY f.تاريخ_الإنشاء DESC
            """

            الفئات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_الفئات.setRowCount(0)

            if الفئات:
                self.جدول_الفئات.setRowCount(len(الفئات))

                for صف, فئة in enumerate(الفئات):
                    for عمود, قيمة in enumerate(فئة):
                        if عمود == 4:  # عمود الحالة
                            حالة = "نشط" if قيمة else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 3:  # عدد المنتجات
                            عدد = int(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(str(عدد))
                            if عدد > 0:
                                عنصر.setForeground(QColor("#3498db"))
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الفئات.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_فئة(self):
        """
        دالة تحديد الفئة المختارة
        """
        صف_محدد = self.جدول_الفئات.currentRow()

        if صف_محدد >= 0:
            رقم_الفئة = self.جدول_الفئات.item(صف_محدد, 0).text()
            self.الفئة_المحددة = int(رقم_الفئة)

            # تفعيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_category"].setEnabled(True)
            self.أزرار_الإجراءات["delete_category"].setEnabled(True)
            self.أزرار_الإجراءات["category_stats"].setEnabled(True)
        else:
            self.الفئة_المحددة = None

            # تعطيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_category"].setEnabled(False)
            self.أزرار_الإجراءات["delete_category"].setEnabled(False)
            self.أزرار_الإجراءات["category_stats"].setEnabled(False)

    def عرض_قائمة_سياق(self, موضع):
        """
        دالة عرض قائمة السياق
        """
        if self.جدول_الفئات.itemAt(موضع):
            قائمة = QMenu(self)

            إجراء_تعديل = قائمة.addAction("تعديل الفئة")
            إجراء_تعديل.triggered.connect(self.تعديل_فئة)

            إجراء_حذف = قائمة.addAction("حذف الفئة")
            إجراء_حذف.triggered.connect(self.حذف_فئة)

            قائمة.addSeparator()

            إجراء_إحصائيات = قائمة.addAction("إحصائيات الفئة")
            إجراء_إحصائيات.triggered.connect(self.إحصائيات_فئة)

            قائمة.exec_(self.جدول_الفئات.mapToGlobal(موضع))

    # دوال الإجراءات
    def إضافة_فئة(self):
        """دالة إضافة فئة جديدة"""
        نافذة_إضافة = نافذة_فئة(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_الفئات()

    def تعديل_فئة(self):
        """دالة تعديل الفئة المحددة"""
        if not self.الفئة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فئة للتعديل")
            return

        نافذة_تعديل = نافذة_فئة(self, "تعديل", self.الفئة_المحددة)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_الفئات()

    def حذف_فئة(self):
        """دالة حذف الفئة المحددة"""
        if not self.الفئة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فئة للحذف")
            return

        # الحصول على اسم الفئة وعدد المنتجات
        صف_محدد = self.جدول_الفئات.currentRow()
        اسم_فئة = self.جدول_الفئات.item(صف_محدد, 1).text()
        عدد_منتجات = int(self.جدول_الفئات.item(صف_محدد, 3).text())

        if عدد_منتجات > 0:
            QMessageBox.warning(self, "تحذير",
                              f"لا يمكن حذف الفئة '{اسم_فئة}' لأنها تحتوي على {عدد_منتجات} منتج.\n"
                              "يرجى نقل المنتجات إلى فئة أخرى أولاً.")
            return

        رد = QMessageBox.question(self, "تأكيد الحذف",
                                  f"هل تريد حذف الفئة '{اسم_فئة}'؟",
                                  QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM الفئات WHERE رقم_الفئة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.الفئة_المحددة,))

                QMessageBox.information(self, "نجح", "تم حذف الفئة بنجاح")
                self.تحميل_الفئات()
                self.الفئة_المحددة = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الفئة: {str(e)}")

    def إحصائيات_فئة(self):
        """دالة عرض إحصائيات الفئة"""
        QMessageBox.information(self, "معلومات", "وظيفة إحصائيات الفئة قيد التطوير")

    def طباعة_الفئات(self):
        """دالة طباعة قائمة الفئات"""
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")
