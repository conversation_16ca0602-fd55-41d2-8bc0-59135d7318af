# -*- coding: utf-8 -*-
"""
اختبار واجهات الموظفين والمصروفات بشكل منفصل
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QTabWidget
from PySide6.QtCore import Qt
from db import تهيئة_قاعدة_البيانات
from other_interfaces import واجهة_الموظفين, واجهة_المصروفات

class نافذة_اختبار_الواجهات(QMainWindow):
    """
    نافذة اختبار واجهات الموظفين والمصروفات
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار واجهات الموظفين والمصروفات")
        self.setMinimumSize(1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء التبويبات
        التبويبات = QTabWidget()
        التبويبات.setLayoutDirection(Qt.RightToLeft)
        
        # تبويب الموظفين
        واجهة_موظفين = واجهة_الموظفين()
        التبويبات.addTab(واجهة_موظفين, "إدارة الموظفين")
        
        # تبويب المصروفات
        واجهة_مصروفات = واجهة_المصروفات()
        التبويبات.addTab(واجهة_مصروفات, "إدارة المصروفات")
        
        # تطبيق الأنماط
        التبويبات.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: #ecf0f1;
            }
            QTabBar::tab {
                background-color: #34495e;
                color: white;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #2c3e50;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #4a6741;
            }
        """)
        
        self.setCentralWidget(التبويبات)

def main():
    """
    دالة تشغيل اختبار الواجهات
    """
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تهيئة قاعدة البيانات
    if not تهيئة_قاعدة_البيانات():
        print("فشل في تهيئة قاعدة البيانات")
        sys.exit(1)
    
    # إنشاء النافذة
    نافذة = نافذة_اختبار_الواجهات()
    نافذة.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
