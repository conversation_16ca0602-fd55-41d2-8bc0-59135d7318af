# واجهات إدارة الموظفين والمصروفات والبنوك - النوافذ الفرعية

## نظرة عامة
تم تطوير ثلاث واجهات شاملة لإدارة الموظفين والمصروفات والبنوك في نظام إدارة المبيعات والمخزون. تستخدم الواجهات نظام النوافذ الفرعية (Dialog Windows) مثل واجهة العملاء لتوفير تجربة مستخدم متسقة وسهولة في الاستخدام.

## 👥 واجهة إدارة الموظفين

### الميزات الرئيسية

#### 1. إدارة شاملة للموظفين
- إضافة موظفين جدد مع جميع البيانات المطلوبة
- تعديل بيانات الموظفين الموجودين
- حذف الموظفين (مع التحقق من الارتباطات)
- تفعيل/إلغاء تفعيل الموظفين

#### 2. البيانات الشخصية
- **الاسم الكامل** (مطلوب)
- **المنصب** (اختياري)
- **رقم الهاتف** (اختياري)
- **العنوان** (اختياري)
- **تاريخ التوظيف** (مطلوب)

#### 3. البيانات المالية
- **الراتب الأساسي** (بالريال السعودي)
- **النسبة** (نسبة مئوية من المبيعات)
- **الرصيد الحالي** (حساب الموظف الجاري)

#### 4. بيانات النظام
- **اسم المستخدم** (للدخول للنظام)
- **كلمة المرور** (مشفرة)
- **الصلاحيات** (تحديد صلاحيات الوصول)
- **حالة النشاط** (نشط/غير نشط)

#### 5. وظائف متقدمة
- **البحث السريع** في جميع بيانات الموظفين
- **جدول تفاعلي** مع ترتيب وفلترة
- **عرض ملون** للحالات (نشط/غير نشط)
- **تحديث فوري** للبيانات

### هيكل قاعدة البيانات - جدول الموظفين
```sql
CREATE TABLE الموظفين (
    رقم_الموظف INT AUTO_INCREMENT PRIMARY KEY,
    الاسم_الكامل VARCHAR(100) NOT NULL,
    المنصب VARCHAR(50),
    رقم_الهاتف VARCHAR(20),
    العنوان TEXT,
    الراتب_الأساسي DECIMAL(10,2) DEFAULT 0.00,
    النسبة DECIMAL(5,2) DEFAULT 0.00,
    الرصيد_الحالي DECIMAL(15,2) DEFAULT 0.00,
    اسم_المستخدم VARCHAR(50) UNIQUE,
    كلمة_المرور VARCHAR(255),
    الصلاحيات TEXT,
    حالة_النشاط BOOLEAN DEFAULT TRUE,
    تاريخ_التوظيف DATE,
    تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 💰 واجهة إدارة المصروفات

### الميزات الرئيسية

#### 1. تسجيل المصروفات
- إضافة مصروفات جديدة مع جميع التفاصيل
- تعديل المصروفات الموجودة
- حذف المصروفات
- ربط المصروفات بالموظفين

#### 2. بيانات المصروف
- **نوع المصروف** (مطلوب) - تصنيف المصروف
- **المبلغ** (مطلوب) - بالريال السعودي
- **تاريخ المصروف** (مطلوب)
- **الموظف المسؤول** (اختياري)
- **الوصف** (اختياري) - تفاصيل المصروف
- **الملاحظات** (اختياري) - ملاحظات إضافية

#### 3. وظائف متقدمة
- **البحث المتقدم** في جميع بيانات المصروفات
- **فلترة حسب التاريخ** (من تاريخ - إلى تاريخ)
- **حساب الإجمالي التلقائي** للمصروفات المعروضة
- **عرض ملون** للمبالغ (أحمر للمصروفات)

#### 4. التقارير والإحصائيات
- عرض إجمالي المصروفات في الوقت الفعلي
- فلترة المصروفات حسب الفترة الزمنية
- ربط المصروفات بالموظفين لتتبع المسؤوليات

### هيكل قاعدة البيانات - جدول المصروفات
```sql
CREATE TABLE المصروفات (
    رقم_المصروف INT AUTO_INCREMENT PRIMARY KEY,
    نوع_المصروف VARCHAR(100) NOT NULL,
    الوصف TEXT,
    المبلغ DECIMAL(15,2) NOT NULL,
    تاريخ_المصروف DATE NOT NULL,
    رقم_الموظف INT,
    ملاحظات TEXT,
    تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (رقم_الموظف) REFERENCES الموظفين(رقم_الموظف)
);
```

## 🎨 التصميم والواجهة

### خصائص التصميم
- **تصميم RTL كامل** للغة العربية
- **ألوان متناسقة** ومريحة للعين
- **أزرار واضحة** مع أيقونات مميزة
- **تخطيط مرن** ومتجاوب
- **أنماط CSS متقدمة** للتفاعل

### الألوان المستخدمة
- **الموظفين**: البنفسجي (#9b59b6) كلون أساسي
- **المصروفات**: البرتقالي (#f39c12) كلون أساسي
- **الإضافة**: الأخضر (#27ae60)
- **التعديل**: البرتقالي (#f39c12)
- **الحذف**: الأحمر (#e74c3c)
- **التحديث**: البنفسجي (#9b59b6)

## 🔧 الوظائف التقنية

### الأمان والتحقق
- **التحقق من صحة البيانات** قبل الحفظ
- **منع تكرار أسماء المستخدمين**
- **التحقق من الارتباطات** قبل الحذف
- **معالجة شاملة للأخطاء**

### الأداء والكفاءة
- **استعلامات محسنة** لقاعدة البيانات
- **تحديث فوري** للبيانات
- **فهرسة مناسبة** للجداول
- **ذاكرة تخزين مؤقت** للبيانات المتكررة

## 📋 كيفية الاستخدام

## 🆕 التحديث الجديد: نظام النوافذ الفرعية

### المميزات الجديدة
- **نوافذ فرعية منفصلة** لإضافة وتعديل البيانات
- **دبل كليك** على أي صف لفتح نافذة التعديل مباشرة
- **تصميم متسق** مع واجهة العملاء
- **تجربة مستخدم محسنة** مع نوافذ مخصصة لكل عملية

### إدارة الموظفين
1. **إضافة موظف جديد**:
   - اضغط "إضافة موظف" أو استخدم شريط الأدوات
   - ستفتح نافذة فرعية مع جميع الحقول المطلوبة
   - أدخل البيانات في المجموعات الثلاث:
     - البيانات الشخصية
     - البيانات المالية
     - بيانات النظام
   - اضغط "حفظ" أو "إلغاء"

2. **تعديل موظف**:
   - **طريقة 1**: حدد الموظف واضغط "تعديل"
   - **طريقة 2**: دبل كليك على صف الموظف
   - ستفتح نافذة التعديل مع البيانات محملة مسبقاً
   - عدل البيانات المطلوبة واضغط "حفظ"

3. **البحث والفلترة**:
   - استخدم حقل البحث للبحث السريع
   - البحث يشمل الاسم والمنصب والهاتف

### إدارة المصروفات
1. **إضافة مصروف جديد**:
   - اضغط "إضافة مصروف"
   - ستفتح نافذة فرعية مخصصة للمصروفات
   - أدخل نوع المصروف والمبلغ والتاريخ
   - اختر الموظف المسؤول (اختياري)
   - أضف الوصف والملاحظات
   - اضغط "حفظ"

2. **تعديل مصروف**:
   - **طريقة 1**: حدد المصروف واضغط "تعديل"
   - **طريقة 2**: دبل كليك على صف المصروف
   - ستفتح نافذة التعديل مع البيانات محملة
   - عدل البيانات واضغط "حفظ"

3. **فلترة حسب التاريخ**:
   - استخدم حقلي "من" و "إلى" في شريط البحث
   - سيتم تحديث الجدول والإجمالي تلقائياً

### إدارة البنوك
1. **إضافة بنك رئيسي**:
   - اضغط "إضافة بنك رئيسي"
   - ستفتح نافذة إضافة البنك
   - أدخل اسم البنك والهاتف والعنوان
   - اضغط "حفظ"

2. **إضافة فرع**:
   - اضغط "إضافة فرع"
   - ستفتح نافذة إضافة الفرع
   - اختر البنك الرئيسي من القائمة
   - أدخل بيانات الفرع واضغط "حفظ"

3. **تعديل بنك أو فرع**:
   - **طريقة 1**: حدد البنك واضغط "تعديل"
   - **طريقة 2**: دبل كليك على البنك في الشجرة
   - ستفتح نافذة التعديل مع البيانات محملة

## 🔗 التكامل مع النظام

### الارتباطات
- **المصروفات ← الموظفين**: ربط المصروف بالموظف المسؤول
- **فواتير البيع ← الموظفين**: ربط الفواتير بالموظف البائع
- **نظام الصلاحيات**: التحكم في وصول الموظفين

### البيانات الأولية
- يتم إنشاء مستخدم إداري افتراضي:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`
  - الصلاحيات: جميع الصلاحيات

## 📊 التقارير المستقبلية
- تقرير رواتب الموظفين
- تقرير أداء الموظفين
- تقرير المصروفات الشهرية/السنوية
- تحليل المصروفات حسب النوع
- مقارنة المصروفات بالفترات السابقة

## 🚀 التطوير المستقبلي
- نظام الحضور والانصراف
- حساب الرواتب التلقائي
- تنبيهات المصروفات الزائدة
- تصدير التقارير إلى Excel/PDF
- نظام الموافقات للمصروفات الكبيرة

## 🎯 المميزات الجديدة في النوافذ الفرعية

### تحسينات تجربة المستخدم
- **نوافذ منفصلة**: كل عملية إضافة أو تعديل تتم في نافذة منفصلة
- **دبل كليك سريع**: انقر مرتين على أي صف للتعديل المباشر
- **تصميم متسق**: نفس نمط واجهة العملاء المألوف
- **منطقة تمرير**: للنوافذ الطويلة مع دعم التمرير
- **أزرار واضحة**: حفظ وإلغاء في كل نافذة

### تحسينات تقنية
- **ذاكرة محسنة**: لا توجد حقول دائمة في الواجهة الرئيسية
- **أداء أفضل**: تحميل البيانات عند الحاجة فقط
- **أمان محسن**: التحقق من البيانات في كل نافذة منفصلة
- **سهولة الصيانة**: كود منظم ومنفصل لكل نافذة

### الواجهات المحدثة
1. **واجهة الموظفين** ✅
   - نافذة إضافة/تعديل موظف
   - دبل كليك للتعديل السريع
   - مجموعات منظمة للبيانات

2. **واجهة المصروفات** ✅
   - نافذة إضافة/تعديل مصروف
   - ربط ديناميكي مع الموظفين
   - دبل كليك للتعديل السريع

3. **واجهة البنوك** ✅
   - نافذة إضافة بنك رئيسي
   - نافذة إضافة فرع مع اختيار البنك الرئيسي
   - نافذة تعديل موحدة للبنوك والفروع
   - دبل كليك على الشجرة للتعديل

## 🔄 مقارنة مع النظام السابق

| الميزة | النظام السابق | النظام الجديد |
|--------|---------------|---------------|
| إضافة/تعديل | في نفس الواجهة | نوافذ فرعية منفصلة |
| التعديل السريع | تحديد + زر تعديل | دبل كليك مباشر |
| تجربة المستخدم | مزدحمة | نظيفة ومنظمة |
| الاتساق | مختلف عن العملاء | متسق مع جميع الواجهات |
| سهولة الاستخدام | متوسطة | عالية جداً |

الواجهات الآن جاهزة للاستخدام الفوري مع نظام النوافذ الفرعية المحسن وتوفر تجربة مستخدم متسقة واحترافية عبر جميع أجزاء التطبيق! 🎉✨
