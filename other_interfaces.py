# -*- coding: utf-8 -*-
"""
الواجهات المتبقية للتطبيق
تحتوي على واجهات الموظفين والمصروفات والبنوك والعقود والأقساط والتقارير المالية
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime
from db import قاعدة_البيانات

class واجهة_الموظفين(QWidget):
    """
    واجهة إدارة الموظفين الشاملة
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة الموظفين
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.الموظف_المحدد = None
        self.إعداد_الواجهة()
        self.تحميل_البيانات()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة الموظفين
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة الموظفين")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #9b59b6;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الأدوات
        self.إنشاء_شريط_الأدوات(التخطيط_الرئيسي)

        # جدول الموظفين
        self.إنشاء_جدول_الموظفين(التخطيط_الرئيسي)

    def إنشاء_شريط_الأدوات(self, التخطيط_الرئيسي):
        """
        إنشاء شريط الأدوات مع الأزرار الرئيسية
        """
        إطار_الأدوات = QFrame()
        إطار_الأدوات.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        تخطيط_الأدوات = QHBoxLayout(إطار_الأدوات)
        تخطيط_الأدوات.setSpacing(10)

        # زر إضافة موظف
        زر_إضافة = QPushButton("إضافة موظف")
        زر_إضافة.setIcon(QIcon("icons/add_employee.png"))
        زر_إضافة.clicked.connect(self.إضافة_موظف)
        زر_إضافة.setStyleSheet(self.نمط_الزر("#27ae60"))

        # زر تعديل
        زر_تعديل = QPushButton("تعديل")
        زر_تعديل.setIcon(QIcon("icons/edit.png"))
        زر_تعديل.clicked.connect(self.تعديل_الموظف)
        زر_تعديل.setStyleSheet(self.نمط_الزر("#f39c12"))

        # زر حذف
        زر_حذف = QPushButton("حذف")
        زر_حذف.setIcon(QIcon("icons/delete.png"))
        زر_حذف.clicked.connect(self.حذف_الموظف)
        زر_حذف.setStyleSheet(self.نمط_الزر("#e74c3c"))

        # زر تحديث
        زر_تحديث = QPushButton("تحديث")
        زر_تحديث.setIcon(QIcon("icons/refresh.png"))
        زر_تحديث.clicked.connect(self.تحديث_البيانات)
        زر_تحديث.setStyleSheet(self.نمط_الزر("#9b59b6"))

        # زر تقرير الموظفين
        زر_تقرير = QPushButton("تقرير الموظفين")
        زر_تقرير.setIcon(QIcon("icons/report.png"))
        زر_تقرير.clicked.connect(self.تقرير_الموظفين)
        زر_تقرير.setStyleSheet(self.نمط_الزر("#3498db"))

        تخطيط_الأدوات.addWidget(زر_إضافة)
        تخطيط_الأدوات.addWidget(زر_تعديل)
        تخطيط_الأدوات.addWidget(زر_حذف)
        تخطيط_الأدوات.addWidget(زر_تحديث)
        تخطيط_الأدوات.addWidget(زر_تقرير)
        تخطيط_الأدوات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأدوات)

    def إنشاء_جدول_الموظفين(self, التخطيط_الرئيسي):
        """
        إنشاء جدول الموظفين
        """
        إطار_الجدول = QFrame()
        إطار_الجدول.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

        تخطيط_الجدول = QVBoxLayout(إطار_الجدول)
        تخطيط_الجدول.setContentsMargins(15, 15, 15, 15)

        # عنوان الجدول
        عنوان_الجدول = QLabel("قائمة الموظفين")
        عنوان_الجدول.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_الجدول.addWidget(عنوان_الجدول)

        # شريط البحث
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("البحث في الموظفين...")
        self.حقل_البحث.textChanged.connect(self.بحث_في_الموظفين)
        self.حقل_البحث.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #9b59b6;
            }
        """)
        تخطيط_الجدول.addWidget(self.حقل_البحث)

        # جدول الموظفين
        self.جدول_الموظفين = QTableWidget()
        self.جدول_الموظفين.setColumnCount(7)
        self.جدول_الموظفين.setHorizontalHeaderLabels([
            "رقم الموظف", "الاسم الكامل", "المنصب", "رقم الهاتف",
            "الراتب الأساسي", "الحالة", "تاريخ التوظيف"
        ])

        # تنسيق الجدول
        self.جدول_الموظفين.setLayoutDirection(Qt.RightToLeft)
        self.جدول_الموظفين.setAlternatingRowColors(True)
        self.جدول_الموظفين.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الموظفين.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_الموظفين.horizontalHeader().setStretchLastSection(True)

        # ربط إشارة التحديد والدبل كليك
        self.جدول_الموظفين.itemSelectionChanged.connect(self.عند_تحديد_موظف)
        self.جدول_الموظفين.itemDoubleClicked.connect(self.تعديل_الموظف)

        self.جدول_الموظفين.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ffffff;
                gridline-color: #ecf0f1;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #9b59b6;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        تخطيط_الجدول.addWidget(self.جدول_الموظفين)
        التخطيط_الرئيسي.addWidget(إطار_الجدول)



    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #9b59b6;
                background-color: #f8f9fa;
            }
        """

    def تحميل_البيانات(self):
        """
        تحميل بيانات الموظفين من قاعدة البيانات
        """
        try:
            استعلام = """
                SELECT رقم_الموظف, الاسم_الكامل, المنصب, رقم_الهاتف,
                       الراتب_الأساسي, حالة_النشاط, تاريخ_التوظيف
                FROM الموظفين
                ORDER BY تاريخ_الإنشاء DESC
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_الموظفين.setRowCount(0)

            # إضافة البيانات للجدول
            for صف, موظف in enumerate(الموظفين):
                self.جدول_الموظفين.insertRow(صف)

                # رقم الموظف
                self.جدول_الموظفين.setItem(صف, 0, QTableWidgetItem(str(موظف[0])))

                # الاسم الكامل
                self.جدول_الموظفين.setItem(صف, 1, QTableWidgetItem(موظف[1]))

                # المنصب
                self.جدول_الموظفين.setItem(صف, 2, QTableWidgetItem(موظف[2] or "غير محدد"))

                # رقم الهاتف
                self.جدول_الموظفين.setItem(صف, 3, QTableWidgetItem(موظف[3] or "غير محدد"))

                # الراتب الأساسي
                راتب = f"{موظف[4]:,.2f} ريال" if موظف[4] else "0.00 ريال"
                self.جدول_الموظفين.setItem(صف, 4, QTableWidgetItem(راتب))

                # الحالة
                حالة = "نشط" if موظف[5] else "غير نشط"
                عنصر_الحالة = QTableWidgetItem(حالة)
                if موظف[5]:
                    عنصر_الحالة.setForeground(QBrush(QColor("#27ae60")))
                else:
                    عنصر_الحالة.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_الموظفين.setItem(صف, 5, عنصر_الحالة)

                # تاريخ التوظيف
                تاريخ = موظف[6].strftime("%Y-%m-%d") if موظف[6] else "غير محدد"
                self.جدول_الموظفين.setItem(صف, 6, QTableWidgetItem(تاريخ))

                # حفظ رقم الموظف في البيانات المخفية
                self.جدول_الموظفين.item(صف, 0).setData(Qt.UserRole, موظف[0])

            # تعديل عرض الأعمدة
            self.جدول_الموظفين.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def عند_تحديد_موظف(self):
        """
        عند تحديد موظف من الجدول
        """
        الصف_المحدد = self.جدول_الموظفين.currentRow()
        if الصف_المحدد >= 0:
            رقم_الموظف = self.جدول_الموظفين.item(الصف_المحدد, 0).data(Qt.UserRole)
            if رقم_الموظف:
                self.الموظف_المحدد = رقم_الموظف
                self.تحميل_تفاصيل_الموظف(رقم_الموظف)
            else:
                self.مسح_النموذج()



    def إضافة_موظف(self):
        """
        إضافة موظف جديد
        """
        نافذة_إضافة = نافذة_موظف(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def تعديل_الموظف(self):
        """
        تعديل الموظف المحدد
        """
        if not self.الموظف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد موظف للتعديل")
            return

        نافذة_تعديل = نافذة_موظف(self, "تعديل", self.الموظف_المحدد)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def حذف_الموظف(self):
        """
        حذف الموظف المحدد
        """
        if not self.الموظف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد موظف للحذف")
            return

        # التحقق من وجود ارتباطات
        if self.التحقق_من_الارتباطات(self.الموظف_المحدد):
            QMessageBox.warning(self, "تحذير",
                              "لا يمكن حذف هذا الموظف لوجود معاملات مرتبطة به.\n"
                              "يرجى إلغاء تفعيل الموظف بدلاً من حذفه.")
            return

        # تأكيد الحذف
        رد = QMessageBox.question(self, "تأكيد الحذف",
                                 "هل أنت متأكد من حذف هذا الموظف؟\n"
                                 "هذا الإجراء لا يمكن التراجع عنه.",
                                 QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM الموظفين WHERE رقم_الموظف = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (self.الموظف_المحدد,))
                self.قاعدة_البيانات.connection.commit()

                QMessageBox.information(self, "نجح", "تم حذف الموظف بنجاح")
                self.تحديث_البيانات()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الموظف: {str(e)}")

    def التحقق_من_الارتباطات(self, رقم_الموظف):
        """
        التحقق من وجود ارتباطات للموظف
        """
        try:
            # التحقق من فواتير البيع
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM فواتير_البيع WHERE رقم_الموظف = %s",
                (رقم_الموظف,)
            )
            عدد_الفواتير = self.قاعدة_البيانات.cursor.fetchone()[0]

            # التحقق من المصروفات
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM المصروفات WHERE رقم_الموظف = %s",
                (رقم_الموظف,)
            )
            عدد_المصروفات = self.قاعدة_البيانات.cursor.fetchone()[0]

            return عدد_الفواتير > 0 or عدد_المصروفات > 0

        except Exception as e:
            print(f"خطأ في التحقق من الارتباطات: {str(e)}")
            return True  # في حالة الخطأ، نمنع الحذف للأمان

    def تحديث_البيانات(self):
        """
        تحديث جميع البيانات
        """
        self.تحميل_البيانات()

    def بحث_في_الموظفين(self):
        """
        البحث في الموظفين
        """
        نص_البحث = self.حقل_البحث.text().strip()

        if not نص_البحث:
            self.تحميل_البيانات()
            return

        try:
            استعلام = """
                SELECT رقم_الموظف, الاسم_الكامل, المنصب, رقم_الهاتف,
                       الراتب_الأساسي, حالة_النشاط, تاريخ_التوظيف
                FROM الموظفين
                WHERE الاسم_الكامل LIKE %s OR المنصب LIKE %s OR رقم_الهاتف LIKE %s
                ORDER BY تاريخ_الإنشاء DESC
            """
            نمط_البحث = f"%{نص_البحث}%"
            self.قاعدة_البيانات.cursor.execute(استعلام, (نمط_البحث, نمط_البحث, نمط_البحث))
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_الموظفين.setRowCount(0)

            # إضافة نتائج البحث
            for صف, موظف in enumerate(الموظفين):
                self.جدول_الموظفين.insertRow(صف)

                # إضافة البيانات (نفس منطق تحميل_البيانات)
                self.جدول_الموظفين.setItem(صف, 0, QTableWidgetItem(str(موظف[0])))
                self.جدول_الموظفين.setItem(صف, 1, QTableWidgetItem(موظف[1]))
                self.جدول_الموظفين.setItem(صف, 2, QTableWidgetItem(موظف[2] or "غير محدد"))
                self.جدول_الموظفين.setItem(صف, 3, QTableWidgetItem(موظف[3] or "غير محدد"))

                راتب = f"{موظف[4]:,.2f} ريال" if موظف[4] else "0.00 ريال"
                self.جدول_الموظفين.setItem(صف, 4, QTableWidgetItem(راتب))

                حالة = "نشط" if موظف[5] else "غير نشط"
                عنصر_الحالة = QTableWidgetItem(حالة)
                if موظف[5]:
                    عنصر_الحالة.setForeground(QBrush(QColor("#27ae60")))
                else:
                    عنصر_الحالة.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_الموظفين.setItem(صف, 5, عنصر_الحالة)

                تاريخ = موظف[6].strftime("%Y-%m-%d") if موظف[6] else "غير محدد"
                self.جدول_الموظفين.setItem(صف, 6, QTableWidgetItem(تاريخ))

                self.جدول_الموظفين.item(صف, 0).setData(Qt.UserRole, موظف[0])

            self.جدول_الموظفين.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تقرير_الموظفين(self):
        """
        إنشاء تقرير الموظفين
        """
        QMessageBox.information(self, "تقرير الموظفين", "سيتم تطوير تقرير الموظفين قريباً")

class واجهة_المصروفات(QWidget):
    """
    واجهة إدارة المصروفات الشاملة
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة المصروفات
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.المصروف_المحدد = None
        self.إعداد_الواجهة()
        self.تحميل_البيانات()
        self.تحميل_الموظفين()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة المصروفات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة المصروفات")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #f39c12;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الأدوات
        self.إنشاء_شريط_الأدوات(التخطيط_الرئيسي)

        # جدول المصروفات
        self.إنشاء_جدول_المصروفات(التخطيط_الرئيسي)

    def إنشاء_شريط_الأدوات(self, التخطيط_الرئيسي):
        """
        إنشاء شريط الأدوات مع الأزرار الرئيسية
        """
        إطار_الأدوات = QFrame()
        إطار_الأدوات.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        تخطيط_الأدوات = QHBoxLayout(إطار_الأدوات)
        تخطيط_الأدوات.setSpacing(10)

        # زر إضافة مصروف
        زر_إضافة = QPushButton("إضافة مصروف")
        زر_إضافة.setIcon(QIcon("icons/add_expense.png"))
        زر_إضافة.clicked.connect(self.إضافة_مصروف)
        زر_إضافة.setStyleSheet(self.نمط_الزر("#27ae60"))

        # زر تعديل
        زر_تعديل = QPushButton("تعديل")
        زر_تعديل.setIcon(QIcon("icons/edit.png"))
        زر_تعديل.clicked.connect(self.تعديل_المصروف)
        زر_تعديل.setStyleSheet(self.نمط_الزر("#f39c12"))

        # زر حذف
        زر_حذف = QPushButton("حذف")
        زر_حذف.setIcon(QIcon("icons/delete.png"))
        زر_حذف.clicked.connect(self.حذف_المصروف)
        زر_حذف.setStyleSheet(self.نمط_الزر("#e74c3c"))

        # زر تحديث
        زر_تحديث = QPushButton("تحديث")
        زر_تحديث.setIcon(QIcon("icons/refresh.png"))
        زر_تحديث.clicked.connect(self.تحديث_البيانات)
        زر_تحديث.setStyleSheet(self.نمط_الزر("#9b59b6"))

        # زر تقرير المصروفات
        زر_تقرير = QPushButton("تقرير المصروفات")
        زر_تقرير.setIcon(QIcon("icons/report.png"))
        زر_تقرير.clicked.connect(self.تقرير_المصروفات)
        زر_تقرير.setStyleSheet(self.نمط_الزر("#3498db"))

        تخطيط_الأدوات.addWidget(زر_إضافة)
        تخطيط_الأدوات.addWidget(زر_تعديل)
        تخطيط_الأدوات.addWidget(زر_حذف)
        تخطيط_الأدوات.addWidget(زر_تحديث)
        تخطيط_الأدوات.addWidget(زر_تقرير)
        تخطيط_الأدوات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأدوات)

    def إنشاء_جدول_المصروفات(self, التخطيط_الرئيسي):
        """
        إنشاء جدول المصروفات
        """
        إطار_الجدول = QFrame()
        إطار_الجدول.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

        تخطيط_الجدول = QVBoxLayout(إطار_الجدول)
        تخطيط_الجدول.setContentsMargins(15, 15, 15, 15)

        # عنوان الجدول مع إجمالي المصروفات
        تخطيط_العنوان = QHBoxLayout()

        عنوان_الجدول = QLabel("قائمة المصروفات")
        عنوان_الجدول.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)

        self.تسمية_الإجمالي = QLabel("الإجمالي: 0.00 ريال")
        self.تسمية_الإجمالي.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #e74c3c;
            padding: 10px;
            background-color: #fff5f5;
            border: 1px solid #e74c3c;
            border-radius: 5px;
        """)

        تخطيط_العنوان.addWidget(عنوان_الجدول)
        تخطيط_العنوان.addStretch()
        تخطيط_العنوان.addWidget(self.تسمية_الإجمالي)

        تخطيط_الجدول.addLayout(تخطيط_العنوان)

        # شريط البحث والفلترة
        تخطيط_البحث = QHBoxLayout()

        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("البحث في المصروفات...")
        self.حقل_البحث.textChanged.connect(self.بحث_في_المصروفات)
        self.حقل_البحث.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #f39c12;
            }
        """)

        # فلتر التاريخ
        self.تاريخ_من = QDateEdit()
        self.تاريخ_من.setDate(QDate.currentDate().addDays(-30))
        self.تاريخ_من.setCalendarPopup(True)
        self.تاريخ_من.dateChanged.connect(self.فلترة_حسب_التاريخ)

        self.تاريخ_إلى = QDateEdit()
        self.تاريخ_إلى.setDate(QDate.currentDate())
        self.تاريخ_إلى.setCalendarPopup(True)
        self.تاريخ_إلى.dateChanged.connect(self.فلترة_حسب_التاريخ)

        تخطيط_البحث.addWidget(QLabel("البحث:"))
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(QLabel("من:"))
        تخطيط_البحث.addWidget(self.تاريخ_من)
        تخطيط_البحث.addWidget(QLabel("إلى:"))
        تخطيط_البحث.addWidget(self.تاريخ_إلى)

        تخطيط_الجدول.addLayout(تخطيط_البحث)

        # جدول المصروفات
        self.جدول_المصروفات = QTableWidget()
        self.جدول_المصروفات.setColumnCount(6)
        self.جدول_المصروفات.setHorizontalHeaderLabels([
            "رقم المصروف", "نوع المصروف", "المبلغ", "تاريخ المصروف", "الموظف", "الوصف"
        ])

        # تنسيق الجدول
        self.جدول_المصروفات.setLayoutDirection(Qt.RightToLeft)
        self.جدول_المصروفات.setAlternatingRowColors(True)
        self.جدول_المصروفات.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_المصروفات.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_المصروفات.horizontalHeader().setStretchLastSection(True)

        # ربط إشارة التحديد والدبل كليك
        self.جدول_المصروفات.itemSelectionChanged.connect(self.عند_تحديد_مصروف)
        self.جدول_المصروفات.itemDoubleClicked.connect(self.تعديل_المصروف)

        self.جدول_المصروفات.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ffffff;
                gridline-color: #ecf0f1;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #f39c12;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        تخطيط_الجدول.addWidget(self.جدول_المصروفات)
        التخطيط_الرئيسي.addWidget(إطار_الجدول)

    def إنشاء_منطقة_التفاصيل(self, تخطيط_المحتوى):
        """
        إنشاء منطقة تفاصيل المصروف المحدد
        """
        إطار_التفاصيل = QFrame()
        إطار_التفاصيل.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        إطار_التفاصيل.setMinimumWidth(350)
        إطار_التفاصيل.setMaximumWidth(450)

        تخطيط_التفاصيل = QVBoxLayout(إطار_التفاصيل)
        تخطيط_التفاصيل.setContentsMargins(20, 20, 20, 20)
        تخطيط_التفاصيل.setSpacing(15)

        # عنوان التفاصيل
        عنوان_التفاصيل = QLabel("تفاصيل المصروف")
        عنوان_التفاصيل.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_التفاصيل.addWidget(عنوان_التفاصيل)

        # نموذج التفاصيل
        self.إنشاء_نموذج_التفاصيل(تخطيط_التفاصيل)

        تخطيط_المحتوى.addWidget(إطار_التفاصيل)

    def إنشاء_نموذج_التفاصيل(self, تخطيط_التفاصيل):
        """
        إنشاء نموذج تفاصيل المصروف
        """
        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("بيانات المصروف")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_نوع_المصروف = QLineEdit()
        self.حقل_نوع_المصروف.setPlaceholderText("أدخل نوع المصروف")
        self.حقل_نوع_المصروف.setStyleSheet(self.نمط_الحقل())

        self.حقل_المبلغ = QDoubleSpinBox()
        self.حقل_المبلغ.setRange(0, 999999.99)
        self.حقل_المبلغ.setDecimals(2)
        self.حقل_المبلغ.setSuffix(" ريال")
        self.حقل_المبلغ.setStyleSheet(self.نمط_الحقل())

        self.حقل_تاريخ_المصروف = QDateEdit()
        self.حقل_تاريخ_المصروف.setDate(QDate.currentDate())
        self.حقل_تاريخ_المصروف.setCalendarPopup(True)
        self.حقل_تاريخ_المصروف.setStyleSheet(self.نمط_الحقل())

        self.قائمة_الموظف = QComboBox()
        self.قائمة_الموظف.addItem("-- اختر الموظف --", None)
        self.قائمة_الموظف.setStyleSheet(self.نمط_القائمة())

        self.حقل_الوصف = QTextEdit()
        self.حقل_الوصف.setPlaceholderText("أدخل وصف المصروف")
        self.حقل_الوصف.setMaximumHeight(80)
        self.حقل_الوصف.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #f39c12;
            }
        """)

        self.حقل_الملاحظات = QTextEdit()
        self.حقل_الملاحظات.setPlaceholderText("أدخل ملاحظات إضافية")
        self.حقل_الملاحظات.setMaximumHeight(80)
        self.حقل_الملاحظات.setStyleSheet(self.حقل_الوصف.styleSheet())

        تخطيط_أساسي.addRow("نوع المصروف:", self.حقل_نوع_المصروف)
        تخطيط_أساسي.addRow("المبلغ:", self.حقل_المبلغ)
        تخطيط_أساسي.addRow("تاريخ المصروف:", self.حقل_تاريخ_المصروف)
        تخطيط_أساسي.addRow("الموظف:", self.قائمة_الموظف)
        تخطيط_أساسي.addRow("الوصف:", self.حقل_الوصف)
        تخطيط_أساسي.addRow("الملاحظات:", self.حقل_الملاحظات)

        تخطيط_التفاصيل.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_المصروف)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.إلغاء_التعديل)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        تخطيط_التفاصيل.addLayout(تخطيط_الأزرار)
        تخطيط_التفاصيل.addStretch()

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #f39c12;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #f39c12;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_البيانات(self):
        """
        تحميل بيانات المصروفات من قاعدة البيانات
        """
        try:
            استعلام = """
                SELECT م.رقم_المصروف, م.نوع_المصروف, م.المبلغ, م.تاريخ_المصروف,
                       موظ.الاسم_الكامل, م.الوصف, م.ملاحظات, م.رقم_الموظف
                FROM المصروفات م
                LEFT JOIN الموظفين موظ ON م.رقم_الموظف = موظ.رقم_الموظف
                ORDER BY م.تاريخ_المصروف DESC, م.تاريخ_الإنشاء DESC
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            المصروفات = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_المصروفات.setRowCount(0)

            إجمالي_المصروفات = 0.0

            # إضافة البيانات للجدول
            for صف, مصروف in enumerate(المصروفات):
                self.جدول_المصروفات.insertRow(صف)

                # رقم المصروف
                self.جدول_المصروفات.setItem(صف, 0, QTableWidgetItem(str(مصروف[0])))

                # نوع المصروف
                self.جدول_المصروفات.setItem(صف, 1, QTableWidgetItem(مصروف[1]))

                # المبلغ
                مبلغ = f"{مصروف[2]:,.2f} ريال"
                عنصر_المبلغ = QTableWidgetItem(مبلغ)
                عنصر_المبلغ.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_المصروفات.setItem(صف, 2, عنصر_المبلغ)
                إجمالي_المصروفات += float(مصروف[2])

                # تاريخ المصروف
                تاريخ = مصروف[3].strftime("%Y-%m-%d") if مصروف[3] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 3, QTableWidgetItem(تاريخ))

                # الموظف
                موظف = مصروف[4] if مصروف[4] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 4, QTableWidgetItem(موظف))

                # الوصف
                وصف = مصروف[5][:50] + "..." if مصروف[5] and len(مصروف[5]) > 50 else (مصروف[5] or "")
                self.جدول_المصروفات.setItem(صف, 5, QTableWidgetItem(وصف))

                # حفظ رقم المصروف في البيانات المخفية
                self.جدول_المصروفات.item(صف, 0).setData(Qt.UserRole, مصروف[0])

            # تحديث إجمالي المصروفات
            self.تسمية_الإجمالي.setText(f"الإجمالي: {إجمالي_المصروفات:,.2f} ريال")

            # تعديل عرض الأعمدة
            self.جدول_المصروفات.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def تحميل_الموظفين(self):
        """
        تحميل قائمة الموظفين النشطين
        """
        try:
            # مسح القائمة
            self.قائمة_الموظف.clear()
            self.قائمة_الموظف.addItem("-- اختر الموظف --", None)

            # تحميل الموظفين النشطين
            استعلام = """
                SELECT رقم_الموظف, الاسم_الكامل
                FROM الموظفين
                WHERE حالة_النشاط = TRUE
                ORDER BY الاسم_الكامل
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            for موظف in الموظفين:
                self.قائمة_الموظف.addItem(موظف[1], موظف[0])

        except Exception as e:
            print(f"خطأ في تحميل الموظفين: {str(e)}")

    def عند_تحديد_مصروف(self):
        """
        عند تحديد مصروف من الجدول
        """
        الصف_المحدد = self.جدول_المصروفات.currentRow()
        if الصف_المحدد >= 0:
            رقم_المصروف = self.جدول_المصروفات.item(الصف_المحدد, 0).data(Qt.UserRole)
            if رقم_المصروف:
                self.المصروف_المحدد = رقم_المصروف
                self.تحميل_تفاصيل_المصروف(رقم_المصروف)
            else:
                self.مسح_النموذج()

    def تحميل_تفاصيل_المصروف(self, رقم_المصروف):
        """
        تحميل تفاصيل المصروف المحدد
        """
        try:
            استعلام = """
                SELECT نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف, رقم_الموظف, ملاحظات
                FROM المصروفات
                WHERE رقم_المصروف = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (رقم_المصروف,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_نوع_المصروف.setText(البيانات[0])
                self.حقل_الوصف.setPlainText(البيانات[1] or "")
                self.حقل_المبلغ.setValue(float(البيانات[2]) if البيانات[2] else 0.0)

                if البيانات[3]:
                    self.حقل_تاريخ_المصروف.setDate(QDate.fromString(البيانات[3].strftime("%Y-%m-%d"), "yyyy-MM-dd"))

                # تحديد الموظف
                if البيانات[4]:
                    for i in range(self.قائمة_الموظف.count()):
                        if self.قائمة_الموظف.itemData(i) == البيانات[4]:
                            self.قائمة_الموظف.setCurrentIndex(i)
                            break
                else:
                    self.قائمة_الموظف.setCurrentIndex(0)

                self.حقل_الملاحظات.setPlainText(البيانات[5] or "")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل المصروف: {str(e)}")

    def مسح_النموذج(self):
        """
        مسح جميع حقول النموذج
        """
        self.حقل_نوع_المصروف.clear()
        self.حقل_الوصف.clear()
        self.حقل_المبلغ.setValue(0.0)
        self.حقل_تاريخ_المصروف.setDate(QDate.currentDate())
        self.قائمة_الموظف.setCurrentIndex(0)
        self.حقل_الملاحظات.clear()
        self.المصروف_المحدد = None

    def إضافة_مصروف(self):
        """
        إضافة مصروف جديد
        """
        نافذة_إضافة = نافذة_مصروف(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def تعديل_المصروف(self):
        """
        تعديل المصروف المحدد
        """
        if not self.المصروف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للتعديل")
            return

        نافذة_تعديل = نافذة_مصروف(self, "تعديل", self.المصروف_المحدد)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def حذف_المصروف(self):
        """
        حذف المصروف المحدد
        """
        if not self.المصروف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للحذف")
            return

        # تأكيد الحذف
        رد = QMessageBox.question(self, "تأكيد الحذف",
                                 "هل أنت متأكد من حذف هذا المصروف؟\n"
                                 "هذا الإجراء لا يمكن التراجع عنه.",
                                 QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM المصروفات WHERE رقم_المصروف = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (self.المصروف_المحدد,))
                self.قاعدة_البيانات.connection.commit()

                QMessageBox.information(self, "نجح", "تم حذف المصروف بنجاح")
                self.تحديث_البيانات()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المصروف: {str(e)}")

    def حفظ_المصروف(self):
        """
        حفظ بيانات المصروف (إضافة أو تعديل)
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        نوع_المصروف = self.حقل_نوع_المصروف.text().strip()
        الوصف = self.حقل_الوصف.toPlainText().strip()
        المبلغ = self.حقل_المبلغ.value()
        تاريخ_المصروف = self.حقل_تاريخ_المصروف.date().toPython()
        رقم_الموظف = self.قائمة_الموظف.currentData()
        الملاحظات = self.حقل_الملاحظات.toPlainText().strip()

        # تحويل القيم الفارغة إلى None
        الوصف = الوصف if الوصف else None
        الملاحظات = الملاحظات if الملاحظات else None

        try:
            if self.المصروف_المحدد:
                # تعديل مصروف موجود
                استعلام = """
                    UPDATE المصروفات
                    SET نوع_المصروف = %s, الوصف = %s, المبلغ = %s,
                        تاريخ_المصروف = %s, رقم_الموظف = %s, ملاحظات = %s
                    WHERE رقم_المصروف = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف,
                     رقم_الموظف, الملاحظات, self.المصروف_المحدد))
                رسالة = "تم تعديل المصروف بنجاح"
            else:
                # إضافة مصروف جديد
                استعلام = """
                    INSERT INTO المصروفات
                    (نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف, رقم_الموظف, ملاحظات)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف, رقم_الموظف, الملاحظات))
                رسالة = "تم إضافة المصروف بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.تحديث_البيانات()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المصروف: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_نوع_المصروف.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع المصروف")
            self.حقل_نوع_المصروف.setFocus()
            return False

        if self.حقل_المبلغ.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر")
            self.حقل_المبلغ.setFocus()
            return False

        return True

    def إلغاء_التعديل(self):
        """
        إلغاء التعديل والعودة للحالة السابقة
        """
        if self.المصروف_المحدد:
            self.تحميل_تفاصيل_المصروف(self.المصروف_المحدد)
        else:
            self.مسح_النموذج()

    def تحديث_البيانات(self):
        """
        تحديث جميع البيانات
        """
        self.تحميل_البيانات()
        self.مسح_النموذج()

    def بحث_في_المصروفات(self):
        """
        البحث في المصروفات
        """
        نص_البحث = self.حقل_البحث.text().strip()

        if not نص_البحث:
            self.تحميل_البيانات()
            return

        try:
            استعلام = """
                SELECT م.رقم_المصروف, م.نوع_المصروف, م.المبلغ, م.تاريخ_المصروف,
                       موظ.الاسم_الكامل, م.الوصف, م.ملاحظات, م.رقم_الموظف
                FROM المصروفات م
                LEFT JOIN الموظفين موظ ON م.رقم_الموظف = موظ.رقم_الموظف
                WHERE م.نوع_المصروف LIKE %s OR م.الوصف LIKE %s OR موظ.الاسم_الكامل LIKE %s
                ORDER BY م.تاريخ_المصروف DESC, م.تاريخ_الإنشاء DESC
            """
            نمط_البحث = f"%{نص_البحث}%"
            self.قاعدة_البيانات.cursor.execute(استعلام, (نمط_البحث, نمط_البحث, نمط_البحث))
            المصروفات = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_المصروفات.setRowCount(0)

            إجمالي_المصروفات = 0.0

            # إضافة نتائج البحث
            for صف, مصروف in enumerate(المصروفات):
                self.جدول_المصروفات.insertRow(صف)

                # إضافة البيانات (نفس منطق تحميل_البيانات)
                self.جدول_المصروفات.setItem(صف, 0, QTableWidgetItem(str(مصروف[0])))
                self.جدول_المصروفات.setItem(صف, 1, QTableWidgetItem(مصروف[1]))

                مبلغ = f"{مصروف[2]:,.2f} ريال"
                عنصر_المبلغ = QTableWidgetItem(مبلغ)
                عنصر_المبلغ.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_المصروفات.setItem(صف, 2, عنصر_المبلغ)
                إجمالي_المصروفات += float(مصروف[2])

                تاريخ = مصروف[3].strftime("%Y-%m-%d") if مصروف[3] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 3, QTableWidgetItem(تاريخ))

                موظف = مصروف[4] if مصروف[4] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 4, QTableWidgetItem(موظف))

                وصف = مصروف[5][:50] + "..." if مصروف[5] and len(مصروف[5]) > 50 else (مصروف[5] or "")
                self.جدول_المصروفات.setItem(صف, 5, QTableWidgetItem(وصف))

                self.جدول_المصروفات.item(صف, 0).setData(Qt.UserRole, مصروف[0])

            # تحديث إجمالي المصروفات
            self.تسمية_الإجمالي.setText(f"الإجمالي: {إجمالي_المصروفات:,.2f} ريال")
            self.جدول_المصروفات.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")

    def فلترة_حسب_التاريخ(self):
        """
        فلترة المصروفات حسب التاريخ
        """
        try:
            تاريخ_من = self.تاريخ_من.date().toPython()
            تاريخ_إلى = self.تاريخ_إلى.date().toPython()

            استعلام = """
                SELECT م.رقم_المصروف, م.نوع_المصروف, م.المبلغ, م.تاريخ_المصروف,
                       موظ.الاسم_الكامل, م.الوصف, م.ملاحظات, م.رقم_الموظف
                FROM المصروفات م
                LEFT JOIN الموظفين موظ ON م.رقم_الموظف = موظ.رقم_الموظف
                WHERE م.تاريخ_المصروف BETWEEN %s AND %s
                ORDER BY م.تاريخ_المصروف DESC, م.تاريخ_الإنشاء DESC
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (تاريخ_من, تاريخ_إلى))
            المصروفات = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_المصروفات.setRowCount(0)

            إجمالي_المصروفات = 0.0

            # إضافة البيانات المفلترة
            for صف, مصروف in enumerate(المصروفات):
                self.جدول_المصروفات.insertRow(صف)

                self.جدول_المصروفات.setItem(صف, 0, QTableWidgetItem(str(مصروف[0])))
                self.جدول_المصروفات.setItem(صف, 1, QTableWidgetItem(مصروف[1]))

                مبلغ = f"{مصروف[2]:,.2f} ريال"
                عنصر_المبلغ = QTableWidgetItem(مبلغ)
                عنصر_المبلغ.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_المصروفات.setItem(صف, 2, عنصر_المبلغ)
                إجمالي_المصروفات += float(مصروف[2])

                تاريخ = مصروف[3].strftime("%Y-%m-%d") if مصروف[3] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 3, QTableWidgetItem(تاريخ))

                موظف = مصروف[4] if مصروف[4] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 4, QTableWidgetItem(موظف))

                وصف = مصروف[5][:50] + "..." if مصروف[5] and len(مصروف[5]) > 50 else (مصروف[5] or "")
                self.جدول_المصروفات.setItem(صف, 5, QTableWidgetItem(وصف))

                self.جدول_المصروفات.item(صف, 0).setData(Qt.UserRole, مصروف[0])

            # تحديث إجمالي المصروفات
            self.تسمية_الإجمالي.setText(f"الإجمالي: {إجمالي_المصروفات:,.2f} ريال")
            self.جدول_المصروفات.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الفلترة: {str(e)}")

    def تقرير_المصروفات(self):
        """
        إنشاء تقرير المصروفات
        """
        QMessageBox.information(self, "تقرير المصروفات", "سيتم تطوير تقرير المصروفات قريباً")


class نافذة_موظف(QDialog):
    """
    نافذة إضافة أو تعديل موظف
    """

    def __init__(self, parent, نوع_العملية, رقم_الموظف=None):
        """
        دالة التهيئة لنافذة الموظف
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_الموظف = رقم_الموظف

        self.إعداد_النافذة()
        self.إنشاء_النموذج()

        if نوع_العملية == "تعديل" and رقم_الموظف:
            self.تحميل_بيانات_الموظف()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} موظف")
        self.setFixedSize(600, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج الموظف
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} موظف")
        عنوان.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # منطقة التمرير
        منطقة_التمرير = QScrollArea()
        منطقة_التمرير.setWidgetResizable(True)
        منطقة_التمرير.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        widget_المحتوى = QWidget()
        تخطيط_المحتوى = QVBoxLayout(widget_المحتوى)
        تخطيط_المحتوى.setSpacing(15)

        # مجموعة البيانات الشخصية
        مجموعة_شخصية = QGroupBox("البيانات الشخصية")
        مجموعة_شخصية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_شخصي = QFormLayout(مجموعة_شخصية)
        تخطيط_شخصي.setSpacing(10)

        # حقول البيانات الشخصية
        self.حقل_الاسم_الكامل = QLineEdit()
        self.حقل_الاسم_الكامل.setPlaceholderText("أدخل الاسم الكامل")
        self.حقل_الاسم_الكامل.setStyleSheet(self.نمط_الحقل())

        self.حقل_المنصب = QLineEdit()
        self.حقل_المنصب.setPlaceholderText("أدخل المنصب")
        self.حقل_المنصب.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الهاتف = QLineEdit()
        self.حقل_رقم_الهاتف.setPlaceholderText("أدخل رقم الهاتف")
        self.حقل_رقم_الهاتف.setStyleSheet(self.نمط_الحقل())

        self.حقل_العنوان = QTextEdit()
        self.حقل_العنوان.setPlaceholderText("أدخل العنوان")
        self.حقل_العنوان.setMaximumHeight(60)
        self.حقل_العنوان.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #9b59b6;
            }
        """)

        self.حقل_تاريخ_التوظيف = QDateEdit()
        self.حقل_تاريخ_التوظيف.setDate(QDate.currentDate())
        self.حقل_تاريخ_التوظيف.setCalendarPopup(True)
        self.حقل_تاريخ_التوظيف.setStyleSheet(self.نمط_الحقل())

        تخطيط_شخصي.addRow("الاسم الكامل:", self.حقل_الاسم_الكامل)
        تخطيط_شخصي.addRow("المنصب:", self.حقل_المنصب)
        تخطيط_شخصي.addRow("رقم الهاتف:", self.حقل_رقم_الهاتف)
        تخطيط_شخصي.addRow("العنوان:", self.حقل_العنوان)
        تخطيط_شخصي.addRow("تاريخ التوظيف:", self.حقل_تاريخ_التوظيف)

        تخطيط_المحتوى.addWidget(مجموعة_شخصية)

        # مجموعة البيانات المالية
        مجموعة_مالية = QGroupBox("البيانات المالية")
        مجموعة_مالية.setStyleSheet(مجموعة_شخصية.styleSheet())

        تخطيط_مالي = QFormLayout(مجموعة_مالية)
        تخطيط_مالي.setSpacing(10)

        self.حقل_الراتب_الأساسي = QDoubleSpinBox()
        self.حقل_الراتب_الأساسي.setRange(0, 999999.99)
        self.حقل_الراتب_الأساسي.setDecimals(2)
        self.حقل_الراتب_الأساسي.setSuffix(" ريال")
        self.حقل_الراتب_الأساسي.setStyleSheet(self.نمط_الحقل())

        self.حقل_النسبة = QDoubleSpinBox()
        self.حقل_النسبة.setRange(0, 100)
        self.حقل_النسبة.setDecimals(2)
        self.حقل_النسبة.setSuffix(" %")
        self.حقل_النسبة.setStyleSheet(self.نمط_الحقل())

        self.حقل_الرصيد_الحالي = QDoubleSpinBox()
        self.حقل_الرصيد_الحالي.setRange(-999999.99, 999999.99)
        self.حقل_الرصيد_الحالي.setDecimals(2)
        self.حقل_الرصيد_الحالي.setSuffix(" ريال")
        self.حقل_الرصيد_الحالي.setStyleSheet(self.نمط_الحقل())

        تخطيط_مالي.addRow("الراتب الأساسي:", self.حقل_الراتب_الأساسي)
        تخطيط_مالي.addRow("النسبة:", self.حقل_النسبة)
        تخطيط_مالي.addRow("الرصيد الحالي:", self.حقل_الرصيد_الحالي)

        تخطيط_المحتوى.addWidget(مجموعة_مالية)

        # مجموعة بيانات النظام
        مجموعة_نظام = QGroupBox("بيانات النظام")
        مجموعة_نظام.setStyleSheet(مجموعة_شخصية.styleSheet())

        تخطيط_نظام = QFormLayout(مجموعة_نظام)
        تخطيط_نظام.setSpacing(10)

        self.حقل_اسم_المستخدم = QLineEdit()
        self.حقل_اسم_المستخدم.setPlaceholderText("أدخل اسم المستخدم")
        self.حقل_اسم_المستخدم.setStyleSheet(self.نمط_الحقل())

        self.حقل_كلمة_المرور = QLineEdit()
        self.حقل_كلمة_المرور.setPlaceholderText("أدخل كلمة المرور")
        self.حقل_كلمة_المرور.setEchoMode(QLineEdit.Password)
        self.حقل_كلمة_المرور.setStyleSheet(self.نمط_الحقل())

        self.حقل_الصلاحيات = QTextEdit()
        self.حقل_الصلاحيات.setPlaceholderText("أدخل الصلاحيات")
        self.حقل_الصلاحيات.setMaximumHeight(60)
        self.حقل_الصلاحيات.setStyleSheet(self.حقل_العنوان.styleSheet())

        self.مربع_الحالة = QCheckBox("الموظف نشط")
        self.مربع_الحالة.setChecked(True)
        self.مربع_الحالة.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:checked {
                background-color: #9b59b6;
                border: 2px solid #9b59b6;
            }
        """)

        تخطيط_نظام.addRow("اسم المستخدم:", self.حقل_اسم_المستخدم)
        تخطيط_نظام.addRow("كلمة المرور:", self.حقل_كلمة_المرور)
        تخطيط_نظام.addRow("الصلاحيات:", self.حقل_الصلاحيات)
        تخطيط_نظام.addRow("", self.مربع_الحالة)

        تخطيط_المحتوى.addWidget(مجموعة_نظام)

        منطقة_التمرير.setWidget(widget_المحتوى)
        التخطيط_الرئيسي.addWidget(منطقة_التمرير)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_الموظف)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.reject)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_الأزرار)

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #9b59b6;
                background-color: #f8f9fa;
            }
        """

    def تحميل_بيانات_الموظف(self):
        """
        تحميل بيانات الموظف للتعديل
        """
        try:
            استعلام = """
                SELECT الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                       النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                       حالة_النشاط, تاريخ_التوظيف
                FROM الموظفين
                WHERE رقم_الموظف = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (self.رقم_الموظف,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_الاسم_الكامل.setText(البيانات[0])
                self.حقل_المنصب.setText(البيانات[1] or "")
                self.حقل_رقم_الهاتف.setText(البيانات[2] or "")
                self.حقل_العنوان.setPlainText(البيانات[3] or "")
                self.حقل_الراتب_الأساسي.setValue(float(البيانات[4]) if البيانات[4] else 0.0)
                self.حقل_النسبة.setValue(float(البيانات[5]) if البيانات[5] else 0.0)
                self.حقل_الرصيد_الحالي.setValue(float(البيانات[6]) if البيانات[6] else 0.0)
                self.حقل_اسم_المستخدم.setText(البيانات[7] or "")
                self.حقل_كلمة_المرور.setText(البيانات[8] or "")
                self.حقل_الصلاحيات.setPlainText(البيانات[9] or "")
                self.مربع_الحالة.setChecked(البيانات[10])

                if البيانات[11]:
                    self.حقل_تاريخ_التوظيف.setDate(QDate.fromString(البيانات[11].strftime("%Y-%m-%d"), "yyyy-MM-dd"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الموظف: {str(e)}")

    def حفظ_الموظف(self):
        """
        حفظ بيانات الموظف
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        الاسم_الكامل = self.حقل_الاسم_الكامل.text().strip()
        المنصب = self.حقل_المنصب.text().strip()
        رقم_الهاتف = self.حقل_رقم_الهاتف.text().strip()
        العنوان = self.حقل_العنوان.toPlainText().strip()
        الراتب_الأساسي = self.حقل_الراتب_الأساسي.value()
        النسبة = self.حقل_النسبة.value()
        الرصيد_الحالي = self.حقل_الرصيد_الحالي.value()
        اسم_المستخدم = self.حقل_اسم_المستخدم.text().strip()
        كلمة_المرور = self.حقل_كلمة_المرور.text().strip()
        الصلاحيات = self.حقل_الصلاحيات.toPlainText().strip()
        حالة_النشاط = self.مربع_الحالة.isChecked()
        تاريخ_التوظيف = self.حقل_تاريخ_التوظيف.date().toPython()

        # تحويل القيم الفارغة إلى None
        المنصب = المنصب if المنصب else None
        رقم_الهاتف = رقم_الهاتف if رقم_الهاتف else None
        العنوان = العنوان if العنوان else None
        اسم_المستخدم = اسم_المستخدم if اسم_المستخدم else None
        كلمة_المرور = كلمة_المرور if كلمة_المرور else None
        الصلاحيات = الصلاحيات if الصلاحيات else None

        try:
            if self.نوع_العملية == "تعديل":
                # تعديل موظف موجود
                استعلام = """
                    UPDATE الموظفين
                    SET الاسم_الكامل = %s, المنصب = %s, رقم_الهاتف = %s, العنوان = %s,
                        الراتب_الأساسي = %s, النسبة = %s, الرصيد_الحالي = %s,
                        اسم_المستخدم = %s, كلمة_المرور = %s, الصلاحيات = %s,
                        حالة_النشاط = %s, تاريخ_التوظيف = %s
                    WHERE رقم_الموظف = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                     النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                     حالة_النشاط, تاريخ_التوظيف, self.رقم_الموظف))
                رسالة = "تم تعديل الموظف بنجاح"
            else:
                # إضافة موظف جديد
                استعلام = """
                    INSERT INTO الموظفين
                    (الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                     النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                     حالة_النشاط, تاريخ_التوظيف)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                     النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                     حالة_النشاط, تاريخ_التوظيف))
                رسالة = "تم إضافة الموظف بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الموظف: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_الاسم_الكامل.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم الكامل")
            self.حقل_الاسم_الكامل.setFocus()
            return False

        # التحقق من عدم تكرار اسم المستخدم
        اسم_المستخدم = self.حقل_اسم_المستخدم.text().strip()
        if اسم_المستخدم:
            try:
                if self.نوع_العملية == "تعديل":
                    # في حالة التعديل، تجاهل الموظف الحالي
                    استعلام = "SELECT COUNT(*) FROM الموظفين WHERE اسم_المستخدم = %s AND رقم_الموظف != %s"
                    self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_المستخدم, self.رقم_الموظف))
                else:
                    # في حالة الإضافة
                    استعلام = "SELECT COUNT(*) FROM الموظفين WHERE اسم_المستخدم = %s"
                    self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_المستخدم,))

                if self.قاعدة_البيانات.cursor.fetchone()[0] > 0:
                    QMessageBox.warning(self, "تحذير", "اسم المستخدم موجود مسبقاً")
                    self.حقل_اسم_المستخدم.setFocus()
                    return False

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في التحقق من البيانات: {str(e)}")
                return False

        return True


class نافذة_مصروف(QDialog):
    """
    نافذة إضافة أو تعديل مصروف
    """

    def __init__(self, parent, نوع_العملية, رقم_المصروف=None):
        """
        دالة التهيئة لنافذة المصروف
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_المصروف = رقم_المصروف

        self.إعداد_النافذة()
        self.إنشاء_النموذج()
        self.تحميل_الموظفين()

        if نوع_العملية == "تعديل" and رقم_المصروف:
            self.تحميل_بيانات_المصروف()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} مصروف")
        self.setFixedSize(500, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج المصروف
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} مصروف")
        عنوان.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("بيانات المصروف")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_نوع_المصروف = QLineEdit()
        self.حقل_نوع_المصروف.setPlaceholderText("أدخل نوع المصروف")
        self.حقل_نوع_المصروف.setStyleSheet(self.نمط_الحقل())

        self.حقل_المبلغ = QDoubleSpinBox()
        self.حقل_المبلغ.setRange(0, 999999.99)
        self.حقل_المبلغ.setDecimals(2)
        self.حقل_المبلغ.setSuffix(" ريال")
        self.حقل_المبلغ.setStyleSheet(self.نمط_الحقل())

        self.حقل_تاريخ_المصروف = QDateEdit()
        self.حقل_تاريخ_المصروف.setDate(QDate.currentDate())
        self.حقل_تاريخ_المصروف.setCalendarPopup(True)
        self.حقل_تاريخ_المصروف.setStyleSheet(self.نمط_الحقل())

        self.قائمة_الموظف = QComboBox()
        self.قائمة_الموظف.addItem("-- اختر الموظف --", None)
        self.قائمة_الموظف.setStyleSheet(self.نمط_القائمة())

        self.حقل_الوصف = QTextEdit()
        self.حقل_الوصف.setPlaceholderText("أدخل وصف المصروف")
        self.حقل_الوصف.setMaximumHeight(80)
        self.حقل_الوصف.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #f39c12;
            }
        """)

        self.حقل_الملاحظات = QTextEdit()
        self.حقل_الملاحظات.setPlaceholderText("أدخل ملاحظات إضافية")
        self.حقل_الملاحظات.setMaximumHeight(80)
        self.حقل_الملاحظات.setStyleSheet(self.حقل_الوصف.styleSheet())

        تخطيط_أساسي.addRow("نوع المصروف:", self.حقل_نوع_المصروف)
        تخطيط_أساسي.addRow("المبلغ:", self.حقل_المبلغ)
        تخطيط_أساسي.addRow("تاريخ المصروف:", self.حقل_تاريخ_المصروف)
        تخطيط_أساسي.addRow("الموظف:", self.قائمة_الموظف)
        تخطيط_أساسي.addRow("الوصف:", self.حقل_الوصف)
        تخطيط_أساسي.addRow("الملاحظات:", self.حقل_الملاحظات)

        التخطيط_الرئيسي.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_المصروف)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.reject)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_الأزرار)

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #f39c12;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QComboBox:focus {
                border-color: #f39c12;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_الموظفين(self):
        """
        تحميل قائمة الموظفين
        """
        try:
            استعلام = "SELECT رقم_الموظف, الاسم_الكامل FROM الموظفين WHERE حالة_النشاط = 1 ORDER BY الاسم_الكامل"
            self.قاعدة_البيانات.cursor.execute(استعلام)
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            for موظف in الموظفين:
                self.قائمة_الموظف.addItem(موظف[1], موظف[0])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الموظفين: {str(e)}")

    def تحميل_بيانات_المصروف(self):
        """
        تحميل بيانات المصروف للتعديل
        """
        try:
            استعلام = """
                SELECT نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف, الوصف, ملاحظات
                FROM المصروفات
                WHERE رقم_المصروف = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (self.رقم_المصروف,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_نوع_المصروف.setText(البيانات[0])
                self.حقل_المبلغ.setValue(float(البيانات[1]))

                if البيانات[2]:
                    self.حقل_تاريخ_المصروف.setDate(QDate.fromString(البيانات[2].strftime("%Y-%m-%d"), "yyyy-MM-dd"))

                # تحديد الموظف
                if البيانات[3]:
                    فهرس = self.قائمة_الموظف.findData(البيانات[3])
                    if فهرس >= 0:
                        self.قائمة_الموظف.setCurrentIndex(فهرس)

                self.حقل_الوصف.setPlainText(البيانات[4] or "")
                self.حقل_الملاحظات.setPlainText(البيانات[5] or "")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المصروف: {str(e)}")

    def حفظ_المصروف(self):
        """
        حفظ بيانات المصروف
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        نوع_المصروف = self.حقل_نوع_المصروف.text().strip()
        المبلغ = self.حقل_المبلغ.value()
        تاريخ_المصروف = self.حقل_تاريخ_المصروف.date().toPython()
        رقم_الموظف = self.قائمة_الموظف.currentData()
        الوصف = self.حقل_الوصف.toPlainText().strip()
        الملاحظات = self.حقل_الملاحظات.toPlainText().strip()

        # تحويل القيم الفارغة إلى None
        الوصف = الوصف if الوصف else None
        الملاحظات = الملاحظات if الملاحظات else None

        try:
            if self.نوع_العملية == "تعديل":
                # تعديل مصروف موجود
                استعلام = """
                    UPDATE المصروفات
                    SET نوع_المصروف = %s, المبلغ = %s, تاريخ_المصروف = %s,
                        رقم_الموظف = %s, الوصف = %s, ملاحظات = %s
                    WHERE رقم_المصروف = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف,
                     الوصف, الملاحظات, self.رقم_المصروف))
                رسالة = "تم تعديل المصروف بنجاح"
            else:
                # إضافة مصروف جديد
                استعلام = """
                    INSERT INTO المصروفات
                    (نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف, الوصف, ملاحظات)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف, الوصف, الملاحظات))
                رسالة = "تم إضافة المصروف بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المصروف: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_نوع_المصروف.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع المصروف")
            self.حقل_نوع_المصروف.setFocus()
            return False

        if self.حقل_المبلغ.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر")
            self.حقل_المبلغ.setFocus()
            return False

        return True


class نافذة_بنك(QDialog):
    """
    نافذة إضافة أو تعديل بنك
    """

    def __init__(self, parent, نوع_العملية, رقم_البنك=None):
        """
        دالة التهيئة لنافذة البنك
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_البنك = رقم_البنك

        self.إعداد_النافذة()
        self.إنشاء_النموذج()

        if نوع_العملية == "إضافة فرع":
            self.تحميل_البنوك_الرئيسية()

        if نوع_العملية == "تعديل" and رقم_البنك:
            self.تحميل_بيانات_البنك()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية}")
        self.setFixedSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج البنك
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية}")
        عنوان.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("بيانات البنك")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_اسم_البنك = QLineEdit()
        self.حقل_اسم_البنك.setPlaceholderText("أدخل اسم البنك")
        self.حقل_اسم_البنك.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_حساب_الشركة = QLineEdit()
        self.حقل_رقم_حساب_الشركة.setPlaceholderText("أدخل رقم حساب الشركة")
        self.حقل_رقم_حساب_الشركة.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الهاتف = QLineEdit()
        self.حقل_رقم_الهاتف.setPlaceholderText("أدخل رقم الهاتف")
        self.حقل_رقم_الهاتف.setStyleSheet(self.نمط_الحقل())

        self.حقل_العنوان = QTextEdit()
        self.حقل_العنوان.setPlaceholderText("أدخل العنوان")
        self.حقل_العنوان.setMaximumHeight(80)
        self.حقل_العنوان.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)

        # قائمة البنك الرئيسي (للفروع فقط)
        if self.نوع_العملية == "إضافة فرع":
            self.قائمة_البنك_الرئيسي = QComboBox()
            self.قائمة_البنك_الرئيسي.addItem("-- اختر البنك الرئيسي --", None)
            self.قائمة_البنك_الرئيسي.setStyleSheet(self.نمط_القائمة())

        self.مربع_الحالة = QCheckBox("البنك نشط")
        self.مربع_الحالة.setChecked(True)
        self.مربع_الحالة.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border: 2px solid #3498db;
            }
        """)

        تخطيط_أساسي.addRow("اسم البنك:", self.حقل_اسم_البنك)
        تخطيط_أساسي.addRow("رقم حساب الشركة:", self.حقل_رقم_حساب_الشركة)
        تخطيط_أساسي.addRow("رقم الهاتف:", self.حقل_رقم_الهاتف)
        تخطيط_أساسي.addRow("العنوان:", self.حقل_العنوان)

        if self.نوع_العملية == "إضافة فرع":
            تخطيط_أساسي.addRow("البنك الرئيسي:", self.قائمة_البنك_الرئيسي)

        تخطيط_أساسي.addRow("", self.مربع_الحالة)

        التخطيط_الرئيسي.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_البنك)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.reject)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_الأزرار)

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_البنوك_الرئيسية(self):
        """
        تحميل قائمة البنوك الرئيسية
        """
        try:
            استعلام = "SELECT رقم_البنك, اسم_البنك FROM البنوك WHERE بنك_رئيسي IS NULL ORDER BY اسم_البنك"
            self.قاعدة_البيانات.cursor.execute(استعلام)
            البنوك = self.قاعدة_البيانات.cursor.fetchall()

            for بنك in البنوك:
                self.قائمة_البنك_الرئيسي.addItem(بنك[1], بنك[0])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البنوك الرئيسية: {str(e)}")

    def تحميل_بيانات_البنك(self):
        """
        تحميل بيانات البنك للتعديل
        """
        try:
            استعلام = """
                SELECT اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, بنك_رئيسي, حالة_النشاط
                FROM البنوك
                WHERE رقم_البنك = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (self.رقم_البنك,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_اسم_البنك.setText(البيانات[0])
                self.حقل_رقم_حساب_الشركة.setText(البيانات[1] or "")
                self.حقل_رقم_الهاتف.setText(البيانات[2] or "")
                self.حقل_العنوان.setPlainText(البيانات[3] or "")
                self.مربع_الحالة.setChecked(البيانات[5])

                # إذا كان فرع، تحميل البنوك الرئيسية وتحديد البنك الرئيسي
                if البيانات[4]:  # إذا كان له بنك رئيسي
                    self.تحميل_البنوك_الرئيسية()
                    فهرس = self.قائمة_البنك_الرئيسي.findData(البيانات[4])
                    if فهرس >= 0:
                        self.قائمة_البنك_الرئيسي.setCurrentIndex(فهرس)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات البنك: {str(e)}")

    def حفظ_البنك(self):
        """
        حفظ بيانات البنك
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        اسم_البنك = self.حقل_اسم_البنك.text().strip()
        رقم_حساب_الشركة = self.حقل_رقم_حساب_الشركة.text().strip()
        رقم_الهاتف = self.حقل_رقم_الهاتف.text().strip()
        العنوان = self.حقل_العنوان.toPlainText().strip()
        حالة_النشاط = self.مربع_الحالة.isChecked()

        # تحويل القيم الفارغة إلى None
        رقم_حساب_الشركة = رقم_حساب_الشركة if رقم_حساب_الشركة else None
        رقم_الهاتف = رقم_الهاتف if رقم_الهاتف else None
        العنوان = العنوان if العنوان else None

        # البنك الرئيسي (للفروع فقط)
        بنك_رئيسي = None
        if self.نوع_العملية == "إضافة فرع":
            بنك_رئيسي = self.قائمة_البنك_الرئيسي.currentData()

        try:
            if self.نوع_العملية == "تعديل":
                # تعديل بنك موجود
                استعلام = """
                    UPDATE البنوك
                    SET اسم_البنك = %s, رقم_حساب_الشركة = %s, رقم_الهاتف = %s, العنوان = %s, حالة_النشاط = %s
                    WHERE رقم_البنك = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط, self.رقم_البنك))
                رسالة = "تم تعديل البنك بنجاح"
            else:
                # إضافة بنك جديد
                استعلام = """
                    INSERT INTO البنوك
                    (اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, بنك_رئيسي, حالة_النشاط)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, بنك_رئيسي, حالة_النشاط))
                رسالة = f"تم {self.نوع_العملية} بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البنك: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_اسم_البنك.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البنك")
            self.حقل_اسم_البنك.setFocus()
            return False

        # التحقق من اختيار البنك الرئيسي للفروع
        if self.نوع_العملية == "إضافة فرع":
            if not self.قائمة_البنك_الرئيسي.currentData():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار البنك الرئيسي")
                self.قائمة_البنك_الرئيسي.setFocus()
                return False

        return True

class واجهة_البنوك(QWidget):
    """
    واجهة إدارة البنوك مع نظام الشجرة للبنوك والفروع
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة البنوك
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.البنك_المحدد = None
        self.إعداد_الواجهة()
        self.تحميل_البيانات()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة البنوك
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة البنوك والفروع")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الأدوات
        self.إنشاء_شريط_الأدوات(التخطيط_الرئيسي)

        # شجرة البنوك
        self.إنشاء_شجرة_البنوك(التخطيط_الرئيسي)

    def إنشاء_شريط_الأدوات(self, التخطيط_الرئيسي):
        """
        إنشاء شريط الأدوات مع الأزرار الرئيسية
        """
        إطار_الأدوات = QFrame()
        إطار_الأدوات.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        تخطيط_الأدوات = QHBoxLayout(إطار_الأدوات)
        تخطيط_الأدوات.setSpacing(10)

        # زر إضافة بنك رئيسي
        زر_إضافة_بنك = QPushButton("إضافة بنك رئيسي")
        زر_إضافة_بنك.setIcon(QIcon("icons/add_bank.png"))
        زر_إضافة_بنك.clicked.connect(self.إضافة_بنك_رئيسي)
        زر_إضافة_بنك.setStyleSheet(self.نمط_الزر("#27ae60"))

        # زر إضافة فرع
        زر_إضافة_فرع = QPushButton("إضافة فرع")
        زر_إضافة_فرع.setIcon(QIcon("icons/add_branch.png"))
        زر_إضافة_فرع.clicked.connect(self.إضافة_فرع)
        زر_إضافة_فرع.setStyleSheet(self.نمط_الزر("#3498db"))

        # زر تعديل
        زر_تعديل = QPushButton("تعديل")
        زر_تعديل.setIcon(QIcon("icons/edit.png"))
        زر_تعديل.clicked.connect(self.تعديل_البنك)
        زر_تعديل.setStyleSheet(self.نمط_الزر("#f39c12"))

        # زر حذف
        زر_حذف = QPushButton("حذف")
        زر_حذف.setIcon(QIcon("icons/delete.png"))
        زر_حذف.clicked.connect(self.حذف_البنك)
        زر_حذف.setStyleSheet(self.نمط_الزر("#e74c3c"))

        # زر تحديث
        زر_تحديث = QPushButton("تحديث")
        زر_تحديث.setIcon(QIcon("icons/refresh.png"))
        زر_تحديث.clicked.connect(self.تحديث_البيانات)
        زر_تحديث.setStyleSheet(self.نمط_الزر("#9b59b6"))

        تخطيط_الأدوات.addWidget(زر_إضافة_بنك)
        تخطيط_الأدوات.addWidget(زر_إضافة_فرع)
        تخطيط_الأدوات.addWidget(زر_تعديل)
        تخطيط_الأدوات.addWidget(زر_حذف)
        تخطيط_الأدوات.addWidget(زر_تحديث)
        تخطيط_الأدوات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأدوات)

    def إنشاء_شجرة_البنوك(self, التخطيط_الرئيسي):
        """
        إنشاء شجرة البنوك والفروع
        """
        إطار_الشجرة = QFrame()
        إطار_الشجرة.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        إطار_الشجرة.setMinimumWidth(400)
        إطار_الشجرة.setMaximumWidth(500)

        تخطيط_الشجرة = QVBoxLayout(إطار_الشجرة)
        تخطيط_الشجرة.setContentsMargins(15, 15, 15, 15)

        # عنوان الشجرة
        عنوان_الشجرة = QLabel("البنوك والفروع")
        عنوان_الشجرة.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_الشجرة.addWidget(عنوان_الشجرة)

        # شجرة البنوك
        self.شجرة_البنوك = QTreeWidget()
        self.شجرة_البنوك.setHeaderLabels(["اسم البنك", "رقم الحساب", "الحالة"])
        self.شجرة_البنوك.setLayoutDirection(Qt.RightToLeft)
        self.شجرة_البنوك.setAlternatingRowColors(True)
        self.شجرة_البنوك.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ffffff;
                font-size: 12px;
                selection-background-color: #3498db;
                selection-color: white;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #ecf0f1;
            }
        """)

        # ربط إشارة التحديد والدبل كليك
        self.شجرة_البنوك.itemSelectionChanged.connect(self.عند_تحديد_بنك)
        self.شجرة_البنوك.itemDoubleClicked.connect(self.تعديل_البنك)

        تخطيط_الشجرة.addWidget(self.شجرة_البنوك)
        التخطيط_الرئيسي.addWidget(إطار_الشجرة)

    def إنشاء_منطقة_التفاصيل(self, تخطيط_المحتوى):
        """
        إنشاء منطقة تفاصيل البنك المحدد
        """
        إطار_التفاصيل = QFrame()
        إطار_التفاصيل.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

        تخطيط_التفاصيل = QVBoxLayout(إطار_التفاصيل)
        تخطيط_التفاصيل.setContentsMargins(20, 20, 20, 20)
        تخطيط_التفاصيل.setSpacing(15)

        # عنوان التفاصيل
        عنوان_التفاصيل = QLabel("تفاصيل البنك")
        عنوان_التفاصيل.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_التفاصيل.addWidget(عنوان_التفاصيل)

        # نموذج التفاصيل
        self.إنشاء_نموذج_التفاصيل(تخطيط_التفاصيل)

        تخطيط_المحتوى.addWidget(إطار_التفاصيل)

    def إنشاء_نموذج_التفاصيل(self, تخطيط_التفاصيل):
        """
        إنشاء نموذج تفاصيل البنك
        """
        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("البيانات الأساسية")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_اسم_البنك = QLineEdit()
        self.حقل_اسم_البنك.setPlaceholderText("أدخل اسم البنك")
        self.حقل_اسم_البنك.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الحساب = QLineEdit()
        self.حقل_رقم_الحساب.setPlaceholderText("أدخل رقم حساب الشركة")
        self.حقل_رقم_الحساب.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الهاتف = QLineEdit()
        self.حقل_رقم_الهاتف.setPlaceholderText("أدخل رقم الهاتف (اختياري)")
        self.حقل_رقم_الهاتف.setStyleSheet(self.نمط_الحقل())

        self.حقل_العنوان = QTextEdit()
        self.حقل_العنوان.setPlaceholderText("أدخل عنوان البنك (اختياري)")
        self.حقل_العنوان.setMaximumHeight(80)
        self.حقل_العنوان.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)

        self.قائمة_البنك_الرئيسي = QComboBox()
        self.قائمة_البنك_الرئيسي.addItem("-- بنك رئيسي --", None)
        self.قائمة_البنك_الرئيسي.setStyleSheet(self.نمط_القائمة())

        self.مربع_الحالة = QCheckBox("البنك نشط")
        self.مربع_الحالة.setChecked(True)
        self.مربع_الحالة.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:checked {
                background-color: #27ae60;
                border: 2px solid #27ae60;
            }
        """)

        تخطيط_أساسي.addRow("اسم البنك:", self.حقل_اسم_البنك)
        تخطيط_أساسي.addRow("رقم الحساب:", self.حقل_رقم_الحساب)
        تخطيط_أساسي.addRow("رقم الهاتف:", self.حقل_رقم_الهاتف)
        تخطيط_أساسي.addRow("العنوان:", self.حقل_العنوان)
        تخطيط_أساسي.addRow("البنك الرئيسي:", self.قائمة_البنك_الرئيسي)
        تخطيط_أساسي.addRow("", self.مربع_الحالة)

        تخطيط_التفاصيل.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_البنك)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.إلغاء_التعديل)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        تخطيط_التفاصيل.addLayout(تخطيط_الأزرار)
        تخطيط_التفاصيل.addStretch()

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
                
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_البيانات(self):
        """
        تحميل بيانات البنوك من قاعدة البيانات
        """
        try:
            # مسح الشجرة
            self.شجرة_البنوك.clear()

            # تحميل البنوك الرئيسية
            استعلام = """
                SELECT رقم_البنك, اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط
                FROM البنوك
                WHERE بنك_رئيسي IS NULL
                ORDER BY اسم_البنك
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            البنوك_الرئيسية = self.قاعدة_البيانات.cursor.fetchall()

            # إضافة البنوك الرئيسية للشجرة
            for بنك in البنوك_الرئيسية:
                عنصر_بنك = QTreeWidgetItem(self.شجرة_البنوك)
                عنصر_بنك.setText(0, بنك[1])  # اسم البنك
                عنصر_بنك.setText(1, بنك[2] or "غير محدد")  # رقم الحساب
                عنصر_بنك.setText(2, "نشط" if بنك[5] else "غير نشط")  # الحالة (تحديث الفهرس)
                عنصر_بنك.setData(0, Qt.UserRole, بنك[0])  # رقم البنك
                عنصر_بنك.setData(1, Qt.UserRole, "بنك_رئيسي")  # نوع العنصر

                # إضافة tooltip مع معلومات إضافية
                tooltip_text = f"البنك: {بنك[1]}\n"
                if بنك[2]:
                    tooltip_text += f"رقم الحساب: {بنك[2]}\n"
                if بنك[3]:
                    tooltip_text += f"الهاتف: {بنك[3]}\n"
                if بنك[4]:
                    tooltip_text += f"العنوان: {بنك[4][:50]}{'...' if len(بنك[4]) > 50 else ''}\n"
                tooltip_text += f"الحالة: {'نشط' if بنك[5] else 'غير نشط'}"
                عنصر_بنك.setToolTip(0, tooltip_text)

                # تلوين حسب الحالة
                if بنك[5]:
                    عنصر_بنك.setForeground(0, QBrush(QColor("#27ae60")))
                else:
                    عنصر_بنك.setForeground(0, QBrush(QColor("#e74c3c")))

                # تحميل الفروع
                self.تحميل_فروع_البنك(بنك[0], عنصر_بنك)

            # توسيع جميع العناصر
            self.شجرة_البنوك.expandAll()

            # تحديث قائمة البنوك الرئيسية
            self.تحديث_قائمة_البنوك_الرئيسية()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def تحميل_فروع_البنك(self, رقم_البنك_الرئيسي, عنصر_البنك_الرئيسي):
        """
        تحميل فروع بنك معين
        """
        try:
            استعلام = """
                SELECT رقم_البنك, اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط
                FROM البنوك
                WHERE بنك_رئيسي = %s
                ORDER BY اسم_البنك
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (رقم_البنك_الرئيسي,))
            الفروع = self.قاعدة_البيانات.cursor.fetchall()

            for فرع in الفروع:
                عنصر_فرع = QTreeWidgetItem(عنصر_البنك_الرئيسي)
                عنصر_فرع.setText(0, f"🏢 {فرع[1]}")  # اسم الفرع مع أيقونة
                عنصر_فرع.setText(1, فرع[2] or "غير محدد")  # رقم الحساب
                عنصر_فرع.setText(2, "نشط" if فرع[5] else "غير نشط")  # الحالة (تحديث الفهرس)
                عنصر_فرع.setData(0, Qt.UserRole, فرع[0])  # رقم البنك
                عنصر_فرع.setData(1, Qt.UserRole, "فرع")  # نوع العنصر

                # إضافة tooltip مع معلومات إضافية للفرع
                tooltip_text = f"الفرع: {فرع[1]}\n"
                if فرع[2]:
                    tooltip_text += f"رقم الحساب: {فرع[2]}\n"
                if فرع[3]:
                    tooltip_text += f"الهاتف: {فرع[3]}\n"
                if فرع[4]:
                    tooltip_text += f"العنوان: {فرع[4][:50]}{'...' if len(فرع[4]) > 50 else ''}\n"
                tooltip_text += f"الحالة: {'نشط' if فرع[5] else 'غير نشط'}"
                عنصر_فرع.setToolTip(0, tooltip_text)

                # تلوين حسب الحالة
                if فرع[5]:
                    عنصر_فرع.setForeground(0, QBrush(QColor("#3498db")))
                else:
                    عنصر_فرع.setForeground(0, QBrush(QColor("#e74c3c")))

        except Exception as e:
            print(f"خطأ في تحميل الفروع: {str(e)}")

    def تحديث_قائمة_البنوك_الرئيسية(self):
        """
        تحديث قائمة البنوك الرئيسية في ComboBox
        """
        try:
            # مسح القائمة
            self.قائمة_البنك_الرئيسي.clear()
            self.قائمة_البنك_الرئيسي.addItem("-- بنك رئيسي --", None)

            # تحميل البنوك الرئيسية
            استعلام = """
                SELECT رقم_البنك, اسم_البنك
                FROM البنوك
                WHERE بنك_رئيسي IS NULL AND حالة_النشاط = TRUE
                ORDER BY اسم_البنك
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            البنوك = self.قاعدة_البيانات.cursor.fetchall()

            for بنك in البنوك:
                self.قائمة_البنك_الرئيسي.addItem(بنك[1], بنك[0])

        except Exception as e:
            print(f"خطأ في تحديث قائمة البنوك: {str(e)}")

    def عند_تحديد_بنك(self):
        """
        عند تحديد بنك من الشجرة
        """
        العناصر_المحددة = self.شجرة_البنوك.selectedItems()
        if العناصر_المحددة:
            العنصر = العناصر_المحددة[0]
            رقم_البنك = العنصر.data(0, Qt.UserRole)
            نوع_العنصر = العنصر.data(1, Qt.UserRole)

            if رقم_البنك:
                self.البنك_المحدد = رقم_البنك
                self.تحميل_تفاصيل_البنك(رقم_البنك)

                # تحديث عنوان التفاصيل حسب نوع العنصر
                if نوع_العنصر == "فرع":
                    # يمكن إضافة منطق خاص بالفروع هنا
                    pass
            else:
                self.مسح_النموذج()

    def تحميل_تفاصيل_البنك(self, رقم_البنك):
        """
        تحميل تفاصيل البنك المحدد
        """
        try:
            استعلام = """
                SELECT اسم_البنك, بنك_رئيسي, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط
                FROM البنوك
                WHERE رقم_البنك = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (رقم_البنك,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_اسم_البنك.setText(البيانات[0])
                self.حقل_رقم_الحساب.setText(البيانات[2] or "")
                self.حقل_رقم_الهاتف.setText(البيانات[3] or "")
                self.حقل_العنوان.setPlainText(البيانات[4] or "")
                self.مربع_الحالة.setChecked(البيانات[5])

                # تحديد البنك الرئيسي
                if البيانات[1]:
                    for i in range(self.قائمة_البنك_الرئيسي.count()):
                        if self.قائمة_البنك_الرئيسي.itemData(i) == البيانات[1]:
                            self.قائمة_البنك_الرئيسي.setCurrentIndex(i)
                            break
                else:
                    self.قائمة_البنك_الرئيسي.setCurrentIndex(0)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل البنك: {str(e)}")

    def مسح_النموذج(self):
        """
        مسح جميع حقول النموذج
        """
        self.حقل_اسم_البنك.clear()
        self.حقل_رقم_الحساب.clear()
        self.حقل_رقم_الهاتف.clear()
        self.حقل_العنوان.clear()
        self.قائمة_البنك_الرئيسي.setCurrentIndex(0)
        self.مربع_الحالة.setChecked(True)
        self.البنك_المحدد = None

    def إضافة_بنك_رئيسي(self):
        """
        إضافة بنك رئيسي جديد
        """
        نافذة_إضافة = نافذة_بنك(self, "إضافة بنك رئيسي")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def إضافة_فرع(self):
        """
        إضافة فرع لبنك موجود
        """
        نافذة_إضافة = نافذة_بنك(self, "إضافة فرع")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def تعديل_البنك(self):
        """
        تعديل البنك المحدد
        """
        if not self.البنك_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد بنك للتعديل")
            return

        self.حقل_اسم_البنك.setFocus()
        QMessageBox.information(self, "تعديل بنك", "عدل البيانات المطلوبة ثم اضغط حفظ")

    def حذف_البنك(self):
        """
        حذف البنك المحدد
        """
        if not self.البنك_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد بنك للحذف")
            return

        # التحقق من وجود فروع أو عملاء مرتبطين
        if self.التحقق_من_الارتباطات(self.البنك_المحدد):
            QMessageBox.warning(self, "تحذير",
                              "لا يمكن حذف هذا البنك لوجود فروع أو عملاء مرتبطين به.\n"
                              "يرجى حذف الارتباطات أولاً أو إلغاء تفعيل البنك بدلاً من حذفه.")
            return

        # تأكيد الحذف
        رد = QMessageBox.question(self, "تأكيد الحذف",
                                 "هل أنت متأكد من حذف هذا البنك؟\n"
                                 "هذا الإجراء لا يمكن التراجع عنه.",
                                 QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM البنوك WHERE رقم_البنك = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (self.البنك_المحدد,))
                self.قاعدة_البيانات.connection.commit()

                QMessageBox.information(self, "نجح", "تم حذف البنك بنجاح")
                self.تحديث_البيانات()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف البنك: {str(e)}")

    def التحقق_من_الارتباطات(self, رقم_البنك):
        """
        التحقق من وجود ارتباطات للبنك
        """
        try:
            # التحقق من الفروع
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM البنوك WHERE بنك_رئيسي = %s",
                (رقم_البنك,)
            )
            عدد_الفروع = self.قاعدة_البيانات.cursor.fetchone()[0]

            # التحقق من العملاء
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM العملاء WHERE رقم_البنك = %s",
                (رقم_البنك,)
            )
            عدد_العملاء = self.قاعدة_البيانات.cursor.fetchone()[0]

            # التحقق من العقود
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM العقود WHERE رقم_البنك = %s",
                (رقم_البنك,)
            )
            عدد_العقود = self.قاعدة_البيانات.cursor.fetchone()[0]

            return عدد_الفروع > 0 or عدد_العملاء > 0 or عدد_العقود > 0

        except Exception as e:
            print(f"خطأ في التحقق من الارتباطات: {str(e)}")
            return True  # في حالة الخطأ، نمنع الحذف للأمان

    def حفظ_البنك(self):
        """
        حفظ بيانات البنك (إضافة أو تعديل)
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        اسم_البنك = self.حقل_اسم_البنك.text().strip()
        رقم_الحساب = self.حقل_رقم_الحساب.text().strip()
        رقم_الهاتف = self.حقل_رقم_الهاتف.text().strip()
        العنوان = self.حقل_العنوان.toPlainText().strip()
        بنك_رئيسي = self.قائمة_البنك_الرئيسي.currentData()
        حالة_النشاط = self.مربع_الحالة.isChecked()

        # تحويل القيم الفارغة إلى None
        رقم_الحساب = رقم_الحساب if رقم_الحساب else None
        رقم_الهاتف = رقم_الهاتف if رقم_الهاتف else None
        العنوان = العنوان if العنوان else None

        try:
            if self.البنك_المحدد:
                # تعديل بنك موجود
                استعلام = """
                    UPDATE البنوك
                    SET اسم_البنك = %s, بنك_رئيسي = %s, رقم_حساب_الشركة = %s,
                        رقم_الهاتف = %s, العنوان = %s, حالة_النشاط = %s
                    WHERE رقم_البنك = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, بنك_رئيسي, رقم_الحساب, رقم_الهاتف, العنوان, حالة_النشاط, self.البنك_المحدد))
                رسالة = "تم تعديل البنك بنجاح"
            else:
                # إضافة بنك جديد
                استعلام = """
                    INSERT INTO البنوك (اسم_البنك, بنك_رئيسي, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, بنك_رئيسي, رقم_الحساب, رقم_الهاتف, العنوان, حالة_النشاط))
                رسالة = "تم إضافة البنك بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.تحديث_البيانات()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البنك: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_اسم_البنك.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البنك")
            self.حقل_اسم_البنك.setFocus()
            return False

        # التحقق من عدم تكرار اسم البنك
        اسم_البنك = self.حقل_اسم_البنك.text().strip()
        try:
            if self.البنك_المحدد:
                # في حالة التعديل، تجاهل البنك الحالي
                استعلام = "SELECT COUNT(*) FROM البنوك WHERE اسم_البنك = %s AND رقم_البنك != %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_البنك, self.البنك_المحدد))
            else:
                # في حالة الإضافة
                استعلام = "SELECT COUNT(*) FROM البنوك WHERE اسم_البنك = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_البنك,))

            if self.قاعدة_البيانات.cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "اسم البنك موجود مسبقاً")
                self.حقل_اسم_البنك.setFocus()
                return False

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في التحقق من البيانات: {str(e)}")
            return False

        return True

    def إلغاء_التعديل(self):
        """
        إلغاء التعديل والعودة للحالة السابقة
        """
        if self.البنك_المحدد:
            self.تحميل_تفاصيل_البنك(self.البنك_المحدد)
        else:
            self.مسح_النموذج()

    def تحديث_البيانات(self):
        """
        تحديث جميع البيانات
        """
        self.تحميل_البيانات()
        self.مسح_النموذج()

class واجهة_العقود(QWidget):
    """
    واجهة إدارة العقود الشاملة
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة العقود
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.العقد_المحدد = None

        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()
        self.تحميل_البيانات()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة العقود
        """
        # التخطيط الرئيسي
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان الصفحة
        عنوان = QLabel("إدارة العقود")
        عنوان.setObjectName("page_title")
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول العقود
        self.إنشاء_جدول_العقود(التخطيط_الرئيسي)

        # شريط الحالة
        self.إنشاء_شريط_الحالة(التخطيط_الرئيسي)

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("رقم العقد، اسم العميل، أو رقم الفاتورة...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_العقود)

        # فلتر حالة العقد
        تسمية_حالة = QLabel("حالة العقد:")
        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "نشط", "مكتمل", "ملغي"])
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_العقود)

        # فلتر البنك
        تسمية_بنك = QLabel("البنك:")
        self.قائمة_بنك = QComboBox()
        self.تحميل_البنوك()
        self.قائمة_بنك.currentTextChanged.connect(self.البحث_عن_العقود)

        # فلتر التاريخ
        تسمية_تاريخ = QLabel("الفترة:")
        self.قائمة_تاريخ = QComboBox()
        self.قائمة_تاريخ.addItems(["الكل", "هذا الشهر", "آخر 3 شهور", "آخر 6 شهور", "هذا العام"])
        self.قائمة_تاريخ.currentTextChanged.connect(self.البحث_عن_العقود)

        # إضافة العناصر للتخطيط
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(تسمية_حالة)
        تخطيط_البحث.addWidget(self.قائمة_حالة)
        تخطيط_البحث.addWidget(تسمية_بنك)
        تخطيط_البحث.addWidget(self.قائمة_بنك)
        تخطيط_البحث.addWidget(تسمية_تاريخ)
        تخطيط_البحث.addWidget(self.قائمة_تاريخ)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الأزرار
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(60)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(10)

        # أزرار الإجراءات
        self.أزرار_الإجراءات = {}

        # زر إنشاء عقد جديد
        self.أزرار_الإجراءات["new_contract"] = QPushButton("إنشاء عقد")
        self.أزرار_الإجراءات["new_contract"].clicked.connect(self.إنشاء_عقد)

        # زر تعديل عقد
        self.أزرار_الإجراءات["edit_contract"] = QPushButton("تعديل عقد")
        self.أزرار_الإجراءات["edit_contract"].clicked.connect(self.تعديل_عقد)

        # زر عرض تفاصيل
        self.أزرار_الإجراءات["view_details"] = QPushButton("عرض التفاصيل")
        self.أزرار_الإجراءات["view_details"].clicked.connect(self.عرض_تفاصيل_عقد)

        # زر عرض الأقساط
        self.أزرار_الإجراءات["view_installments"] = QPushButton("عرض الأقساط")
        self.أزرار_الإجراءات["view_installments"].clicked.connect(self.عرض_أقساط_عقد)

        # زر إلغاء عقد
        self.أزرار_الإجراءات["cancel_contract"] = QPushButton("إلغاء عقد")
        self.أزرار_الإجراءات["cancel_contract"].clicked.connect(self.إلغاء_عقد)

        # زر طباعة عقد
        self.أزرار_الإجراءات["print_contract"] = QPushButton("طباعة عقد")
        self.أزرار_الإجراءات["print_contract"].clicked.connect(self.طباعة_عقد)

        # زر تحديث البيانات
        زر_تحديث = QPushButton("تحديث البيانات")
        زر_تحديث.clicked.connect(self.تحميل_البيانات)

        # إضافة الأزرار للتخطيط
        for زر in self.أزرار_الإجراءات.values():
            تخطيط_الأزرار.addWidget(زر)

        تخطيط_الأزرار.addWidget(زر_تحديث)
        تخطيط_الأزرار.addStretch()

        # تعطيل أزرار التعديل والحذف في البداية
        for مفتاح in ["edit_contract", "view_details", "view_installments", "cancel_contract", "print_contract"]:
            self.أزرار_الإجراءات[مفتاح].setEnabled(False)

        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_جدول_العقود(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول العقود
        """
        self.جدول_العقود = QTableWidget()
        self.جدول_العقود.setObjectName("contracts_table")

        # تحديد الأعمدة
        الأعمدة = [
            "رقم العقد", "اسم العميل", "رقم الفاتورة", "البنك",
            "مبلغ العقد", "القسط الشهري", "عدد الأقساط",
            "تاريخ البداية", "تاريخ الانتهاء", "حالة العقد", "الأقساط المسددة"
        ]

        self.جدول_العقود.setColumnCount(len(الأعمدة))
        self.جدول_العقود.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_العقود.setAlternatingRowColors(True)
        self.جدول_العقود.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_العقود.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_العقود.setSortingEnabled(True)

        # ضبط عرض الأعمدة
        header = self.جدول_العقود.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(الأعمدة)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # ربط الأحداث
        self.جدول_العقود.itemSelectionChanged.connect(self.عند_تحديد_عقد)
        self.جدول_العقود.itemDoubleClicked.connect(self.عرض_تفاصيل_عقد)

        التخطيط_الرئيسي.addWidget(self.جدول_العقود)

    def إنشاء_شريط_الحالة(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الحالة
        """
        إطار_الحالة = QFrame()
        إطار_الحالة.setObjectName("status_frame")
        إطار_الحالة.setFixedHeight(40)

        تخطيط_الحالة = QHBoxLayout(إطار_الحالة)
        تخطيط_الحالة.setContentsMargins(15, 5, 15, 5)
        تخطيط_الحالة.setSpacing(20)

        # إحصائيات سريعة
        self.تسمية_عدد_العقود = QLabel("عدد العقود: 0")
        self.تسمية_العقود_النشطة = QLabel("النشطة: 0")
        self.تسمية_إجمالي_المبالغ = QLabel("إجمالي المبالغ: 0.00")
        self.تسمية_الأقساط_المتأخرة = QLabel("أقساط متأخرة: 0")

        تخطيط_الحالة.addWidget(self.تسمية_عدد_العقود)
        تخطيط_الحالة.addWidget(self.تسمية_العقود_النشطة)
        تخطيط_الحالة.addWidget(self.تسمية_إجمالي_المبالغ)
        تخطيط_الحالة.addWidget(self.تسمية_الأقساط_المتأخرة)
        تخطيط_الحالة.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الحالة)

    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS لواجهة العقود
        """
        نمط = """
        QLabel#page_title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #e67e22;
        }

        QFrame#search_frame, QFrame#buttons_frame, QFrame#status_frame {
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
        }

        QTableWidget#contracts_table {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
            gridline-color: #ecf0f1;
        }

        QTableWidget#contracts_table::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }

        QTableWidget#contracts_table::item:selected {
            background-color: #e67e22;
            color: white;
        }

        QPushButton {
            background-color: #e67e22;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            font-weight: bold;
            min-width: 100px;
        }

        QPushButton:hover {
            background-color: #d35400;
        }

        QPushButton:pressed {
            background-color: #ba4a00;
        }

        QPushButton:disabled {
            background-color: #bdc3c7;
            color: #7f8c8d;
        }

        QLineEdit, QComboBox {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 12px;
        }

        QLineEdit:focus, QComboBox:focus {
            border-color: #e67e22;
        }
        """
        self.setStyleSheet(نمط)

    def تحميل_البنوك(self):
        """
        دالة تحميل قائمة البنوك
        """
        try:
            استعلام = "SELECT اسم_البنك FROM البنوك WHERE حالة_النشاط = TRUE ORDER BY اسم_البنك"
            البنوك = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_بنك.clear()
            self.قائمة_بنك.addItem("الكل")

            if البنوك:
                for بنك in البنوك:
                    self.قائمة_بنك.addItem(بنك[0])

        except Exception as e:
            print(f"خطأ في تحميل البنوك: {str(e)}")

    def تحميل_البيانات(self):
        """
        دالة تحميل جميع العقود
        """
        try:
            استعلام = """
            SELECT
                ع.رقم_العقد,
                CONCAT(عم.الاسم_الأول, ' ', عم.اللقب) as اسم_العميل,
                ع.رقم_الفاتورة,
                ب.اسم_البنك,
                ع.مبلغ_العقد,
                ع.القسط_الشهري,
                ع.عدد_الأقساط,
                DATE_FORMAT(ع.تاريخ_بداية_العقد, '%Y-%m-%d') as تاريخ_البداية,
                DATE_FORMAT(ع.تاريخ_انتهاء_العقد, '%Y-%m-%d') as تاريخ_الانتهاء,
                ع.حالة_العقد,
                COALESCE(COUNT(ق.رقم_القسط), 0) as الأقساط_المسددة
            FROM العقود ع
            JOIN العملاء عم ON ع.رقم_العميل = عم.رقم_العميل
            JOIN البنوك ب ON ع.رقم_البنك = ب.رقم_البنك
            LEFT JOIN الأقساط ق ON ع.رقم_العقد = ق.رقم_العقد AND ق.حالة_السداد = 'مسدد'
            GROUP BY ع.رقم_العقد
            ORDER BY ع.تاريخ_الإنشاء DESC
            """

            العقود = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.جدول_العقود.setRowCount(0)

            if العقود:
                self.جدول_العقود.setRowCount(len(العقود))

                for صف, عقد in enumerate(العقود):
                    for عمود, قيمة in enumerate(عقد):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة is not None else "")

                        # تلوين حالة العقد
                        if عمود == 9:  # عمود حالة العقد
                            if قيمة == "نشط":
                                عنصر.setBackground(QColor("#d5f4e6"))
                            elif قيمة == "مكتمل":
                                عنصر.setBackground(QColor("#d1ecf1"))
                            elif قيمة == "ملغي":
                                عنصر.setBackground(QColor("#f8d7da"))

                        # تلوين الأقساط المسددة
                        elif عمود == 10:  # عمود الأقساط المسددة
                            أقساط_مسددة = int(قيمة) if قيمة else 0
                            عدد_أقساط = int(عقد[6]) if عقد[6] else 0
                            if عدد_أقساط > 0:
                                نسبة = (أقساط_مسددة / عدد_أقساط) * 100
                                عنصر.setText(f"{أقساط_مسددة}/{عدد_أقساط} ({نسبة:.1f}%)")
                                if نسبة == 100:
                                    عنصر.setBackground(QColor("#d5f4e6"))
                                elif نسبة >= 50:
                                    عنصر.setBackground(QColor("#fff3cd"))
                                else:
                                    عنصر.setBackground(QColor("#f8d7da"))

                        self.جدول_العقود.setItem(صف, عمود, عنصر)

            # تحديث الإحصائيات
            self.تحديث_الإحصائيات()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل العقود: {str(e)}")

    def تحديث_الإحصائيات(self):
        """
        دالة تحديث الإحصائيات السريعة
        """
        try:
            # عدد العقود الإجمالي
            استعلام_عدد = "SELECT COUNT(*) FROM العقود"
            عدد_العقود = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_عدد)[0][0]

            # عدد العقود النشطة
            استعلام_نشطة = "SELECT COUNT(*) FROM العقود WHERE حالة_العقد = 'نشط'"
            عدد_النشطة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_نشطة)[0][0]

            # إجمالي المبالغ
            استعلام_مبالغ = "SELECT COALESCE(SUM(مبلغ_العقد), 0) FROM العقود WHERE حالة_العقد = 'نشط'"
            إجمالي_المبالغ = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_مبالغ)[0][0]

            # عدد الأقساط المتأخرة
            استعلام_متأخرة = """
            SELECT COUNT(*) FROM الأقساط
            WHERE تاريخ_الاستحقاق < CURDATE() AND حالة_السداد = 'متأخر'
            """
            أقساط_متأخرة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_متأخرة)[0][0]

            # تحديث التسميات
            self.تسمية_عدد_العقود.setText(f"عدد العقود: {عدد_العقود}")
            self.تسمية_العقود_النشطة.setText(f"النشطة: {عدد_النشطة}")
            self.تسمية_إجمالي_المبالغ.setText(f"إجمالي المبالغ: {إجمالي_المبالغ:,.2f}")
            self.تسمية_الأقساط_المتأخرة.setText(f"أقساط متأخرة: {أقساط_متأخرة}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def البحث_عن_العقود(self):
        """
        دالة البحث والفلترة في العقود
        """
        نص_البحث = self.حقل_البحث.text().strip()
        حالة_مختارة = self.قائمة_حالة.currentText()
        بنك_مختار = self.قائمة_بنك.currentText()

        for صف in range(self.جدول_العقود.rowCount()):
            إظهار_الصف = True

            # فلترة النص
            if نص_البحث:
                وجد_النص = False
                for عمود in range(self.جدول_العقود.columnCount()):
                    عنصر = self.جدول_العقود.item(صف, عمود)
                    if عنصر and نص_البحث.lower() in عنصر.text().lower():
                        وجد_النص = True
                        break
                if not وجد_النص:
                    إظهار_الصف = False

            # فلترة الحالة
            if حالة_مختارة != "الكل" and إظهار_الصف:
                عنصر_حالة = self.جدول_العقود.item(صف, 9)
                if not عنصر_حالة or عنصر_حالة.text() != حالة_مختارة:
                    إظهار_الصف = False

            # فلترة البنك
            if بنك_مختار != "الكل" and إظهار_الصف:
                عنصر_بنك = self.جدول_العقود.item(صف, 3)
                if not عنصر_بنك or عنصر_بنك.text() != بنك_مختار:
                    إظهار_الصف = False

            self.جدول_العقود.setRowHidden(صف, not إظهار_الصف)

    def عند_تحديد_عقد(self):
        """
        دالة تنفذ عند تحديد عقد من الجدول
        """
        الصف_المحدد = self.جدول_العقود.currentRow()

        if الصف_المحدد >= 0:
            عنصر_رقم_العقد = self.جدول_العقود.item(الصف_المحدد, 0)
            if عنصر_رقم_العقد:
                self.العقد_المحدد = int(عنصر_رقم_العقد.text())

                # تفعيل أزرار الإجراءات
                for مفتاح in ["edit_contract", "view_details", "view_installments", "cancel_contract", "print_contract"]:
                    self.أزرار_الإجراءات[مفتاح].setEnabled(True)
        else:
            self.العقد_المحدد = None
            # تعطيل أزرار الإجراءات
            for مفتاح in ["edit_contract", "view_details", "view_installments", "cancel_contract", "print_contract"]:
                self.أزرار_الإجراءات[مفتاح].setEnabled(False)

    def إنشاء_عقد(self):
        """
        دالة إنشاء عقد جديد
        """
        try:
            from contract_creation_dialog import حوار_إنشاء_عقد

            حوار = حوار_إنشاء_عقد(self)
            if حوار.exec() == QDialog.Accepted:
                # تحديث البيانات بعد إنشاء العقد
                self.تحميل_البيانات()
                QMessageBox.information(self, "نجح", "تم إنشاء العقد بنجاح وتحديث البيانات")

        except ImportError:
            QMessageBox.critical(self, "خطأ", "لم يتم العثور على حوار إنشاء العقد")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح حوار إنشاء العقد: {str(e)}")

    def تعديل_عقد(self):
        """
        دالة تعديل العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تعديل العقد رقم {self.العقد_المحدد} - قيد التطوير")

    def عرض_تفاصيل_عقد(self):
        """
        دالة عرض تفاصيل العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        QMessageBox.information(self, "معلومات", f"عرض تفاصيل العقد رقم {self.العقد_المحدد} - قيد التطوير")

    def عرض_أقساط_عقد(self):
        """
        دالة عرض أقساط العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        QMessageBox.information(self, "معلومات", f"عرض أقساط العقد رقم {self.العقد_المحدد} - قيد التطوير")

    def إلغاء_عقد(self):
        """
        دالة إلغاء العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        رد = QMessageBox.question(
            self, "تأكيد الإلغاء",
            f"هل أنت متأكد من إلغاء العقد رقم {self.العقد_المحدد}؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if رد == QMessageBox.Yes:
            try:
                استعلام = "UPDATE العقود SET حالة_العقد = 'ملغي' WHERE رقم_العقد = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.العقد_المحدد,))

                QMessageBox.information(self, "نجح", "تم إلغاء العقد بنجاح")
                self.تحميل_البيانات()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إلغاء العقد: {str(e)}")

    def طباعة_عقد(self):
        """
        دالة طباعة العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        QMessageBox.information(self, "معلومات", f"طباعة العقد رقم {self.العقد_المحدد} - قيد التطوير")


class واجهة_الأقساط(QWidget):
    """
    واجهة إدارة الأقساط والديون الشاملة
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة الأقساط
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.العقد_المحدد = None
        self.القسط_المحدد = None

        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة الأقساط والديون
        """
        # التخطيط الرئيسي
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان الصفحة
        عنوان = QLabel("إدارة الأقساط والديون")
        عنوان.setObjectName("page_title")
        التخطيط_الرئيسي.addWidget(عنوان)

        # التبويبات
        self.تبويبات = QTabWidget()
        self.تبويبات.setObjectName("main_tabs")

        # تبويب العقود النشطة
        self.تبويب_العقود = تبويب_العقود_النشطة(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_العقود, "العقود النشطة")

        # تبويب الأقساط المستحقة
        self.تبويب_الأقساط_المستحقة = تبويب_الأقساط_المستحقة(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_الأقساط_المستحقة, "الأقساط المستحقة")

        # تبويب الأقساط المتأخرة
        self.تبويب_الأقساط_المتأخرة = تبويب_الأقساط_المتأخرة(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_الأقساط_المتأخرة, "الأقساط المتأخرة")

        # تبويب كشوف الحسابات
        self.تبويب_كشوف_الحسابات = تبويب_كشوف_الحسابات(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_كشوف_الحسابات, "كشوف الحسابات")

        التخطيط_الرئيسي.addWidget(self.تبويبات)

    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS لواجهة الأقساط
        """
        نمط = """
        QLabel#page_title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #e74c3c;
        }

        QTabWidget#main_tabs {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }

        QTabWidget#main_tabs::pane {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }

        QTabWidget#main_tabs::tab-bar {
            alignment: right;
        }

        QTabBar::tab {
            background-color: #ecf0f1;
            color: #2c3e50;
            padding: 8px 15px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-weight: bold;
        }

        QTabBar::tab:selected {
            background-color: #e74c3c;
            color: white;
        }

        QTabBar::tab:hover {
            background-color: #c0392b;
            color: white;
        }
        """
        self.setStyleSheet(نمط)


class تبويب_العقود_النشطة(QWidget):
    """
    تبويب عرض العقود النشطة
    """

    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب العقود النشطة
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.العقد_المحدد = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب العقود النشطة
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول العقود
        self.إنشاء_جدول_العقود(التخطيط_الرئيسي)

        # تحميل البيانات
        self.تحميل_العقود()

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("رقم العقد، اسم العميل، أو رقم الفاتورة...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_العقود)

        # فلتر حالة العقد
        تسمية_حالة = QLabel("حالة العقد:")
        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "نشط", "مكتمل", "ملغي"])
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_العقود)

        # فلتر البنك
        تسمية_بنك = QLabel("البنك:")
        self.قائمة_بنك = QComboBox()
        self.تحميل_البنوك()
        self.قائمة_بنك.currentTextChanged.connect(self.البحث_عن_العقود)

        # إضافة العناصر للتخطيط
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(تسمية_حالة)
        تخطيط_البحث.addWidget(self.قائمة_حالة)
        تخطيط_البحث.addWidget(تسمية_بنك)
        تخطيط_البحث.addWidget(self.قائمة_بنك)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الأزرار
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(60)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(10)

        # أزرار الإجراءات
        self.أزرار_الإجراءات = {}

        # زر عرض تفاصيل العقد
        self.أزرار_الإجراءات["view_contract"] = QPushButton("عرض التفاصيل")
        self.أزرار_الإجراءات["view_contract"].clicked.connect(self.عرض_تفاصيل_عقد)

        # زر عرض الأقساط
        self.أزرار_الإجراءات["view_installments"] = QPushButton("عرض الأقساط")
        self.أزرار_الإجراءات["view_installments"].clicked.connect(self.عرض_أقساط_عقد)

        # زر تحديث العقد
        self.أزرار_الإجراءات["update_contract"] = QPushButton("تحديث العقد")
        self.أزرار_الإجراءات["update_contract"].clicked.connect(self.تحديث_عقد)

        # زر إلغاء العقد
        self.أزرار_الإجراءات["cancel_contract"] = QPushButton("إلغاء العقد")
        self.أزرار_الإجراءات["cancel_contract"].clicked.connect(self.إلغاء_عقد)

        # زر تحديث البيانات
        زر_تحديث = QPushButton("تحديث البيانات")
        زر_تحديث.clicked.connect(self.تحميل_العقود)

        # إضافة الأزرار للتخطيط
        for زر in self.أزرار_الإجراءات.values():
            تخطيط_الأزرار.addWidget(زر)
            زر.setEnabled(False)  # تعطيل الأزرار في البداية

        تخطيط_الأزرار.addWidget(زر_تحديث)
        تخطيط_الأزرار.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_جدول_العقود(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول العقود
        """
        self.جدول_العقود = QTableWidget()
        self.جدول_العقود.setObjectName("contracts_table")

        # تحديد الأعمدة
        الأعمدة = [
            "رقم العقد", "اسم العميل", "رقم الفاتورة", "البنك",
            "مبلغ العقد", "القسط الشهري", "عدد الأقساط",
            "تاريخ البداية", "تاريخ الانتهاء", "حالة العقد"
        ]

        self.جدول_العقود.setColumnCount(len(الأعمدة))
        self.جدول_العقود.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_العقود.setAlternatingRowColors(True)
        self.جدول_العقود.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_العقود.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_العقود.setSortingEnabled(True)

        # ضبط عرض الأعمدة
        header = self.جدول_العقود.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(الأعمدة)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # ربط الأحداث
        self.جدول_العقود.itemSelectionChanged.connect(self.عند_تحديد_عقد)
        self.جدول_العقود.itemDoubleClicked.connect(self.عرض_تفاصيل_عقد)

        التخطيط_الرئيسي.addWidget(self.جدول_العقود)

    def تحميل_البنوك(self):
        """
        دالة تحميل قائمة البنوك
        """
        try:
            استعلام = "SELECT اسم_البنك FROM البنوك WHERE حالة_النشاط = TRUE ORDER BY اسم_البنك"
            البنوك = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_بنك.clear()
            self.قائمة_بنك.addItem("الكل")

            if البنوك:
                for بنك in البنوك:
                    self.قائمة_بنك.addItem(بنك[0])

        except Exception as e:
            print(f"خطأ في تحميل البنوك: {str(e)}")

    def تحميل_العقود(self):
        """
        دالة تحميل جميع العقود النشطة
        """
        try:
            استعلام = """
            SELECT
                ع.رقم_العقد,
                CONCAT(عم.الاسم_الأول, ' ', عم.اللقب) as اسم_العميل,
                ع.رقم_الفاتورة,
                ب.اسم_البنك,
                ع.مبلغ_العقد,
                ع.القسط_الشهري,
                ع.عدد_الأقساط,
                DATE_FORMAT(ع.تاريخ_بداية_العقد, '%Y-%m-%d') as تاريخ_البداية,
                DATE_FORMAT(ع.تاريخ_انتهاء_العقد, '%Y-%m-%d') as تاريخ_الانتهاء,
                ع.حالة_العقد
            FROM العقود ع
            JOIN العملاء عم ON ع.رقم_العميل = عم.رقم_العميل
            JOIN البنوك ب ON ع.رقم_البنك = ب.رقم_البنك
            ORDER BY ع.تاريخ_الإنشاء DESC
            """

            العقود = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.جدول_العقود.setRowCount(0)

            if العقود:
                self.جدول_العقود.setRowCount(len(العقود))

                for صف, عقد in enumerate(العقود):
                    for عمود, قيمة in enumerate(عقد):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة is not None else "")

                        # تلوين حالة العقد
                        if عمود == 9:  # عمود حالة العقد
                            if قيمة == "نشط":
                                عنصر.setBackground(QColor("#d5f4e6"))
                            elif قيمة == "مكتمل":
                                عنصر.setBackground(QColor("#d1ecf1"))
                            elif قيمة == "ملغي":
                                عنصر.setBackground(QColor("#f8d7da"))

                        self.جدول_العقود.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل العقود: {str(e)}")

    def البحث_عن_العقود(self):
        """
        دالة البحث والفلترة في العقود
        """
        نص_البحث = self.حقل_البحث.text().strip()
        حالة_مختارة = self.قائمة_حالة.currentText()
        بنك_مختار = self.قائمة_بنك.currentText()

        for صف in range(self.جدول_العقود.rowCount()):
            إظهار_الصف = True

            # فلترة النص
            if نص_البحث:
                وجد_النص = False
                for عمود in range(self.جدول_العقود.columnCount()):
                    عنصر = self.جدول_العقود.item(صف, عمود)
                    if عنصر and نص_البحث.lower() in عنصر.text().lower():
                        وجد_النص = True
                        break
                if not وجد_النص:
                    إظهار_الصف = False

            # فلترة الحالة
            if حالة_مختارة != "الكل" and إظهار_الصف:
                عنصر_حالة = self.جدول_العقود.item(صف, 9)
                if not عنصر_حالة or عنصر_حالة.text() != حالة_مختارة:
                    إظهار_الصف = False

            # فلترة البنك
            if بنك_مختار != "الكل" and إظهار_الصف:
                عنصر_بنك = self.جدول_العقود.item(صف, 3)
                if not عنصر_بنك or عنصر_بنك.text() != بنك_مختار:
                    إظهار_الصف = False

            self.جدول_العقود.setRowHidden(صف, not إظهار_الصف)

    def عند_تحديد_عقد(self):
        """
        دالة تنفذ عند تحديد عقد من الجدول
        """
        الصف_المحدد = self.جدول_العقود.currentRow()

        if الصف_المحدد >= 0:
            عنصر_رقم_العقد = self.جدول_العقود.item(الصف_المحدد, 0)
            if عنصر_رقم_العقد:
                self.العقد_المحدد = int(عنصر_رقم_العقد.text())

                # تفعيل أزرار الإجراءات
                for زر in self.أزرار_الإجراءات.values():
                    زر.setEnabled(True)
        else:
            self.العقد_المحدد = None
            # تعطيل أزرار الإجراءات
            for زر in self.أزرار_الإجراءات.values():
                زر.setEnabled(False)

    def عرض_تفاصيل_عقد(self):
        """
        دالة عرض تفاصيل العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        QMessageBox.information(self, "معلومات", f"عرض تفاصيل العقد رقم {self.العقد_المحدد} - قيد التطوير")

    def عرض_أقساط_عقد(self):
        """
        دالة عرض أقساط العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        QMessageBox.information(self, "معلومات", f"عرض أقساط العقد رقم {self.العقد_المحدد} - قيد التطوير")

    def تحديث_عقد(self):
        """
        دالة تحديث العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تحديث العقد رقم {self.العقد_المحدد} - قيد التطوير")

    def إلغاء_عقد(self):
        """
        دالة إلغاء العقد المحدد
        """
        if not self.العقد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عقد أولاً")
            return

        رد = QMessageBox.question(
            self, "تأكيد الإلغاء",
            f"هل أنت متأكد من إلغاء العقد رقم {self.العقد_المحدد}؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if رد == QMessageBox.Yes:
            try:
                استعلام = "UPDATE العقود SET حالة_العقد = 'ملغي' WHERE رقم_العقد = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.العقد_المحدد,))

                QMessageBox.information(self, "نجح", "تم إلغاء العقد بنجاح")
                self.تحميل_العقود()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إلغاء العقد: {str(e)}")


class تبويب_الأقساط_المستحقة(QWidget):
    """
    تبويب عرض الأقساط المستحقة
    """

    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب الأقساط المستحقة
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.القسط_المحدد = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب الأقساط المستحقة
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول الأقساط
        self.إنشاء_جدول_الأقساط(التخطيط_الرئيسي)

        # تحميل البيانات
        self.تحميل_الأقساط_المستحقة()

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("اسم العميل، رقم العقد، أو رقم القسط...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الأقساط)

        # فلتر فترة الاستحقاق
        تسمية_فترة = QLabel("فترة الاستحقاق:")
        self.قائمة_فترة = QComboBox()
        self.قائمة_فترة.addItems(["الكل", "هذا الأسبوع", "هذا الشهر", "الشهر القادم", "متأخرة"])
        self.قائمة_فترة.currentTextChanged.connect(self.البحث_عن_الأقساط)

        # فلتر المبلغ
        تسمية_مبلغ = QLabel("المبلغ من:")
        self.حقل_مبلغ_من = QLineEdit()
        self.حقل_مبلغ_من.setPlaceholderText("0")
        تسمية_مبلغ_إلى = QLabel("إلى:")
        self.حقل_مبلغ_إلى = QLineEdit()
        self.حقل_مبلغ_إلى.setPlaceholderText("999999")

        # إضافة العناصر للتخطيط
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(تسمية_فترة)
        تخطيط_البحث.addWidget(self.قائمة_فترة)
        تخطيط_البحث.addWidget(تسمية_مبلغ)
        تخطيط_البحث.addWidget(self.حقل_مبلغ_من)
        تخطيط_البحث.addWidget(تسمية_مبلغ_إلى)
        تخطيط_البحث.addWidget(self.حقل_مبلغ_إلى)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الأزرار
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(60)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(10)

        # أزرار الإجراءات
        self.أزرار_الإجراءات = {}

        # زر تسجيل دفعة
        self.أزرار_الإجراءات["pay_installment"] = QPushButton("تسجيل دفعة")
        self.أزرار_الإجراءات["pay_installment"].clicked.connect(self.تسجيل_دفعة)

        # زر عرض تفاصيل القسط
        self.أزرار_الإجراءات["view_details"] = QPushButton("عرض التفاصيل")
        self.أزرار_الإجراءات["view_details"].clicked.connect(self.عرض_تفاصيل_قسط)

        # زر إرسال تذكير
        self.أزرار_الإجراءات["send_reminder"] = QPushButton("إرسال تذكير")
        self.أزرار_الإجراءات["send_reminder"].clicked.connect(self.إرسال_تذكير)

        # زر طباعة كشف حساب
        self.أزرار_الإجراءات["print_statement"] = QPushButton("طباعة كشف حساب")
        self.أزرار_الإجراءات["print_statement"].clicked.connect(self.طباعة_كشف_حساب)

        # زر تحديث البيانات
        زر_تحديث = QPushButton("تحديث البيانات")
        زر_تحديث.clicked.connect(self.تحميل_الأقساط_المستحقة)

        # إضافة الأزرار للتخطيط
        for زر in self.أزرار_الإجراءات.values():
            تخطيط_الأزرار.addWidget(زر)
            زر.setEnabled(False)  # تعطيل الأزرار في البداية

        تخطيط_الأزرار.addWidget(زر_تحديث)
        تخطيط_الأزرار.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_جدول_الأقساط(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول الأقساط المستحقة
        """
        self.جدول_الأقساط = QTableWidget()
        self.جدول_الأقساط.setObjectName("installments_table")

        # تحديد الأعمدة
        الأعمدة = [
            "رقم القسط", "رقم العقد", "اسم العميل", "رقم القسط في العقد",
            "مبلغ القسط", "تاريخ الاستحقاق", "المبلغ المدفوع",
            "المبلغ المتبقي", "حالة السداد", "الأيام المتأخرة"
        ]

        self.جدول_الأقساط.setColumnCount(len(الأعمدة))
        self.جدول_الأقساط.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_الأقساط.setAlternatingRowColors(True)
        self.جدول_الأقساط.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الأقساط.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_الأقساط.setSortingEnabled(True)

        # ضبط عرض الأعمدة
        header = self.جدول_الأقساط.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(الأعمدة)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # ربط الأحداث
        self.جدول_الأقساط.itemSelectionChanged.connect(self.عند_تحديد_قسط)
        self.جدول_الأقساط.itemDoubleClicked.connect(self.تسجيل_دفعة)

        التخطيط_الرئيسي.addWidget(self.جدول_الأقساط)

    def تحميل_الأقساط_المستحقة(self):
        """
        دالة تحميل الأقساط المستحقة
        """
        try:
            استعلام = """
            SELECT
                ق.رقم_القسط,
                ق.رقم_العقد,
                CONCAT(عم.الاسم_الأول, ' ', عم.اللقب) as اسم_العميل,
                ق.رقم_القسط_في_العقد,
                ق.مبلغ_القسط,
                DATE_FORMAT(ق.تاريخ_الاستحقاق, '%Y-%m-%d') as تاريخ_الاستحقاق,
                ق.المبلغ_المدفوع,
                (ق.مبلغ_القسط - ق.المبلغ_المدفوع) as المبلغ_المتبقي,
                ق.حالة_السداد,
                DATEDIFF(CURDATE(), ق.تاريخ_الاستحقاق) as الأيام_المتأخرة
            FROM الأقساط ق
            JOIN العقود ع ON ق.رقم_العقد = ع.رقم_العقد
            JOIN العملاء عم ON ع.رقم_العميل = عم.رقم_العميل
            WHERE ق.حالة_السداد IN ('غير مسدد', 'متأخر')
            ORDER BY ق.تاريخ_الاستحقاق ASC
            """

            الأقساط = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.جدول_الأقساط.setRowCount(0)

            if الأقساط:
                self.جدول_الأقساط.setRowCount(len(الأقساط))

                for صف, قسط in enumerate(الأقساط):
                    for عمود, قيمة in enumerate(قسط):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة is not None else "")

                        # تلوين حالة السداد والأيام المتأخرة
                        if عمود == 8:  # عمود حالة السداد
                            if قيمة == "غير مسدد":
                                عنصر.setBackground(QColor("#fff3cd"))
                            elif قيمة == "متأخر":
                                عنصر.setBackground(QColor("#f8d7da"))
                        elif عمود == 9:  # عمود الأيام المتأخرة
                            أيام = int(قيمة) if قيمة is not None else 0
                            if أيام > 0:
                                عنصر.setBackground(QColor("#f8d7da"))
                                عنصر.setForeground(QColor("#721c24"))
                            elif أيام >= -7:  # مستحق خلال أسبوع
                                عنصر.setBackground(QColor("#fff3cd"))

                        self.جدول_الأقساط.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الأقساط المستحقة: {str(e)}")

    def البحث_عن_الأقساط(self):
        """
        دالة البحث والفلترة في الأقساط
        """
        نص_البحث = self.حقل_البحث.text().strip()
        فترة_مختارة = self.قائمة_فترة.currentText()

        for صف in range(self.جدول_الأقساط.rowCount()):
            إظهار_الصف = True

            # فلترة النص
            if نص_البحث:
                وجد_النص = False
                for عمود in range(self.جدول_الأقساط.columnCount()):
                    عنصر = self.جدول_الأقساط.item(صف, عمود)
                    if عنصر and نص_البحث.lower() in عنصر.text().lower():
                        وجد_النص = True
                        break
                if not وجد_النص:
                    إظهار_الصف = False

            # فلترة الفترة
            if فترة_مختارة != "الكل" and إظهار_الصف:
                عنصر_أيام = self.جدول_الأقساط.item(صف, 9)
                if عنصر_أيام:
                    أيام = int(عنصر_أيام.text()) if عنصر_أيام.text().isdigit() or عنصر_أيام.text().startswith('-') else 0

                    if فترة_مختارة == "هذا الأسبوع" and not (-7 <= أيام <= 0):
                        إظهار_الصف = False
                    elif فترة_مختارة == "هذا الشهر" and not (-30 <= أيام <= 0):
                        إظهار_الصف = False
                    elif فترة_مختارة == "الشهر القادم" and not (-60 <= أيام <= -30):
                        إظهار_الصف = False
                    elif فترة_مختارة == "متأخرة" and أيام <= 0:
                        إظهار_الصف = False

            self.جدول_الأقساط.setRowHidden(صف, not إظهار_الصف)

    def عند_تحديد_قسط(self):
        """
        دالة تنفذ عند تحديد قسط من الجدول
        """
        الصف_المحدد = self.جدول_الأقساط.currentRow()

        if الصف_المحدد >= 0:
            عنصر_رقم_القسط = self.جدول_الأقساط.item(الصف_المحدد, 0)
            if عنصر_رقم_القسط:
                self.القسط_المحدد = int(عنصر_رقم_القسط.text())

                # تفعيل أزرار الإجراءات
                for زر in self.أزرار_الإجراءات.values():
                    زر.setEnabled(True)
        else:
            self.القسط_المحدد = None
            # تعطيل أزرار الإجراءات
            for زر in self.أزرار_الإجراءات.values():
                زر.setEnabled(False)

    def تسجيل_دفعة(self):
        """
        دالة تسجيل دفعة للقسط المحدد
        """
        if not self.القسط_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قسط أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تسجيل دفعة للقسط رقم {self.القسط_المحدد} - قيد التطوير")

    def عرض_تفاصيل_قسط(self):
        """
        دالة عرض تفاصيل القسط المحدد
        """
        if not self.القسط_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قسط أولاً")
            return

        QMessageBox.information(self, "معلومات", f"عرض تفاصيل القسط رقم {self.القسط_المحدد} - قيد التطوير")

    def إرسال_تذكير(self):
        """
        دالة إرسال تذكير للعميل
        """
        if not self.القسط_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قسط أولاً")
            return

        QMessageBox.information(self, "معلومات", f"إرسال تذكير للقسط رقم {self.القسط_المحدد} - قيد التطوير")

    def طباعة_كشف_حساب(self):
        """
        دالة طباعة كشف حساب العميل
        """
        if not self.القسط_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قسط أولاً")
            return

        QMessageBox.information(self, "معلومات", f"طباعة كشف حساب للقسط رقم {self.القسط_المحدد} - قيد التطوير")


class تبويب_الأقساط_المتأخرة(QWidget):
    """
    تبويب عرض الأقساط المتأخرة
    """

    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب الأقساط المتأخرة
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.القسط_المحدد = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب الأقساط المتأخرة
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # إحصائيات سريعة
        self.إنشاء_إحصائيات_سريعة(التخطيط_الرئيسي)

        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول الأقساط المتأخرة
        self.إنشاء_جدول_الأقساط_المتأخرة(التخطيط_الرئيسي)

        # تحميل البيانات
        self.تحميل_الأقساط_المتأخرة()

    def إنشاء_إحصائيات_سريعة(self, التخطيط_الرئيسي):
        """
        دالة إنشاء إحصائيات سريعة للأقساط المتأخرة
        """
        إطار_الإحصائيات = QFrame()
        إطار_الإحصائيات.setObjectName("stats_frame")
        إطار_الإحصائيات.setFixedHeight(80)

        تخطيط_الإحصائيات = QHBoxLayout(إطار_الإحصائيات)
        تخطيط_الإحصائيات.setContentsMargins(15, 10, 15, 10)
        تخطيط_الإحصائيات.setSpacing(20)

        # عدد الأقساط المتأخرة
        self.تسمية_عدد_الأقساط = QLabel("عدد الأقساط المتأخرة: 0")
        self.تسمية_عدد_الأقساط.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")

        # إجمالي المبلغ المتأخر
        self.تسمية_إجمالي_المبلغ = QLabel("إجمالي المبلغ المتأخر: 0.00")
        self.تسمية_إجمالي_المبلغ.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")

        # متوسط أيام التأخير
        self.تسمية_متوسط_التأخير = QLabel("متوسط أيام التأخير: 0")
        self.تسمية_متوسط_التأخير.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")

        تخطيط_الإحصائيات.addWidget(self.تسمية_عدد_الأقساط)
        تخطيط_الإحصائيات.addWidget(self.تسمية_إجمالي_المبلغ)
        تخطيط_الإحصائيات.addWidget(self.تسمية_متوسط_التأخير)
        تخطيط_الإحصائيات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الإحصائيات)

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("اسم العميل، رقم العقد، أو رقم القسط...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الأقساط)

        # فلتر مدة التأخير
        تسمية_مدة = QLabel("مدة التأخير:")
        self.قائمة_مدة = QComboBox()
        self.قائمة_مدة.addItems(["الكل", "1-7 أيام", "8-30 يوم", "31-90 يوم", "أكثر من 90 يوم"])
        self.قائمة_مدة.currentTextChanged.connect(self.البحث_عن_الأقساط)

        # فلتر المبلغ
        تسمية_مبلغ = QLabel("المبلغ أكبر من:")
        self.حقل_مبلغ_أدنى = QLineEdit()
        self.حقل_مبلغ_أدنى.setPlaceholderText("0")

        # إضافة العناصر للتخطيط
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(تسمية_مدة)
        تخطيط_البحث.addWidget(self.قائمة_مدة)
        تخطيط_البحث.addWidget(تسمية_مبلغ)
        تخطيط_البحث.addWidget(self.حقل_مبلغ_أدنى)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الأزرار
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(60)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(10)

        # أزرار الإجراءات
        self.أزرار_الإجراءات = {}

        # زر تسجيل دفعة عاجلة
        self.أزرار_الإجراءات["urgent_payment"] = QPushButton("دفعة عاجلة")
        self.أزرار_الإجراءات["urgent_payment"].clicked.connect(self.تسجيل_دفعة_عاجلة)

        # زر إرسال إنذار
        self.أزرار_الإجراءات["send_warning"] = QPushButton("إرسال إنذار")
        self.أزرار_الإجراءات["send_warning"].clicked.connect(self.إرسال_إنذار)

        # زر تجميد العقد
        self.أزرار_الإجراءات["freeze_contract"] = QPushButton("تجميد العقد")
        self.أزرار_الإجراءات["freeze_contract"].clicked.connect(self.تجميد_عقد)

        # زر تقرير متأخرات
        self.أزرار_الإجراءات["overdue_report"] = QPushButton("تقرير المتأخرات")
        self.أزرار_الإجراءات["overdue_report"].clicked.connect(self.تقرير_متأخرات)

        # زر تحديث البيانات
        زر_تحديث = QPushButton("تحديث البيانات")
        زر_تحديث.clicked.connect(self.تحميل_الأقساط_المتأخرة)

        # إضافة الأزرار للتخطيط
        for زر in self.أزرار_الإجراءات.values():
            تخطيط_الأزرار.addWidget(زر)
            زر.setEnabled(False)  # تعطيل الأزرار في البداية

        تخطيط_الأزرار.addWidget(زر_تحديث)
        تخطيط_الأزرار.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_جدول_الأقساط_المتأخرة(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول الأقساط المتأخرة
        """
        self.جدول_الأقساط_المتأخرة = QTableWidget()
        self.جدول_الأقساط_المتأخرة.setObjectName("overdue_table")

        # تحديد الأعمدة
        الأعمدة = [
            "رقم القسط", "رقم العقد", "اسم العميل", "رقم الهاتف",
            "مبلغ القسط", "تاريخ الاستحقاق", "أيام التأخير",
            "المبلغ المتبقي", "آخر اتصال", "ملاحظات"
        ]

        self.جدول_الأقساط_المتأخرة.setColumnCount(len(الأعمدة))
        self.جدول_الأقساط_المتأخرة.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_الأقساط_المتأخرة.setAlternatingRowColors(True)
        self.جدول_الأقساط_المتأخرة.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الأقساط_المتأخرة.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_الأقساط_المتأخرة.setSortingEnabled(True)

        # ضبط عرض الأعمدة
        header = self.جدول_الأقساط_المتأخرة.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(الأعمدة)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # ربط الأحداث
        self.جدول_الأقساط_المتأخرة.itemSelectionChanged.connect(self.عند_تحديد_قسط_متأخر)
        self.جدول_الأقساط_المتأخرة.itemDoubleClicked.connect(self.تسجيل_دفعة_عاجلة)

        التخطيط_الرئيسي.addWidget(self.جدول_الأقساط_المتأخرة)

    def تحميل_الأقساط_المتأخرة(self):
        """
        دالة تحميل الأقساط المتأخرة
        """
        try:
            استعلام = """
            SELECT
                ق.رقم_القسط,
                ق.رقم_العقد,
                CONCAT(عم.الاسم_الأول, ' ', عم.اللقب) as اسم_العميل,
                عم.رقم_الهاتف,
                ق.مبلغ_القسط,
                DATE_FORMAT(ق.تاريخ_الاستحقاق, '%Y-%m-%d') as تاريخ_الاستحقاق,
                DATEDIFF(CURDATE(), ق.تاريخ_الاستحقاق) as أيام_التأخير,
                (ق.مبلغ_القسط - ق.المبلغ_المدفوع) as المبلغ_المتبقي,
                'لم يتم الاتصال' as آخر_اتصال,
                ق.ملاحظات
            FROM الأقساط ق
            JOIN العقود ع ON ق.رقم_العقد = ع.رقم_العقد
            JOIN العملاء عم ON ع.رقم_العميل = عم.رقم_العميل
            WHERE ق.تاريخ_الاستحقاق < CURDATE()
            AND ق.حالة_السداد = 'متأخر'
            ORDER BY أيام_التأخير DESC, ق.مبلغ_القسط DESC
            """

            الأقساط = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.جدول_الأقساط_المتأخرة.setRowCount(0)

            # تحديث الإحصائيات
            self.تحديث_الإحصائيات(الأقساط)

            if الأقساط:
                self.جدول_الأقساط_المتأخرة.setRowCount(len(الأقساط))

                for صف, قسط in enumerate(الأقساط):
                    for عمود, قيمة in enumerate(قسط):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة is not None else "")

                        # تلوين حسب مدة التأخير
                        if عمود == 6:  # عمود أيام التأخير
                            أيام = int(قيمة) if قيمة is not None else 0
                            if أيام > 90:
                                عنصر.setBackground(QColor("#721c24"))
                                عنصر.setForeground(QColor("white"))
                            elif أيام > 30:
                                عنصر.setBackground(QColor("#f8d7da"))
                            elif أيام > 7:
                                عنصر.setBackground(QColor("#fff3cd"))

                        self.جدول_الأقساط_المتأخرة.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الأقساط المتأخرة: {str(e)}")

    def تحديث_الإحصائيات(self, الأقساط):
        """
        دالة تحديث الإحصائيات السريعة
        """
        if الأقساط:
            عدد_الأقساط = len(الأقساط)
            إجمالي_المبلغ = sum(float(قسط[7]) for قسط in الأقساط if قسط[7])
            متوسط_التأخير = sum(int(قسط[6]) for قسط in الأقساط if قسط[6]) / عدد_الأقساط

            self.تسمية_عدد_الأقساط.setText(f"عدد الأقساط المتأخرة: {عدد_الأقساط}")
            self.تسمية_إجمالي_المبلغ.setText(f"إجمالي المبلغ المتأخر: {إجمالي_المبلغ:,.2f}")
            self.تسمية_متوسط_التأخير.setText(f"متوسط أيام التأخير: {متوسط_التأخير:.0f}")
        else:
            self.تسمية_عدد_الأقساط.setText("عدد الأقساط المتأخرة: 0")
            self.تسمية_إجمالي_المبلغ.setText("إجمالي المبلغ المتأخر: 0.00")
            self.تسمية_متوسط_التأخير.setText("متوسط أيام التأخير: 0")

    def البحث_عن_الأقساط(self):
        """
        دالة البحث والفلترة في الأقساط المتأخرة
        """
        نص_البحث = self.حقل_البحث.text().strip()
        مدة_مختارة = self.قائمة_مدة.currentText()

        for صف in range(self.جدول_الأقساط_المتأخرة.rowCount()):
            إظهار_الصف = True

            # فلترة النص
            if نص_البحث:
                وجد_النص = False
                for عمود in range(self.جدول_الأقساط_المتأخرة.columnCount()):
                    عنصر = self.جدول_الأقساط_المتأخرة.item(صف, عمود)
                    if عنصر and نص_البحث.lower() in عنصر.text().lower():
                        وجد_النص = True
                        break
                if not وجد_النص:
                    إظهار_الصف = False

            # فلترة مدة التأخير
            if مدة_مختارة != "الكل" and إظهار_الصف:
                عنصر_أيام = self.جدول_الأقساط_المتأخرة.item(صف, 6)
                if عنصر_أيام:
                    أيام = int(عنصر_أيام.text()) if عنصر_أيام.text().isdigit() else 0

                    if مدة_مختارة == "1-7 أيام" and not (1 <= أيام <= 7):
                        إظهار_الصف = False
                    elif مدة_مختارة == "8-30 يوم" and not (8 <= أيام <= 30):
                        إظهار_الصف = False
                    elif مدة_مختارة == "31-90 يوم" and not (31 <= أيام <= 90):
                        إظهار_الصف = False
                    elif مدة_مختارة == "أكثر من 90 يوم" and أيام <= 90:
                        إظهار_الصف = False

            self.جدول_الأقساط_المتأخرة.setRowHidden(صف, not إظهار_الصف)

    def عند_تحديد_قسط_متأخر(self):
        """
        دالة تنفذ عند تحديد قسط متأخر من الجدول
        """
        الصف_المحدد = self.جدول_الأقساط_المتأخرة.currentRow()

        if الصف_المحدد >= 0:
            عنصر_رقم_القسط = self.جدول_الأقساط_المتأخرة.item(الصف_المحدد, 0)
            if عنصر_رقم_القسط:
                self.القسط_المحدد = int(عنصر_رقم_القسط.text())

                # تفعيل أزرار الإجراءات
                for زر in self.أزرار_الإجراءات.values():
                    زر.setEnabled(True)
        else:
            self.القسط_المحدد = None
            # تعطيل أزرار الإجراءات
            for زر in self.أزرار_الإجراءات.values():
                زر.setEnabled(False)

    def تسجيل_دفعة_عاجلة(self):
        """
        دالة تسجيل دفعة عاجلة للقسط المتأخر
        """
        if not self.القسط_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قسط أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تسجيل دفعة عاجلة للقسط رقم {self.القسط_المحدد} - قيد التطوير")

    def إرسال_إنذار(self):
        """
        دالة إرسال إنذار للعميل
        """
        if not self.القسط_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قسط أولاً")
            return

        QMessageBox.information(self, "معلومات", f"إرسال إنذار للقسط رقم {self.القسط_المحدد} - قيد التطوير")

    def تجميد_عقد(self):
        """
        دالة تجميد العقد
        """
        if not self.القسط_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قسط أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تجميد العقد للقسط رقم {self.القسط_المحدد} - قيد التطوير")

    def تقرير_متأخرات(self):
        """
        دالة إنشاء تقرير المتأخرات
        """
        QMessageBox.information(self, "معلومات", "تقرير المتأخرات - قيد التطوير")


class تبويب_كشوف_الحسابات(QWidget):
    """
    تبويب كشوف حسابات العملاء
    """

    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب كشوف الحسابات
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.العميل_المحدد = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب كشوف الحسابات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # شريط اختيار العميل
        self.إنشاء_شريط_اختيار_العميل(التخطيط_الرئيسي)

        # معلومات العميل
        self.إنشاء_معلومات_العميل(التخطيط_الرئيسي)

        # جدول كشف الحساب
        self.إنشاء_جدول_كشف_الحساب(التخطيط_الرئيسي)

        # ملخص الحساب
        self.إنشاء_ملخص_الحساب(التخطيط_الرئيسي)

    def إنشاء_شريط_اختيار_العميل(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط اختيار العميل
        """
        إطار_اختيار = QFrame()
        إطار_اختيار.setObjectName("selection_frame")
        إطار_اختيار.setFixedHeight(70)

        تخطيط_اختيار = QHBoxLayout(إطار_اختيار)
        تخطيط_اختيار.setContentsMargins(15, 10, 15, 10)
        تخطيط_اختيار.setSpacing(15)

        # اختيار العميل
        تسمية_عميل = QLabel("اختر العميل:")
        self.قائمة_العملاء = QComboBox()
        self.قائمة_العملاء.setMinimumWidth(300)
        self.تحميل_العملاء()
        self.قائمة_العملاء.currentTextChanged.connect(self.عند_اختيار_عميل)

        # فترة الكشف
        تسمية_فترة = QLabel("الفترة:")
        self.قائمة_فترة = QComboBox()
        self.قائمة_فترة.addItems(["آخر 3 شهور", "آخر 6 شهور", "آخر سنة", "كامل السجل"])
        self.قائمة_فترة.currentTextChanged.connect(self.تحميل_كشف_الحساب)

        # زر طباعة
        زر_طباعة = QPushButton("طباعة الكشف")
        زر_طباعة.clicked.connect(self.طباعة_كشف_الحساب)

        # زر تصدير
        زر_تصدير = QPushButton("تصدير PDF")
        زر_تصدير.clicked.connect(self.تصدير_كشف_الحساب)

        تخطيط_اختيار.addWidget(تسمية_عميل)
        تخطيط_اختيار.addWidget(self.قائمة_العملاء)
        تخطيط_اختيار.addWidget(تسمية_فترة)
        تخطيط_اختيار.addWidget(self.قائمة_فترة)
        تخطيط_اختيار.addWidget(زر_طباعة)
        تخطيط_اختيار.addWidget(زر_تصدير)
        تخطيط_اختيار.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_اختيار)

    def إنشاء_معلومات_العميل(self, التخطيط_الرئيسي):
        """
        دالة إنشاء قسم معلومات العميل
        """
        إطار_معلومات = QFrame()
        إطار_معلومات.setObjectName("info_frame")
        إطار_معلومات.setFixedHeight(100)

        تخطيط_معلومات = QHBoxLayout(إطار_معلومات)
        تخطيط_معلومات.setContentsMargins(15, 10, 15, 10)
        تخطيط_معلومات.setSpacing(20)

        # معلومات العميل
        self.تسمية_اسم_العميل = QLabel("اسم العميل: غير محدد")
        self.تسمية_رقم_هاتف = QLabel("رقم الهاتف: غير محدد")
        self.تسمية_عدد_العقود = QLabel("عدد العقود: 0")
        self.تسمية_الرصيد_الحالي = QLabel("الرصيد الحالي: 0.00")

        # تنسيق التسميات
        for تسمية in [self.تسمية_اسم_العميل, self.تسمية_رقم_هاتف,
                      self.تسمية_عدد_العقود, self.تسمية_الرصيد_الحالي]:
            تسمية.setStyleSheet("font-weight: bold; font-size: 12px; color: #2c3e50;")

        تخطيط_معلومات.addWidget(self.تسمية_اسم_العميل)
        تخطيط_معلومات.addWidget(self.تسمية_رقم_هاتف)
        تخطيط_معلومات.addWidget(self.تسمية_عدد_العقود)
        تخطيط_معلومات.addWidget(self.تسمية_الرصيد_الحالي)
        تخطيط_معلومات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_معلومات)

    def إنشاء_جدول_كشف_الحساب(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول كشف الحساب
        """
        self.جدول_كشف_الحساب = QTableWidget()
        self.جدول_كشف_الحساب.setObjectName("statement_table")

        # تحديد الأعمدة
        الأعمدة = [
            "التاريخ", "نوع العملية", "رقم المرجع", "الوصف",
            "المبلغ المدين", "المبلغ الدائن", "الرصيد", "ملاحظات"
        ]

        self.جدول_كشف_الحساب.setColumnCount(len(الأعمدة))
        self.جدول_كشف_الحساب.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_كشف_الحساب.setAlternatingRowColors(True)
        self.جدول_كشف_الحساب.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_كشف_الحساب.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_كشف_الحساب.setSortingEnabled(True)

        # ضبط عرض الأعمدة
        header = self.جدول_كشف_الحساب.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(الأعمدة)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        التخطيط_الرئيسي.addWidget(self.جدول_كشف_الحساب)

    def إنشاء_ملخص_الحساب(self, التخطيط_الرئيسي):
        """
        دالة إنشاء ملخص الحساب
        """
        إطار_ملخص = QFrame()
        إطار_ملخص.setObjectName("summary_frame")
        إطار_ملخص.setFixedHeight(80)

        تخطيط_ملخص = QHBoxLayout(إطار_ملخص)
        تخطيط_ملخص.setContentsMargins(15, 10, 15, 10)
        تخطيط_ملخص.setSpacing(20)

        # ملخص الحساب
        self.تسمية_إجمالي_المدين = QLabel("إجمالي المدين: 0.00")
        self.تسمية_إجمالي_الدائن = QLabel("إجمالي الدائن: 0.00")
        self.تسمية_الرصيد_النهائي = QLabel("الرصيد النهائي: 0.00")
        self.تسمية_عدد_العمليات = QLabel("عدد العمليات: 0")

        # تنسيق التسميات
        for تسمية in [self.تسمية_إجمالي_المدين, self.تسمية_إجمالي_الدائن,
                      self.تسمية_الرصيد_النهائي, self.تسمية_عدد_العمليات]:
            تسمية.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 5px;")

        تخطيط_ملخص.addWidget(self.تسمية_إجمالي_المدين)
        تخطيط_ملخص.addWidget(self.تسمية_إجمالي_الدائن)
        تخطيط_ملخص.addWidget(self.تسمية_الرصيد_النهائي)
        تخطيط_ملخص.addWidget(self.تسمية_عدد_العمليات)
        تخطيط_ملخص.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_ملخص)

    def تحميل_العملاء(self):
        """
        دالة تحميل قائمة العملاء
        """
        try:
            استعلام = """
            SELECT رقم_العميل, CONCAT(الاسم_الأول, ' ', اللقب) as الاسم_الكامل
            FROM العملاء
            WHERE حالة_النشاط = TRUE
            ORDER BY الاسم_الأول, اللقب
            """
            العملاء = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_العملاء.clear()
            self.قائمة_العملاء.addItem("اختر عميل...")

            if العملاء:
                for عميل in العملاء:
                    self.قائمة_العملاء.addItem(f"{عميل[1]} ({عميل[0]})", عميل[0])

        except Exception as e:
            print(f"خطأ في تحميل العملاء: {str(e)}")

    def عند_اختيار_عميل(self):
        """
        دالة تنفذ عند اختيار عميل
        """
        فهرس_محدد = self.قائمة_العملاء.currentIndex()

        if فهرس_محدد > 0:  # تجاهل "اختر عميل..."
            self.العميل_المحدد = self.قائمة_العملاء.currentData()
            self.تحميل_معلومات_العميل()
            self.تحميل_كشف_الحساب()
        else:
            self.العميل_المحدد = None
            self.مسح_البيانات()

    def تحميل_معلومات_العميل(self):
        """
        دالة تحميل معلومات العميل المحدد
        """
        if not self.العميل_المحدد:
            return

        try:
            # معلومات العميل الأساسية
            استعلام_عميل = """
            SELECT CONCAT(الاسم_الأول, ' ', اللقب) as الاسم_الكامل,
                   رقم_الهاتف, الرصيد_الحالي
            FROM العملاء
            WHERE رقم_العميل = %s
            """
            معلومات_عميل = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_عميل, (self.العميل_المحدد,))

            # عدد العقود
            استعلام_عقود = """
            SELECT COUNT(*) as عدد_العقود
            FROM العقود
            WHERE رقم_العميل = %s
            """
            عدد_عقود = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_عقود, (self.العميل_المحدد,))

            if معلومات_عميل:
                عميل = معلومات_عميل[0]
                self.تسمية_اسم_العميل.setText(f"اسم العميل: {عميل[0]}")
                self.تسمية_رقم_هاتف.setText(f"رقم الهاتف: {عميل[1] or 'غير محدد'}")
                self.تسمية_الرصيد_الحالي.setText(f"الرصيد الحالي: {عميل[2]:,.2f}")

            if عدد_عقود:
                self.تسمية_عدد_العقود.setText(f"عدد العقود: {عدد_عقود[0][0]}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل معلومات العميل: {str(e)}")

    def تحميل_كشف_الحساب(self):
        """
        دالة تحميل كشف حساب العميل
        """
        if not self.العميل_المحدد:
            return

        try:
            # تحديد الفترة
            فترة_محددة = self.قائمة_فترة.currentText()
            شرط_التاريخ = ""

            if فترة_محددة == "آخر 3 شهور":
                شرط_التاريخ = "AND DATE(تاريخ_الإنشاء) >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)"
            elif فترة_محددة == "آخر 6 شهور":
                شرط_التاريخ = "AND DATE(تاريخ_الإنشاء) >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)"
            elif فترة_محددة == "آخر سنة":
                شرط_التاريخ = "AND DATE(تاريخ_الإنشاء) >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)"

            # استعلام كشف الحساب (مبسط للعرض)
            استعلام = f"""
            SELECT
                DATE_FORMAT(ق.تاريخ_الاستحقاق, '%Y-%m-%d') as التاريخ,
                'قسط' as نوع_العملية,
                ق.رقم_القسط as رقم_المرجع,
                CONCAT('قسط رقم ', ق.رقم_القسط_في_العقد, ' من العقد ', ق.رقم_العقد) as الوصف,
                ق.مبلغ_القسط as المبلغ_المدين,
                ق.المبلغ_المدفوع as المبلغ_الدائن,
                0 as الرصيد,
                ق.ملاحظات
            FROM الأقساط ق
            JOIN العقود ع ON ق.رقم_العقد = ع.رقم_العقد
            WHERE ع.رقم_العميل = %s {شرط_التاريخ}
            ORDER BY ق.تاريخ_الاستحقاق DESC
            """

            العمليات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.العميل_المحدد,))

            self.جدول_كشف_الحساب.setRowCount(0)

            if العمليات:
                self.جدول_كشف_الحساب.setRowCount(len(العمليات))

                رصيد_جاري = 0
                إجمالي_مدين = 0
                إجمالي_دائن = 0

                for صف, عملية in enumerate(العمليات):
                    مدين = float(عملية[4]) if عملية[4] else 0
                    دائن = float(عملية[5]) if عملية[5] else 0

                    رصيد_جاري += مدين - دائن
                    إجمالي_مدين += مدين
                    إجمالي_دائن += دائن

                    # إنشاء عملية معدلة مع الرصيد
                    عملية_معدلة = list(عملية)
                    عملية_معدلة[6] = f"{رصيد_جاري:,.2f}"

                    for عمود, قيمة in enumerate(عملية_معدلة):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة is not None else "")

                        # تلوين المبالغ
                        if عمود == 4 and مدين > 0:  # مدين
                            عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 5 and دائن > 0:  # دائن
                            عنصر.setForeground(QColor("#27ae60"))
                        elif عمود == 6:  # الرصيد
                            if رصيد_جاري > 0:
                                عنصر.setForeground(QColor("#e74c3c"))
                            else:
                                عنصر.setForeground(QColor("#27ae60"))

                        self.جدول_كشف_الحساب.setItem(صف, عمود, عنصر)

                # تحديث الملخص
                self.تحديث_ملخص_الحساب(إجمالي_مدين, إجمالي_دائن, رصيد_جاري, len(العمليات))
            else:
                self.تحديث_ملخص_الحساب(0, 0, 0, 0)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل كشف الحساب: {str(e)}")

    def تحديث_ملخص_الحساب(self, إجمالي_مدين, إجمالي_دائن, رصيد_نهائي, عدد_عمليات):
        """
        دالة تحديث ملخص الحساب
        """
        self.تسمية_إجمالي_المدين.setText(f"إجمالي المدين: {إجمالي_مدين:,.2f}")
        self.تسمية_إجمالي_الدائن.setText(f"إجمالي الدائن: {إجمالي_دائن:,.2f}")
        self.تسمية_الرصيد_النهائي.setText(f"الرصيد النهائي: {رصيد_نهائي:,.2f}")
        self.تسمية_عدد_العمليات.setText(f"عدد العمليات: {عدد_عمليات}")

        # تلوين الرصيد النهائي
        if رصيد_نهائي > 0:
            self.تسمية_الرصيد_النهائي.setStyleSheet("font-weight: bold; font-size: 14px; color: #e74c3c; padding: 5px;")
        else:
            self.تسمية_الرصيد_النهائي.setStyleSheet("font-weight: bold; font-size: 14px; color: #27ae60; padding: 5px;")

    def مسح_البيانات(self):
        """
        دالة مسح جميع البيانات
        """
        self.تسمية_اسم_العميل.setText("اسم العميل: غير محدد")
        self.تسمية_رقم_هاتف.setText("رقم الهاتف: غير محدد")
        self.تسمية_عدد_العقود.setText("عدد العقود: 0")
        self.تسمية_الرصيد_الحالي.setText("الرصيد الحالي: 0.00")

        self.جدول_كشف_الحساب.setRowCount(0)
        self.تحديث_ملخص_الحساب(0, 0, 0, 0)

    def طباعة_كشف_الحساب(self):
        """
        دالة طباعة كشف الحساب
        """
        if not self.العميل_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل أولاً")
            return

        QMessageBox.information(self, "معلومات", "طباعة كشف الحساب - قيد التطوير")

    def تصدير_كشف_الحساب(self):
        """
        دالة تصدير كشف الحساب إلى PDF
        """
        if not self.العميل_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل أولاً")
            return

        QMessageBox.information(self, "معلومات", "تصدير كشف الحساب - قيد التطوير")


class واجهة_التقارير_المالية(QWidget):
    """
    واجهة التقارير المالية
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة التقارير المالية
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.إعداد_الواجهة()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة التقارير المالية
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)
        
        # عنوان الصفحة
        عنوان = QLabel("التقارير المالية")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #27ae60;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # محتوى مؤقت
        محتوى = QLabel("واجهة التقارير المالية - قيد التطوير")
        محتوى.setAlignment(Qt.AlignCenter)
        محتوى.setStyleSheet("font-size: 18px; color: #7f8c8d; margin: 50px;")
        التخطيط_الرئيسي.addWidget(محتوى)
        
        # معلومات إضافية
        معلومات = QLabel("""
        ستتضمن هذه الواجهة:
        • تقارير المبيعات اليومية والشهرية
        • تقارير الأرباح والخسائر
        • تقارير المخزون والحركة
        • تقارير العملاء والموردين
        • التحليلات المالية المتقدمة
        """)
        معلومات.setStyleSheet("font-size: 14px; color: #34495e; padding: 20px;")
        التخطيط_الرئيسي.addWidget(معلومات)

class واجهة_الإعدادات(QWidget):
    """
    واجهة الإعدادات العامة
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة الإعدادات
        """
        super().__init__()
        self.إعداد_الواجهة()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة الإعدادات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)
        
        # عنوان الصفحة
        عنوان = QLabel("الإعدادات العامة")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #95a5a6;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # محتوى مؤقت
        محتوى = QLabel("واجهة الإعدادات العامة - قيد التطوير")
        محتوى.setAlignment(Qt.AlignCenter)
        محتوى.setStyleSheet("font-size: 18px; color: #7f8c8d; margin: 50px;")
        التخطيط_الرئيسي.addWidget(محتوى)
        
        # معلومات إضافية
        معلومات = QLabel("""
        ستتضمن هذه الواجهة:
        • إعدادات قاعدة البيانات
        • إعدادات الطباعة والتقارير
        • إدارة المستخدمين والصلاحيات
        • إعدادات النسخ الاحتياطي
        • تخصيص واجهة المستخدم
        """)
        معلومات.setStyleSheet("font-size: 14px; color: #34495e; padding: 20px;")
        التخطيط_الرئيسي.addWidget(معلومات)

class واجهة_الموردين(QWidget):
    """واجهة إدارة الموردين الرئيسية"""

    def __init__(self):
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        self.المورد_المحدد = None

        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()
        self.تحميل_الموردين()

        # تعطيل أزرار التعديل والحذف في البداية
        self.أزرار_الإجراءات["edit_supplier"].setEnabled(False)
        self.أزرار_الإجراءات["delete_supplier"].setEnabled(False)
        self.أزرار_الإجراءات["view_details"].setEnabled(False)
        self.أزرار_الإجراءات["supplier_report"].setEnabled(False)

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة إدارة الموردين
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة الموردين")
        عنوان.setObjectName("page_title")
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الفلاتر
        self.إنشاء_شريط_الفلاتر(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # منطقة البيانات
        self.إنشاء_منطقة_البيانات(التخطيط_الرئيسي)

    def إنشاء_شريط_الفلاتر(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الفلاتر والبحث
        """
        إطار_الفلاتر = QFrame()
        إطار_الفلاتر.setObjectName("filters_bar")
        إطار_الفلاتر.setFixedHeight(80)

        تخطيط_الفلاتر = QHBoxLayout(إطار_الفلاتر)
        تخطيط_الفلاتر.setContentsMargins(20, 15, 20, 15)
        تخطيط_الفلاتر.setSpacing(20)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        تسمية_البحث.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setObjectName("search_field")
        self.حقل_البحث.setPlaceholderText("اسم المورد، رقم الهاتف، أو العنوان...")
        self.حقل_البحث.setFixedHeight(40)
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الموردين)

        # زر البحث
        زر_البحث = QPushButton("🔍")
        زر_البحث.setObjectName("search_button")
        زر_البحث.setFixedSize(40, 40)
        زر_البحث.clicked.connect(self.البحث_عن_الموردين)

        # فلتر الحالة
        تسمية_حالة = QLabel("الحالة:")
        تسمية_حالة.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "نشط", "غير نشط"])
        self.قائمة_حالة.setFixedHeight(40)
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_الموردين)

        # زر الفلتر المتقدم
        زر_فلتر = QPushButton("فلتر متقدم")
        زر_فلتر.setObjectName("filter_button")
        زر_فلتر.setFixedHeight(40)
        زر_فلتر.clicked.connect(self.فلتر_متقدم)

        تخطيط_الفلاتر.addWidget(تسمية_البحث)
        تخطيط_الفلاتر.addWidget(self.حقل_البحث, 3)
        تخطيط_الفلاتر.addWidget(زر_البحث)
        تخطيط_الفلاتر.addWidget(تسمية_حالة)
        تخطيط_الفلاتر.addWidget(self.قائمة_حالة)
        تخطيط_الفلاتر.addWidget(زر_فلتر)
        تخطيط_الفلاتر.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الفلاتر)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط أزرار الإجراءات
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("actions_bar")
        إطار_الأزرار.setFixedHeight(100)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(20, 15, 20, 15)
        تخطيط_الأزرار.setSpacing(15)

        # أزرار الإجراءات
        أزرار = [
            ("➕", "إضافة مورد", "add_supplier", "#27ae60", self.إضافة_مورد),
            ("✏️", "تعديل", "edit_supplier", "#3498db", self.تعديل_مورد),
            ("🗑️", "حذف", "delete_supplier", "#e74c3c", self.حذف_مورد),
            ("👁️", "عرض التفاصيل", "view_details", "#9b59b6", self.عرض_تفاصيل_مورد),
            ("📊", "تقرير المورد", "supplier_report", "#f39c12", self.تقرير_مورد),
            ("🖨️", "طباعة", "print_suppliers", "#7f8c8d", self.طباعة_الموردين),
            ("📤", "تصدير", "export_suppliers", "#34495e", self.تصدير_الموردين)
        ]

        self.أزرار_الإجراءات = {}

        for أيقونة, نص, مفتاح, لون, وظيفة in أزرار:
            زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
            زر.clicked.connect(وظيفة)
            self.أزرار_الإجراءات[مفتاح] = زر
            تخطيط_الأزرار.addWidget(زر)

        تخطيط_الأزرار.addStretch()
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(100, 70)
        زر.setObjectName("action_button")

        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)

        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 24px;")

        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 10px; font-weight: bold;")
        تسمية_نص.setWordWrap(True)

        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)

        return زر

    def إنشاء_منطقة_البيانات(self, التخطيط_الرئيسي):
        """
        دالة إنشاء منطقة عرض البيانات
        """
        منطقة_البيانات = QStackedWidget()
        منطقة_البيانات.setObjectName("data_area")

        # جدول الموردين
        self.جدول_الموردين = QTableWidget()
        self.جدول_الموردين.setObjectName("suppliers_table")

        # إعداد الأعمدة
        الأعمدة = [
            "رقم المورد", "اسم المورد", "العنوان", "رقم الهاتف",
            "البريد الإلكتروني", "الرصيد الحالي", "الحالة", "تاريخ الإضافة"
        ]

        self.جدول_الموردين.setColumnCount(len(الأعمدة))
        self.جدول_الموردين.setHorizontalHeaderLabels(الأعمدة)

        # إعداد خصائص الجدول
        self.جدول_الموردين.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الموردين.setAlternatingRowColors(True)
        self.جدول_الموردين.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.جدول_الموردين.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المورد
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # العنوان

        # ربط النقر بتحديد المورد
        self.جدول_الموردين.itemSelectionChanged.connect(self.تحديد_مورد)

        # قائمة سياق
        self.جدول_الموردين.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_الموردين.customContextMenuRequested.connect(self.عرض_قائمة_سياق)

        منطقة_البيانات.addWidget(self.جدول_الموردين)
        التخطيط_الرئيسي.addWidget(منطقة_البيانات)

    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط على الواجهة
        """
        self.setStyleSheet("""
            QLabel#page_title {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #ecf0f1,
                                           stop:1 #ffffff);
                border-radius: 10px;
                border-left: 5px solid #e67e22;
            }

            QFrame#filters_bar, QFrame#actions_bar {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
            }

            QStackedWidget#data_area {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
            }

            QTableWidget#suppliers_table {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: none;
                border-radius: 8px;
                font-size: 12px;
            }

            QTableWidget#suppliers_table::item {
                padding: 10px;
                border-bottom: 1px solid #dee2e6;
            }

            QTableWidget#suppliers_table::item:selected {
                background-color: #3498db;
                color: white;
            }

            QTableWidget#suppliers_table::item:hover {
                background-color: #e3f2fd;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #e67e22,
                                           stop:1 #d35400);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }

            QHeaderView::section:hover {
                background-color: #f39c12;
            }
        """)

    def تحميل_الموردين(self):
        """
        دالة تحميل جميع الموردين
        """
        try:
            استعلام = """
            SELECT رقم_المورد, اسم_المورد, العنوان, رقم_الهاتف,
                   البريد_الإلكتروني, الرصيد_الحالي, حالة_النشاط,
                   DATE_FORMAT(تاريخ_الإضافة, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الموردين
            ORDER BY تاريخ_الإضافة DESC
            """

            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if الموردين:
                self.جدول_الموردين.setRowCount(len(الموردين))

                for صف, مورد in enumerate(الموردين):
                    for عمود, قيمة in enumerate(مورد):
                        if عمود == 6:  # عمود الحالة
                            حالة = "نشط" if قيمة == "نشط" else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 5:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{رصيد:.2f}")
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))  # أحمر للدين
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))  # أخضر للرصيد الموجب
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الموردين.setItem(صف, عمود, عنصر)
            else:
                self.جدول_الموردين.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين: {str(e)}")

    def البحث_عن_الموردين(self):
        """
        دالة البحث عن الموردين
        """
        نص_البحث = self.حقل_البحث.text().strip()
        حالة_الفلتر = self.قائمة_حالة.currentText()

        try:
            شرط_البحث = "1=1"
            معاملات = []

            # شرط البحث النصي
            if نص_البحث:
                شرط_البحث += """ AND (
                    اسم_المورد LIKE %s OR العنوان LIKE %s OR
                    رقم_الهاتف LIKE %s OR البريد_الإلكتروني LIKE %s
                )"""
                معاملات.extend([f"%{نص_البحث}%"] * 4)

            # شرط فلتر الحالة
            if حالة_الفلتر != "الكل":
                شرط_البحث += " AND حالة_النشاط = %s"
                معاملات.append(حالة_الفلتر)

            استعلام = f"""
            SELECT رقم_المورد, اسم_المورد, العنوان, رقم_الهاتف,
                   البريد_الإلكتروني, الرصيد_الحالي, حالة_النشاط,
                   DATE_FORMAT(تاريخ_الإضافة, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الموردين
            WHERE {شرط_البحث}
            ORDER BY تاريخ_الإضافة DESC
            """

            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_الموردين.setRowCount(0)

            if الموردين:
                self.جدول_الموردين.setRowCount(len(الموردين))

                for صف, مورد in enumerate(الموردين):
                    for عمود, قيمة in enumerate(مورد):
                        if عمود == 6:  # عمود الحالة
                            حالة = "نشط" if قيمة == "نشط" else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 5:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{رصيد:.2f}")
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الموردين.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_مورد(self):
        """
        دالة تحديد المورد المختار
        """
        صف_محدد = self.جدول_الموردين.currentRow()

        if صف_محدد >= 0:
            رقم_المورد = self.جدول_الموردين.item(صف_محدد, 0).text()
            self.المورد_المحدد = int(رقم_المورد)

            # تفعيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_supplier"].setEnabled(True)
            self.أزرار_الإجراءات["delete_supplier"].setEnabled(True)
            self.أزرار_الإجراءات["view_details"].setEnabled(True)
            self.أزرار_الإجراءات["supplier_report"].setEnabled(True)
        else:
            self.المورد_المحدد = None

            # تعطيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_supplier"].setEnabled(False)
            self.أزرار_الإجراءات["delete_supplier"].setEnabled(False)
            self.أزرار_الإجراءات["view_details"].setEnabled(False)
            self.أزرار_الإجراءات["supplier_report"].setEnabled(False)

    def عرض_قائمة_سياق(self, موضع):
        """
        دالة عرض قائمة السياق
        """
        if self.جدول_الموردين.itemAt(موضع):
            قائمة = QMenu(self)

            إجراء_تعديل = قائمة.addAction("تعديل المورد")
            إجراء_تعديل.triggered.connect(self.تعديل_مورد)

            إجراء_حذف = قائمة.addAction("حذف المورد")
            إجراء_حذف.triggered.connect(self.حذف_مورد)

            قائمة.addSeparator()

            إجراء_تفاصيل = قائمة.addAction("عرض التفاصيل")
            إجراء_تفاصيل.triggered.connect(self.عرض_تفاصيل_مورد)

            إجراء_تقرير = قائمة.addAction("تقرير المورد")
            إجراء_تقرير.triggered.connect(self.تقرير_مورد)

            قائمة.exec_(self.جدول_الموردين.mapToGlobal(موضع))

    # دوال الإجراءات
    def إضافة_مورد(self):
        """دالة إضافة مورد جديد"""
        QMessageBox.information(self, "معلومات", "وظيفة إضافة المورد قيد التطوير")

    def تعديل_مورد(self):
        """دالة تعديل المورد المحدد"""
        if not self.المورد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return
        QMessageBox.information(self, "معلومات", "وظيفة تعديل المورد قيد التطوير")

    def حذف_مورد(self):
        """دالة حذف المورد المحدد"""
        if not self.المورد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return

        # الحصول على اسم المورد
        صف_محدد = self.جدول_الموردين.currentRow()
        اسم_مورد = self.جدول_الموردين.item(صف_محدد, 1).text()

        رد = QMessageBox.question(self, "تأكيد الحذف",
                                  f"هل تريد حذف المورد '{اسم_مورد}'؟\n"
                                  "تحذير: سيتم حذف جميع البيانات المرتبطة بهذا المورد",
                                  QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM الموردين WHERE رقم_المورد = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.المورد_المحدد,))

                QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح")
                self.تحميل_الموردين()
                self.المورد_المحدد = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المورد: {str(e)}")

    def عرض_تفاصيل_مورد(self):
        """دالة عرض تفاصيل المورد"""
        QMessageBox.information(self, "معلومات", "وظيفة تفاصيل المورد قيد التطوير")

    def تقرير_مورد(self):
        """دالة إنشاء تقرير المورد"""
        QMessageBox.information(self, "معلومات", "وظيفة تقرير المورد قيد التطوير")

    def طباعة_الموردين(self):
        """دالة طباعة قائمة الموردين"""
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")

    def تصدير_الموردين(self):
        """دالة تصدير قائمة الموردين"""
        QMessageBox.information(self, "معلومات", "وظيفة التصدير قيد التطوير")

    def فلتر_متقدم(self):
        """دالة الفلتر المتقدم"""
        QMessageBox.information(self, "معلومات", "وظيفة الفلتر المتقدم قيد التطوير")
