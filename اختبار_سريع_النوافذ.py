# -*- coding: utf-8 -*-
"""
اختبار سريع للنوافذ الفرعية الجديدة
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import Qt
from db import تهيئة_قاعدة_البيانات
from other_interfaces import نافذة_موظف, نافذة_مصروف, نافذة_بنك

class نافذة_اختبار_سريع(QMainWindow):
    """
    نافذة اختبار سريع للنوافذ الفرعية
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار النوافذ الفرعية")
        self.setFixedSize(400, 300)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء قاعدة البيانات الوهمية
        from db import قاعدة_البيانات
        self.قاعدة_البيانات = قاعدة_البيانات()
        
        # إنشاء الواجهة
        widget_مركزي = QWidget()
        self.setCentralWidget(widget_مركزي)
        
        تخطيط = QVBoxLayout(widget_مركزي)
        تخطيط.setSpacing(20)
        تخطيط.setContentsMargins(30, 30, 30, 30)
        
        # أزرار الاختبار
        زر_موظف = QPushButton("اختبار نافذة الموظف")
        زر_موظف.clicked.connect(self.اختبار_نافذة_موظف)
        زر_موظف.setStyleSheet(self.نمط_الزر("#9b59b6"))
        
        زر_مصروف = QPushButton("اختبار نافذة المصروف")
        زر_مصروف.clicked.connect(self.اختبار_نافذة_مصروف)
        زر_مصروف.setStyleSheet(self.نمط_الزر("#f39c12"))
        
        زر_بنك_رئيسي = QPushButton("اختبار نافذة البنك الرئيسي")
        زر_بنك_رئيسي.clicked.connect(self.اختبار_نافذة_بنك_رئيسي)
        زر_بنك_رئيسي.setStyleSheet(self.نمط_الزر("#3498db"))
        
        زر_فرع = QPushButton("اختبار نافذة الفرع")
        زر_فرع.clicked.connect(self.اختبار_نافذة_فرع)
        زر_فرع.setStyleSheet(self.نمط_الزر("#27ae60"))
        
        تخطيط.addWidget(زر_موظف)
        تخطيط.addWidget(زر_مصروف)
        تخطيط.addWidget(زر_بنك_رئيسي)
        تخطيط.addWidget(زر_فرع)
        تخطيط.addStretch()
    
    def نمط_الزر(self, لون):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون};
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {لون}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون}aa;
            }}
        """
    
    def اختبار_نافذة_موظف(self):
        """
        اختبار نافذة الموظف
        """
        نافذة = نافذة_موظف(self, "إضافة")
        نافذة.exec_()
    
    def اختبار_نافذة_مصروف(self):
        """
        اختبار نافذة المصروف
        """
        نافذة = نافذة_مصروف(self, "إضافة")
        نافذة.exec_()
    
    def اختبار_نافذة_بنك_رئيسي(self):
        """
        اختبار نافذة البنك الرئيسي
        """
        نافذة = نافذة_بنك(self, "إضافة بنك رئيسي")
        نافذة.exec_()
    
    def اختبار_نافذة_فرع(self):
        """
        اختبار نافذة الفرع
        """
        نافذة = نافذة_بنك(self, "إضافة فرع")
        نافذة.exec_()

def main():
    """
    دالة تشغيل الاختبار
    """
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تهيئة قاعدة البيانات
    if not تهيئة_قاعدة_البيانات():
        print("فشل في تهيئة قاعدة البيانات")
        sys.exit(1)
    
    # إنشاء النافذة
    نافذة = نافذة_اختبار_سريع()
    نافذة.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
