#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إصلاح قاعدة البيانات
يضيف الأعمدة المفقودة ويصحح هيكل الجداول
"""

import mysql.connector
from mysql.connector import Error

def إصلاح_قاعدة_البيانات():
    """
    دالة إصلاح قاعدة البيانات وإضافة الأعمدة المفقودة
    """
    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password="kh123456",
            database="Sales_system",
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        
        cursor = connection.cursor()
        
        print("🔧 بدء إصلاح قاعدة البيانات...")
        
        # إصلاح جدول الموردين - إضافة عمود الملاحظات
        try:
            cursor.execute("""
                ALTER TABLE الموردين 
                ADD COLUMN ملاحظات TEXT AFTER الرصيد_الحالي
            """)
            print("✓ تم إضافة عمود الملاحظات لجدول الموردين")
        except Error as e:
            if "Duplicate column name" in str(e):
                print("- عمود الملاحظات موجود مسبقاً في جدول الموردين")
            else:
                print(f"❌ خطأ في إضافة عمود الملاحظات: {e}")
        
        # إصلاح جدول الفئات - تغيير اسم العمود
        try:
            cursor.execute("DESCRIBE الفئات")
            columns = [column[0] for column in cursor.fetchall()]
            
            if 'وصف_الفئة' in columns and 'الوصف' not in columns:
                cursor.execute("""
                    ALTER TABLE الفئات 
                    CHANGE COLUMN وصف_الفئة الوصف TEXT
                """)
                print("✓ تم تغيير اسم عمود الوصف في جدول الفئات")
            elif 'الوصف' in columns:
                print("- عمود الوصف موجود مسبقاً في جدول الفئات")
            else:
                cursor.execute("""
                    ALTER TABLE الفئات 
                    ADD COLUMN الوصف TEXT AFTER اسم_الفئة
                """)
                print("✓ تم إضافة عمود الوصف لجدول الفئات")
        except Error as e:
            print(f"❌ خطأ في إصلاح جدول الفئات: {e}")
        
        # إصلاح جدول الفئات - تغيير اسم عمود التاريخ
        try:
            cursor.execute("DESCRIBE الفئات")
            columns = [column[0] for column in cursor.fetchall()]
            
            if 'تاريخ_الإنشاء' in columns and 'تاريخ_الإضافة' not in columns:
                cursor.execute("""
                    ALTER TABLE الفئات 
                    CHANGE COLUMN تاريخ_الإنشاء تاريخ_الإضافة TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                """)
                print("✓ تم تغيير اسم عمود التاريخ في جدول الفئات")
            elif 'تاريخ_الإضافة' in columns:
                print("- عمود تاريخ_الإضافة موجود مسبقاً في جدول الفئات")
        except Error as e:
            print(f"❌ خطأ في تغيير عمود التاريخ: {e}")
        
        # التحقق من جدول فواتير الشراء
        try:
            cursor.execute("SHOW TABLES LIKE 'فواتير_الشراء'")
            if not cursor.fetchone():
                print("⚠️ جدول فواتير_الشراء غير موجود، سيتم إنشاؤه...")
                cursor.execute("""
                    CREATE TABLE فواتير_الشراء (
                        رقم_الفاتورة INT AUTO_INCREMENT PRIMARY KEY,
                        رقم_المورد INT,
                        تاريخ_الفاتورة DATE NOT NULL,
                        إجمالي_الفاتورة DECIMAL(15,2) NOT NULL DEFAULT 0.00,
                        المبلغ_المدفوع DECIMAL(15,2) DEFAULT 0.00,
                        حالة_الدفع ENUM('غير مدفوع', 'مدفوع جزئياً', 'مدفوع') DEFAULT 'غير مدفوع',
                        ملاحظات TEXT,
                        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (رقم_المورد) REFERENCES الموردين(رقم_المورد) ON DELETE SET NULL
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)
                print("✓ تم إنشاء جدول فواتير_الشراء")
            else:
                print("- جدول فواتير_الشراء موجود مسبقاً")
        except Error as e:
            print(f"❌ خطأ في التحقق من جدول فواتير_الشراء: {e}")
        
        # التحقق من جدول تفاصيل فواتير الشراء
        try:
            cursor.execute("SHOW TABLES LIKE 'تفاصيل_فواتير_الشراء'")
            if not cursor.fetchone():
                print("⚠️ جدول تفاصيل_فواتير_الشراء غير موجود، سيتم إنشاؤه...")
                cursor.execute("""
                    CREATE TABLE تفاصيل_فواتير_الشراء (
                        رقم_التفصيل INT AUTO_INCREMENT PRIMARY KEY,
                        رقم_الفاتورة INT NOT NULL,
                        الباركود VARCHAR(50) NOT NULL,
                        الكمية INT NOT NULL,
                        سعر_الوحدة DECIMAL(10,2) NOT NULL,
                        الإجمالي DECIMAL(15,2) NOT NULL,
                        FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_الشراء(رقم_الفاتورة) ON DELETE CASCADE,
                        FOREIGN KEY (الباركود) REFERENCES المنتجات(الباركود) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)
                print("✓ تم إنشاء جدول تفاصيل_فواتير_الشراء")
            else:
                print("- جدول تفاصيل_فواتير_الشراء موجود مسبقاً")
        except Error as e:
            print(f"❌ خطأ في التحقق من جدول تفاصيل_فواتير_الشراء: {e}")
        
        # حفظ التغييرات
        connection.commit()
        print("\n✅ تم إصلاح قاعدة البيانات بنجاح!")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """
    دالة التشغيل الرئيسية
    """
    print("=== إصلاح قاعدة البيانات ===")
    print("سيتم إصلاح هيكل قاعدة البيانات وإضافة الأعمدة المفقودة")
    print()
    
    if إصلاح_قاعدة_البيانات():
        print("\n🎉 تم الإصلاح بنجاح!")
        print("يمكنك الآن تشغيل التطبيق بدون مشاكل")
    else:
        print("\n❌ فشل في الإصلاح")
        print("تحقق من إعدادات قاعدة البيانات")

if __name__ == "__main__":
    main()
