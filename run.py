#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل التطبيق مع معالجة الأخطاء
"""

import sys
import traceback
from PySide6.QtWidgets import QApplication, QMessageBox

def main():
    """
    دالة تشغيل التطبيق الرئيسية مع معالجة الأخطاء
    """
    app = QApplication(sys.argv)

    try:
        # تهيئة قاعدة البيانات أولاً
        print("جاري تهيئة قاعدة البيانات...")
        from db import تهيئة_قاعدة_البيانات

        if not تهيئة_قاعدة_البيانات():
            QMessageBox.critical(None, "خطأ",
                               "فشل في تهيئة قاعدة البيانات.\n"
                               "تأكد من:\n"
                               "1. تشغيل MySQL Server\n"
                               "2. صحة بيانات الاتصال\n"
                               "3. وجود صلاحيات إنشاء قاعدة البيانات")
            return 1

        print("تم تهيئة قاعدة البيانات بنجاح")

        # إصلاح قاعدة البيانات
        print("جاري إصلاح قاعدة البيانات...")
        try:
            from fix_database import إصلاح_قاعدة_البيانات
            if إصلاح_قاعدة_البيانات():
                print("✓ تم إصلاح قاعدة البيانات بنجاح")
            else:
                print("⚠️ تحذير: فشل في إصلاح قاعدة البيانات، لكن سيتم المتابعة")
        except Exception as e:
            print(f"⚠️ تحذير: خطأ في إصلاح قاعدة البيانات: {e}")
            print("سيتم المتابعة بدون إصلاح")

        # اختبار الواجهات قبل التشغيل
        print("جاري اختبار الواجهات...")
        try:
            from main import النافذة_الرئيسية
            from pos_interface import واجهة_نقطة_البيع
            from customers_interface import واجهة_إدارة_العملاء
            from inventory_interface import واجهة_إدارة_المخزون
            from other_interfaces import واجهة_الموردين
            print("✓ جميع الواجهات جاهزة")
        except Exception as e:
            print(f"❌ خطأ في الواجهات: {e}")
            QMessageBox.critical(None, "خطأ في الواجهات",
                               f"فشل في تحميل إحدى الواجهات:\n{str(e)}\n\n"
                               "يرجى التحقق من سلامة ملفات التطبيق")
            return 1

        # تشغيل التطبيق
        print("جاري تشغيل التطبيق...")
        window = النافذة_الرئيسية()
        window.show()

        print("✅ تم تشغيل التطبيق بنجاح!")
        print("📝 يمكنك الآن استخدام جميع ميزات التطبيق")
        return app.exec()

    except ImportError as e:
        error_msg = f"خطأ في استيراد المكتبات المطلوبة:\n{str(e)}\n\nتأكد من تثبيت المتطلبات:\npip install -r requirements.txt"
        print(error_msg)
        QMessageBox.critical(None, "خطأ في الاستيراد", error_msg)
        return 1

    except Exception as e:
        error_msg = f"خطأ غير متوقع:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"
        print(error_msg)
        QMessageBox.critical(None, "خطأ", error_msg)
        return 1

if __name__ == "__main__":
    sys.exit(main())
