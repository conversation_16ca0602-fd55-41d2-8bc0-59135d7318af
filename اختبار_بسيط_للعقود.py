# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام العقود
"""

import sys
from db import قاعدة_البيانات, تهيئة_قاعدة_البيانات

def اختبار_بسيط():
    """
    اختبار بسيط للتأكد من عمل النظام
    """
    print("=== اختبار نظام العقود ===")
    
    try:
        # تهيئة قاعدة البيانات
        print("1. تهيئة قاعدة البيانات...")
        تهيئة_قاعدة_البيانات()
        print("تم تهيئة قاعدة البيانات بنجاح")
        
        # إنشاء اتصال
        print("2. إنشاء اتصال...")
        قاعدة = قاعدة_البيانات()
        قاعدة.اتصال_قاعدة_البيانات()
        قاعدة.cursor.execute(f"USE {قاعدة.database}")
        print("تم الاتصال بنجاح")
        
        # اختبار إنشاء رقم عقد
        print("3. اختبار إنشاء رقم عقد...")
        رقم_عقد = قاعدة.إنشاء_رقم_عقد_تلقائي()
        print(f"رقم العقد المقترح: {رقم_عقد}")
        
        # اختبار التحقق من الرقم
        print("4. اختبار التحقق من الرقم...")
        متاح = قاعدة.التحقق_من_رقم_العقد(رقم_عقد)
        print(f"الرقم متاح: {متاح}")
        
        # اختبار المعاملات
        print("5. اختبار المعاملات...")
        قاعدة.connection.start_transaction()
        قاعدة.تنفيذ_استعلام_بدون_commit("SELECT 1")
        قاعدة.connection.commit()
        print("المعاملات تعمل بنجاح")
        
        # إغلاق الاتصال
        قاعدة.إغلاق_الاتصال()
        print("تم إغلاق الاتصال")
        
        print("\nجميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"خطأ: {str(e)}")
        return False

def اختبار_حوار():
    """
    اختبار حوار إنشاء العقد
    """
    print("\n=== اختبار حوار إنشاء العقد ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # استيراد الحوار
        from contract_creation_dialog import حوار_إنشاء_عقد
        
        print("إنشاء حوار إنشاء العقد...")
        حوار = حوار_إنشاء_عقد()
        print("تم إنشاء الحوار بنجاح")
        
        print("عرض الحوار...")
        حوار.show()
        print("الحوار جاهز للاستخدام!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"خطأ في الحوار: {str(e)}")
        return False

def main():
    """
    الدالة الرئيسية
    """
    print("اختبار نظام العقود الشامل")
    print("=" * 40)
    
    # اختبار الاتصال
    if اختبار_بسيط():
        print("\n" + "=" * 40)
        
        # سؤال عن اختبار الحوار
        try:
            جواب = input("\nاختبار حوار إنشاء العقد؟ (y/n): ")
            if جواب.lower() in ['y', 'yes']:
                اختبار_حوار()
            else:
                print("انتهى الاختبار بنجاح!")
        except:
            print("انتهى الاختبار")
    else:
        print("فشل في الاختبار!")

if __name__ == "__main__":
    main()
