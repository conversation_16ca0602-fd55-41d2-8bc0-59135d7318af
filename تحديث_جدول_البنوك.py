# -*- coding: utf-8 -*-
"""
سكريبت تحديث جدول البنوك لإضافة حقلي رقم الهاتف والعنوان
"""

import mysql.connector
from mysql.connector import Error
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def تحديث_جدول_البنوك():
    """
    تحديث جدول البنوك لإضافة الحقول الجديدة
    """
    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='kh123456',
            database='Sales_system',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        
        cursor = connection.cursor()
        
        print("بدء تحديث جدول البنوك...")

        # التحقق من وجود عمود رقم_الهاتف
        cursor.execute("""
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'Sales_system'
            AND TABLE_NAME = 'البنوك'
            AND COLUMN_NAME = 'رقم_الهاتف'
        """)

        if cursor.fetchone()[0] == 0:
            # إضافة عمود رقم الهاتف
            cursor.execute("""
                ALTER TABLE البنوك
                ADD COLUMN رقم_الهاتف VARCHAR(20) AFTER رقم_حساب_الشركة
            """)
            print("تم إضافة عمود رقم_الهاتف")
        else:
            print("عمود رقم_الهاتف موجود بالفعل")

        # التحقق من وجود عمود العنوان
        cursor.execute("""
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'Sales_system'
            AND TABLE_NAME = 'البنوك'
            AND COLUMN_NAME = 'العنوان'
        """)

        if cursor.fetchone()[0] == 0:
            # إضافة عمود العنوان
            cursor.execute("""
                ALTER TABLE البنوك
                ADD COLUMN العنوان TEXT AFTER رقم_الهاتف
            """)
            print("تم إضافة عمود العنوان")
        else:
            print("عمود العنوان موجود بالفعل")

        # إضافة الفهارس إذا لم تكن موجودة
        try:
            cursor.execute("CREATE INDEX idx_اسم_بنك ON البنوك(اسم_البنك)")
            print("تم إضافة فهرس اسم البنك")
        except:
            print("فهرس اسم البنك موجود بالفعل")

        try:
            cursor.execute("CREATE INDEX idx_هاتف_بنك ON البنوك(رقم_الهاتف)")
            print("تم إضافة فهرس رقم الهاتف")
        except:
            print("فهرس رقم الهاتف موجود بالفعل")
        
        # حفظ التغييرات
        connection.commit()
        
        # عرض هيكل الجدول المحدث
        cursor.execute("DESCRIBE البنوك")
        columns = cursor.fetchall()
        
        print("\nهيكل جدول البنوك المحدث:")
        print("-" * 60)
        for column in columns:
            print(f"  {column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]}")

        print("\nتم تحديث جدول البنوك بنجاح!")

        # إضافة بيانات تجريبية للحقول الجديدة (اختياري)
        إضافة_بيانات_تجريبية = input("\nهل تريد إضافة بيانات تجريبية للحقول الجديدة؟ (y/n): ")
        if إضافة_بيانات_تجريبية.lower() == 'y':
            تحديث_البيانات_التجريبية(cursor, connection)
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"خطأ في تحديث جدول البنوك: {e}")
        return False
    
    return True

def تحديث_البيانات_التجريبية(cursor, connection):
    """
    تحديث البيانات الموجودة بمعلومات تجريبية
    """
    try:
        print("\nإضافة بيانات تجريبية...")

        # تحديث البنوك الموجودة ببيانات تجريبية
        تحديثات = [
            ("البنك الأهلي السعودي", "920001000", "الرياض، شارع الملك فهد، مبنى البنك الأهلي"),
            ("بنك الراجحي", "920003344", "الرياض، طريق الملك عبدالعزيز، برج الراجحي"),
            ("بنك الرياض", "920002470", "الرياض، شارع العليا، مركز الرياض المالي"),
            ("بنك ساب", "920007777", "الرياض، حي الملز، مجمع ساب المصرفي")
        ]

        for اسم_البنك, رقم_الهاتف, العنوان in تحديثات:
            cursor.execute("""
                UPDATE البنوك
                SET رقم_الهاتف = %s, العنوان = %s
                WHERE اسم_البنك = %s AND رقم_الهاتف IS NULL
            """, (رقم_الهاتف, العنوان, اسم_البنك))

        connection.commit()
        print("تم تحديث البيانات التجريبية")

    except Error as e:
        print(f"خطأ في إضافة البيانات التجريبية: {e}")

if __name__ == "__main__":
    print("سكريبت تحديث جدول البنوك")
    print("=" * 50)

    if تحديث_جدول_البنوك():
        print("\nتم التحديث بنجاح! يمكنك الآن استخدام الحقول الجديدة في واجهة البنوك.")
    else:
        print("\nفشل في التحديث. يرجى التحقق من الأخطاء أعلاه.")
