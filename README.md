# نظام إدارة المبيعات والمخزون

نظام شامل لإدارة المبيعات والمخزون مطور باستخدام Python و PySide6 مع دعم كامل للغة العربية وتخطيط RTL.

## المميزات الرئيسية

### 🏪 نقطة البيع (POS)
- واجهة بيع سهلة الاستخدام
- البحث السريع عن المنتجات (بالاسم، الباركود، الفئة)
- إدارة السلة مع حساب تلقائي للإجماليات
- خيارات دفع متعددة (نقدي، آجل، أقساط)
- ربط المبيعات الآجلة والأقساط بالعملاء
- ترقيم تلقائي للفواتير

### 👥 إدارة العملاء
- إضافة وتعديل وحذف بيانات العملاء
- ربط العملاء بالبنوك للأقساط
- متابعة رصيد العملاء
- بحث متقدم وفلترة
- تقارير العملاء

### 📦 إدارة المخزون
- إدارة شاملة للمنتجات والأصناف مع نوافذ إضافة/تعديل متكاملة
- تصنيف المنتجات بفئات مع إدارة كاملة للفئات
- نظام باركود متقدم مع إنشاء تلقائي
- تتبع الكميات والصلاحيات مع تنبيهات ذكية
- تنبيهات نفاد المخزون بألوان مميزة
- إدارة فواتير الشراء مع فلاتر متقدمة
- إدارة شاملة للموردين مع تتبع الأرصدة

### 🏪 إدارة الموردين
- قاعدة بيانات شاملة للموردين مع واجهة مستقلة
- نوافذ إضافة/تعديل موردين متكاملة
- ربط الفواتير بالموردين تلقائياً
- متابعة الحسابات المالية والأرصدة
- فلاتر بحث متقدمة (الاسم، الهاتف، العنوان، الحالة)
- عرض الديون والأرصدة بألوان مميزة
- تقارير الموردين وإحصائيات شاملة

### 💰 النظام المالي
- إدارة المصروفات والتصنيفات
- نظام الأقساط المصرفية
- إدارة العقود والديون
- تقارير مالية شاملة
- حساب الأرباح والخسائر

### 👨‍💼 إدارة الموظفين
- بيانات الموظفين والرواتب
- نظام الصلاحيات والمستخدمين
- تسجيل الحضور والانصراف
- حساب النسب والعمولات

### 🏦 إدارة البنوك
- إدارة البنوك الرئيسية والفروع
- حسابات الشركة المصرفية
- ربط العملاء بالبنوك للأقساط

## المتطلبات التقنية

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- MySQL Server 8.0 أو أحدث
- PySide6
- mysql-connector-python

### قاعدة البيانات
- **الخادم**: localhost
- **المستخدم**: root
- **كلمة المرور**: kh123456
- **اسم قاعدة البيانات**: Sales_system

## التثبيت والتشغيل

### الطريقة السريعة جداً
```bash
# تشغيل فوري (إذا كانت المتطلبات مثبتة)
python quick_start.py
```

### الطريقة السريعة (موصى بها)
```bash
# إعداد شامل تلقائي
python setup.py

# تشغيل التطبيق مع إصلاح تلقائي
python run.py
```

### إصلاح قاعدة البيانات
```bash
# إصلاح مشاكل قاعدة البيانات
python fix_database.py
```

### اختبار التطبيق
```bash
# اختبار شامل للتطبيق
python test_app.py
```

### الطريقة اليدوية

#### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

أو يدوياً:
```bash
pip install PySide6 mysql-connector-python
```

#### 2. إعداد قاعدة البيانات
- تأكد من تشغيل MySQL Server
- قم بتشغيل ملف `db.py` لإنشاء قاعدة البيانات والجداول:
```bash
python db.py
```

#### 3. تشغيل التطبيق
```bash
python main.py
```

### استكشاف الأخطاء

#### مشاكل قاعدة البيانات
إذا واجهت أخطاء مثل "Unknown column":
```bash
python fix_database.py
```

#### مشاكل عامة
استخدم ملف التشغيل المحسن:
```bash
python run.py
```
سيعرض رسائل خطأ مفصلة ويصلح قاعدة البيانات تلقائياً.

#### اختبار شامل
للتحقق من سلامة جميع المكونات:
```bash
python test_app.py
```

## هيكل المشروع

```
اقومينت/
├── main.py                 # الملف الرئيسي للتطبيق
├── db.py                   # إعداد قاعدة البيانات والجداول
├── pos_interface.py        # واجهة نقطة البيع
├── customers_interface.py  # واجهة إدارة العملاء
├── inventory_interface.py  # واجهة إدارة المخزون
├── other_interfaces.py     # الواجهات المتبقية
├── styles.py              # أنماط CSS للتطبيق
├── setup.py               # ملف الإعداد التلقائي
├── run.py                 # ملف التشغيل المحسن
├── quick_start.py         # تشغيل سريع
├── test_app.py            # اختبار التطبيق
├── fix_database.py        # إصلاح قاعدة البيانات
├── requirements.txt       # قائمة المتطلبات
└── README.md              # هذا الملف
```

## قاعدة البيانات

### الجداول الرئيسية
- **الفئات**: تصنيف المنتجات
- **المنتجات**: بيانات المنتجات والأسعار
- **العملاء**: بيانات العملاء
- **الموردين**: بيانات الموردين
- **البنوك**: البنوك والفروع
- **الموظفين**: بيانات الموظفين والمستخدمين
- **فواتير_البيع**: فواتير المبيعات
- **فواتير_الشراء**: فواتير الشراء
- **العقود**: عقود البيع بالأقساط
- **الأقساط**: تفاصيل الأقساط
- **المصروفات**: المصروفات والتصنيفات

## الواجهات

### الواجهة الرئيسية
- شريط قوائم علوي شامل
- قائمة جانبية قابلة للطي
- منطقة محتوى متجاوبة
- دعم كامل للعربية وتخطيط RTL

### نقطة البيع
- منطقة بحث وعرض المنتجات
- سلة المشتريات التفاعلية
- خيارات الدفع المتعددة
- حفظ وطباعة الفواتير

### إدارة العملاء
- جدول العملاء مع بحث وفلترة
- نماذج إضافة وتعديل العملاء
- عرض تفاصيل العميل
- ربط العملاء بالبنوك

### إدارة المخزون (محسنة) ✨
- **تبويب المنتجات:** إدارة كاملة مع نوافذ إضافة/تعديل متطورة
- **تبويب فواتير الشراء:** عرض وإدارة فواتير الشراء مع فلاتر متقدمة
- **تبويب الموردين:** إدارة شاملة للموردين مع تتبع الأرصدة
- **تبويب الفئات:** إدارة تصنيفات المنتجات مع إحصائيات
- بحث متقدم وفلترة في جميع التبويبات
- تنبيهات المخزون بألوان مميزة
- قوائم سياق تفاعلية

### إدارة الموردين المستقلة (جديدة) 🆕
- واجهة مستقلة لإدارة الموردين
- جدول موردين مع فلاتر متعددة
- نوافذ إضافة/تعديل موردين شاملة
- عرض الأرصدة والديون بألوان مميزة
- أزرار إجراءات متكاملة (إضافة، تعديل، حذف، تقارير)
- بحث متقدم في جميع بيانات الموردين

## المميزات التقنية

### التصميم
- واجهة عربية كاملة RTL
- تصميم متجاوب (1280×720 فما فوق)
- أنماط CSS متقدمة
- ألوان وأيقونات متناسقة

### الأمان
- نظام مستخدمين وصلاحيات
- تشفير كلمات المرور
- حماية قاعدة البيانات

### الأداء
- استعلامات محسنة
- فهرسة قاعدة البيانات
- تحميل تدريجي للبيانات

## الاستخدام

### بدء التشغيل
1. قم بتشغيل التطبيق
2. سيتم إنشاء قاعدة البيانات تلقائياً
3. استخدم المستخدم الافتراضي:
   - **اسم المستخدم**: admin
   - **كلمة المرور**: admin123

### نقطة البيع
1. اختر "نقطة بيع" من القائمة الجانبية
2. ابحث عن المنتجات وأضفها للسلة
3. اختر نوع البيع والعميل (إن أردت)
4. أتمم البيع واطبع الفاتورة

### إدارة العملاء
1. اختر "العملاء" من القائمة الجانبية
2. أضف عملاء جدد أو عدل الموجودين
3. استخدم البحث والفلاتر للعثور على العملاء

## التطوير المستقبلي

### المميزات المخططة
- [ ] تطوير واجهات الموظفين والمصروفات
- [ ] نظام التقارير المتقدم
- [ ] تطبيق الهاتف المحمول
- [ ] النسخ الاحتياطي التلقائي
- [ ] تكامل مع أنظمة الدفع الإلكتروني

### التحسينات التقنية
- [ ] تحسين الأداء
- [ ] إضافة المزيد من التقارير
- [ ] تحسين واجهة المستخدم
- [ ] دعم قواعد بيانات إضافية

## المساهمة

نرحب بالمساهمات في تطوير النظام. يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا النظام بعناية لخدمة الشركات العربية مع مراعاة الاحتياجات المحلية والمتطلبات التقنية الحديثة.**
