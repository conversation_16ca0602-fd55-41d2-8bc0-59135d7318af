# -*- coding: utf-8 -*-
"""
اختبار واجهة إدارة الفواتير
يتضمن اختبار جميع التبويبات والوظائف المطورة
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtCore import Qt
from db import تهيئة_قاعدة_البيانات
from invoices_interface import واجهة_إدارة_الفواتير

class نافذة_اختبار_الفواتير(QMainWindow):
    """
    نافذة اختبار واجهة الفواتير
    """
    
    def __init__(self):
        """
        دالة التهيئة لنافذة الاختبار
        """
        super().__init__()
        self.إعداد_النافذة()
        self.إنشاء_الواجهة()
    
    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة الرئيسية
        """
        self.setWindowTitle("اختبار واجهة إدارة الفواتير - نظام إدارة المبيعات")
        self.setGeometry(100, 100, 1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)
    
    def إنشاء_الواجهة(self):
        """
        دالة إنشاء واجهة الفواتير
        """
        try:
            واجهة_فواتير = واجهة_إدارة_الفواتير()
            self.setCentralWidget(واجهة_فواتير)
            print("تم تحميل واجهة الفواتير بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل واجهة الفواتير: {str(e)}")

def main():
    """
    دالة تشغيل التطبيق الرئيسية
    """
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تهيئة قاعدة البيانات
    print("جاري تهيئة قاعدة البيانات...")
    try:
        تهيئة_قاعدة_البيانات()
        print("تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
        return
    
    # إنشاء النافذة الرئيسية
    نافذة = نافذة_اختبار_الفواتير()
    نافذة.show()
    
    print("=== معلومات واجهة الفواتير ===")
    print("التبويبات المتاحة:")
    print("1. فواتير البيع - إدارة شاملة لفواتير البيع")
    print("2. فواتير الشراء - إدارة شاملة لفواتير الشراء")
    print("3. تقارير الفواتير - تقارير متنوعة للفواتير")
    print("4. إحصائيات المبيعات - إحصائيات تفصيلية")
    print("\nالمميزات:")
    print("- فلترة وبحث متقدم")
    print("- عرض تفاصيل الفواتير")
    print("- إحصائيات سريعة")
    print("- تلوين تفاعلي للبيانات")
    print("- دعم RTL كامل")
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
