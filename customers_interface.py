# -*- coding: utf-8 -*-
"""
واجهة إدارة العملاء
تحتوي على إضافة وتعديل وحذف وعرض بيانات العملاء
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime
from db import قاعدة_البيانات

class واجهة_إدارة_العملاء(QWidget):
    """
    واجهة إدارة العملاء الرئيسية
    تحتوي على جدول العملاء ونماذج الإضافة والتعديل
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة إدارة العملاء
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.العميل_المحدد = None
        
        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()
        self.تحميل_العملاء()

        # تعطيل أزرار التعديل والحذف في البداية
        self.أزرار_الإجراءات["edit_customer"].setEnabled(False)
        self.أزرار_الإجراءات["delete_customer"].setEnabled(False)
        self.أزرار_الإجراءات["view_details"].setEnabled(False)
        self.أزرار_الإجراءات["customer_report"].setEnabled(False)
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة إدارة العملاء
        """
        # التخطيط الرئيسي
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)
        
        # عنوان الصفحة
        عنوان = QLabel("إدارة العملاء")
        عنوان.setObjectName("page_title")
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)
        
        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)
        
        # جدول العملاء
        self.إنشاء_جدول_العملاء(التخطيط_الرئيسي)
    
    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        # إطار البحث
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)
        
        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)
        
        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("اسم العميل، رقم الهاتف، أو رقم الهوية...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_العملاء)
        
        # فلتر الحالة
        تسمية_حالة = QLabel("الحالة:")
        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "نشط", "غير نشط"])
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_العملاء)
        
        # زر البحث المتقدم
        زر_بحث_متقدم = QPushButton("بحث متقدم")
        زر_بحث_متقدم.setObjectName("advanced_search_button")
        زر_بحث_متقدم.clicked.connect(self.فتح_البحث_المتقدم)
        
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث, 2)
        تخطيط_البحث.addWidget(تسمية_حالة)
        تخطيط_البحث.addWidget(self.قائمة_حالة)
        تخطيط_البحث.addWidget(زر_بحث_متقدم)
        
        التخطيط_الرئيسي.addWidget(إطار_البحث)
    
    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط أزرار الإجراءات
        """
        # إطار الأزرار
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(80)
        
        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(15)
        
        # أزرار الإجراءات
        أزرار = [
            ("➕", "إضافة عميل", "add_customer", "#27ae60", self.إضافة_عميل),
            ("✏️", "تعديل", "edit_customer", "#3498db", self.تعديل_عميل),
            ("🗑️", "حذف", "delete_customer", "#e74c3c", self.حذف_عميل),
            ("👁️", "عرض التفاصيل", "view_details", "#9b59b6", self.عرض_تفاصيل_عميل),
            ("📊", "تقرير العميل", "customer_report", "#f39c12", self.تقرير_عميل),
            ("🖨️", "طباعة", "print_customers", "#7f8c8d", self.طباعة_العملاء)
        ]
        
        self.أزرار_الإجراءات = {}
        
        for أيقونة, نص, مفتاح, لون, وظيفة in أزرار:
            زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
            زر.clicked.connect(وظيفة)
            self.أزرار_الإجراءات[مفتاح] = زر
            تخطيط_الأزرار.addWidget(زر)
        
        تخطيط_الأزرار.addStretch()
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)
    
    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(100, 60)
        زر.setObjectName("action_button")
        
        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)
        
        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 20px;")
        
        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 9px; font-weight: bold;")
        تسمية_نص.setWordWrap(True)
        
        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)
        
        # تطبيق اللون
        زر.setStyleSheet(f"""
            QPushButton#action_button {{
                border: 2px solid {لون};
                border-radius: 8px;
                background-color: white;
            }}
            QPushButton#action_button:hover {{
                background-color: {لون};
                color: white;
            }}
            QPushButton#action_button:pressed {{
                background-color: {لون};
                border: 3px solid {لون};
            }}
        """)
        
        return زر
    
    def إنشاء_جدول_العملاء(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول عرض العملاء
        """
        # جدول العملاء
        self.جدول_العملاء = QTableWidget()
        self.جدول_العملاء.setObjectName("customers_table")
        
        # إعداد الأعمدة
        الأعمدة = [
            "رقم العميل", "الاسم الأول", "اللقب", "رقم الهاتف", 
            "العنوان", "إثبات شخصي", "البنك", "رقم الحساب", 
            "الرصيد الحالي", "الحالة", "تاريخ التسجيل"
        ]
        
        self.جدول_العملاء.setColumnCount(len(الأعمدة))
        self.جدول_العملاء.setHorizontalHeaderLabels(الأعمدة)
        
        # إعداد خصائص الجدول
        self.جدول_العملاء.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_العملاء.setAlternatingRowColors(True)
        self.جدول_العملاء.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = self.جدول_العملاء.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الاسم الأول
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # اللقب
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # العنوان
        
        # ربط النقر بتحديد العميل
        self.جدول_العملاء.itemSelectionChanged.connect(self.تحديد_عميل)
        
        # قائمة سياق
        self.جدول_العملاء.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_العملاء.customContextMenuRequested.connect(self.عرض_قائمة_سياق)
        
        التخطيط_الرئيسي.addWidget(self.جدول_العملاء)
    
    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS لواجهة إدارة العملاء
        """
        نمط = """
        QLabel#page_title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }
        
        QFrame#search_frame, QFrame#buttons_frame {
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
        }
        
        QTableWidget#customers_table {
            gridline-color: #bdc3c7;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #3498db;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
        }
        
        QTableWidget#customers_table::item {
            padding: 8px;
            text-align: center;
        }
        
        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
        }
        
        QPushButton#advanced_search_button {
            background-color: #9b59b6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 15px;
            font-weight: bold;
        }
        
        QPushButton#advanced_search_button:hover {
            background-color: #8e44ad;
        }
        
        QLineEdit, QComboBox {
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            padding: 8px;
            font-size: 12px;
        }
        
        QLineEdit:focus, QComboBox:focus {
            border-color: #3498db;
        }
        """
        
        self.setStyleSheet(نمط)

    def تحميل_العملاء(self):
        """
        دالة تحميل جميع العملاء
        """
        try:
            استعلام = """
            SELECT ع.رقم_العميل, ع.الاسم_الأول, ع.اللقب, ع.رقم_الهاتف,
                   ع.العنوان, ع.إثبات_شخصي, ب.اسم_البنك, ع.رقم_الحساب,
                   ع.الرصيد_الحالي,
                   CASE WHEN ع.حالة_النشاط = 1 THEN 'نشط' ELSE 'غير نشط' END as الحالة,
                   DATE_FORMAT(ع.تاريخ_التسجيل, '%Y-%m-%d') as تاريخ_التسجيل
            FROM العملاء ع
            LEFT JOIN البنوك ب ON ع.رقم_البنك = ب.رقم_البنك
            ORDER BY ع.تاريخ_التسجيل DESC
            """

            العملاء = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if العملاء:
                self.جدول_العملاء.setRowCount(len(العملاء))

                for صف, عميل in enumerate(العملاء):
                    for عمود, قيمة in enumerate(عميل):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)

                        # تلوين الرصيد حسب القيمة
                        if عمود == 8:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))  # أحمر للدين
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))  # أخضر للرصيد الموجب

                        # تلوين الحالة
                        if عمود == 9:  # عمود الحالة
                            if قيمة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))

                        self.جدول_العملاء.setItem(صف, عمود, عنصر)
            else:
                self.جدول_العملاء.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل العملاء: {str(e)}")

    def البحث_عن_العملاء(self):
        """
        دالة البحث عن العملاء
        """
        نص_البحث = self.حقل_البحث.text().strip()
        حالة_الفلتر = self.قائمة_حالة.currentText()

        try:
            شرط_البحث = "1=1"
            معاملات = []

            # شرط البحث النصي
            if نص_البحث:
                شرط_البحث += """ AND (
                    ع.الاسم_الأول LIKE %s OR ع.اللقب LIKE %s OR
                    ع.رقم_الهاتف LIKE %s OR ع.إثبات_شخصي LIKE %s
                )"""
                معاملات.extend([f"%{نص_البحث}%"] * 4)

            # شرط فلتر الحالة
            if حالة_الفلتر != "الكل":
                if حالة_الفلتر == "نشط":
                    شرط_البحث += " AND ع.حالة_النشاط = 1"
                else:
                    شرط_البحث += " AND ع.حالة_النشاط = 0"

            استعلام = f"""
            SELECT ع.رقم_العميل, ع.الاسم_الأول, ع.اللقب, ع.رقم_الهاتف,
                   ع.العنوان, ع.إثبات_شخصي, ب.اسم_البنك, ع.رقم_الحساب,
                   ع.الرصيد_الحالي,
                   CASE WHEN ع.حالة_النشاط = 1 THEN 'نشط' ELSE 'غير نشط' END as الحالة,
                   DATE_FORMAT(ع.تاريخ_التسجيل, '%Y-%m-%d') as تاريخ_التسجيل
            FROM العملاء ع
            LEFT JOIN البنوك ب ON ع.رقم_البنك = ب.رقم_البنك
            WHERE {شرط_البحث}
            ORDER BY ع.تاريخ_التسجيل DESC
            """

            العملاء = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_العملاء.setRowCount(0)

            if العملاء:
                self.جدول_العملاء.setRowCount(len(العملاء))

                for صف, عميل in enumerate(العملاء):
                    for عمود, قيمة in enumerate(عميل):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)

                        # تلوين الرصيد حسب القيمة
                        if عمود == 8:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))

                        # تلوين الحالة
                        if عمود == 9:  # عمود الحالة
                            if قيمة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))

                        self.جدول_العملاء.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_عميل(self):
        """
        دالة تحديد العميل المختار
        """
        صف_محدد = self.جدول_العملاء.currentRow()

        if صف_محدد >= 0:
            رقم_العميل = self.جدول_العملاء.item(صف_محدد, 0).text()
            self.العميل_المحدد = int(رقم_العميل)

            # تفعيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_customer"].setEnabled(True)
            self.أزرار_الإجراءات["delete_customer"].setEnabled(True)
            self.أزرار_الإجراءات["view_details"].setEnabled(True)
            self.أزرار_الإجراءات["customer_report"].setEnabled(True)
        else:
            self.العميل_المحدد = None

            # تعطيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_customer"].setEnabled(False)
            self.أزرار_الإجراءات["delete_customer"].setEnabled(False)
            self.أزرار_الإجراءات["view_details"].setEnabled(False)
            self.أزرار_الإجراءات["customer_report"].setEnabled(False)

    def عرض_قائمة_سياق(self, موضع):
        """
        دالة عرض قائمة السياق
        """
        if self.جدول_العملاء.itemAt(موضع):
            قائمة = QMenu(self)

            إجراء_تعديل = قائمة.addAction("تعديل العميل")
            إجراء_تعديل.triggered.connect(self.تعديل_عميل)

            إجراء_حذف = قائمة.addAction("حذف العميل")
            إجراء_حذف.triggered.connect(self.حذف_عميل)

            قائمة.addSeparator()

            إجراء_تفاصيل = قائمة.addAction("عرض التفاصيل")
            إجراء_تفاصيل.triggered.connect(self.عرض_تفاصيل_عميل)

            إجراء_تقرير = قائمة.addAction("تقرير العميل")
            إجراء_تقرير.triggered.connect(self.تقرير_عميل)

            قائمة.exec_(self.جدول_العملاء.mapToGlobal(موضع))

    def إضافة_عميل(self):
        """
        دالة إضافة عميل جديد
        """
        نافذة_إضافة = نافذة_عميل(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_العملاء()

    def تعديل_عميل(self):
        """
        دالة تعديل العميل المحدد
        """
        if not self.العميل_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return

        نافذة_تعديل = نافذة_عميل(self, "تعديل", self.العميل_المحدد)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_العملاء()

    def حذف_عميل(self):
        """
        دالة حذف العميل المحدد
        """
        if not self.العميل_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return

        # الحصول على اسم العميل
        صف_محدد = self.جدول_العملاء.currentRow()
        اسم_عميل = f"{self.جدول_العملاء.item(صف_محدد, 1).text()} {self.جدول_العملاء.item(صف_محدد, 2).text()}"

        رد = QMessageBox.question(self, "تأكيد الحذف",
                                  f"هل تريد حذف العميل '{اسم_عميل}'؟\n"
                                  "تحذير: سيتم حذف جميع البيانات المرتبطة بهذا العميل",
                                  QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM العملاء WHERE رقم_العميل = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.العميل_المحدد,))

                QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
                self.تحميل_العملاء()
                self.العميل_المحدد = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل: {str(e)}")

    def عرض_تفاصيل_عميل(self):
        """
        دالة عرض تفاصيل العميل
        """
        if not self.العميل_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لعرض التفاصيل")
            return

        نافذة_تفاصيل = نافذة_تفاصيل_عميل(self, self.العميل_المحدد)
        نافذة_تفاصيل.exec_()

    def تقرير_عميل(self):
        """
        دالة إنشاء تقرير العميل
        """
        if not self.العميل_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لإنشاء التقرير")
            return

        QMessageBox.information(self, "معلومات", "وظيفة التقارير قيد التطوير")

    def طباعة_العملاء(self):
        """
        دالة طباعة قائمة العملاء
        """
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")

    def فتح_البحث_المتقدم(self):
        """
        دالة فتح نافذة البحث المتقدم
        """
        QMessageBox.information(self, "معلومات", "البحث المتقدم قيد التطوير")

class نافذة_عميل(QDialog):
    """
    نافذة إضافة أو تعديل عميل
    """

    def __init__(self, parent, نوع_العملية, رقم_العميل=None):
        """
        دالة التهيئة لنافذة العميل
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_العميل = رقم_العميل

        self.إعداد_النافذة()
        self.إنشاء_النموذج()
        self.تحميل_البنوك()

        if نوع_العملية == "تعديل" and رقم_العميل:
            self.تحميل_بيانات_العميل()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} عميل")
        self.setFixedSize(500, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج بيانات العميل
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} عميل")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # نموذج البيانات
        نموذج = QFormLayout()
        نموذج.setSpacing(10)

        # الاسم الأول
        self.حقل_الاسم_الأول = QLineEdit()
        self.حقل_الاسم_الأول.setPlaceholderText("أدخل الاسم الأول")
        نموذج.addRow("الاسم الأول *:", self.حقل_الاسم_الأول)

        # اللقب
        self.حقل_اللقب = QLineEdit()
        self.حقل_اللقب.setPlaceholderText("أدخل اللقب")
        نموذج.addRow("اللقب *:", self.حقل_اللقب)

        # رقم الهاتف
        self.حقل_الهاتف = QLineEdit()
        self.حقل_الهاتف.setPlaceholderText("05xxxxxxxx")
        نموذج.addRow("رقم الهاتف:", self.حقل_الهاتف)

        # العنوان
        self.حقل_العنوان = QTextEdit()
        self.حقل_العنوان.setMaximumHeight(80)
        self.حقل_العنوان.setPlaceholderText("أدخل العنوان")
        نموذج.addRow("العنوان:", self.حقل_العنوان)

        # إثبات شخصي
        self.حقل_إثبات_شخصي = QLineEdit()
        self.حقل_إثبات_شخصي.setPlaceholderText("رقم الهوية أو الإقامة")
        نموذج.addRow("إثبات شخصي:", self.حقل_إثبات_شخصي)

        # البنك
        self.قائمة_البنك = QComboBox()
        نموذج.addRow("البنك:", self.قائمة_البنك)

        # رقم الحساب
        self.حقل_رقم_الحساب = QLineEdit()
        self.حقل_رقم_الحساب.setPlaceholderText("رقم الحساب البنكي")
        نموذج.addRow("رقم الحساب:", self.حقل_رقم_الحساب)

        # الوكيل
        self.حقل_الوكيل = QLineEdit()
        self.حقل_الوكيل.setPlaceholderText("اسم الوكيل")
        نموذج.addRow("الوكيل:", self.حقل_الوكيل)

        # من طرف
        self.حقل_من_طرف = QLineEdit()
        self.حقل_من_طرف.setPlaceholderText("من طرف")
        نموذج.addRow("من طرف:", self.حقل_من_طرف)

        # رقم العقد
        self.حقل_رقم_العقد = QLineEdit()
        self.حقل_رقم_العقد.setPlaceholderText("رقم العقد")
        نموذج.addRow("رقم العقد:", self.حقل_رقم_العقد)

        # حالة النشاط
        self.مربع_نشط = QCheckBox("عميل نشط")
        self.مربع_نشط.setChecked(True)
        نموذج.addRow("الحالة:", self.مربع_نشط)

        التخطيط_الرئيسي.addLayout(نموذج)

        # أزرار الحفظ والإلغاء
        تخطيط_أزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        زر_حفظ.clicked.connect(self.حفظ_العميل)

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        زر_إلغاء.clicked.connect(self.reject)

        تخطيط_أزرار.addWidget(زر_حفظ)
        تخطيط_أزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_أزرار)

    def تحميل_البنوك(self):
        """
        دالة تحميل قائمة البنوك
        """
        try:
            استعلام = "SELECT رقم_البنك, اسم_البنك FROM البنوك WHERE حالة_النشاط = TRUE ORDER BY اسم_البنك"
            البنوك = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_البنك.clear()
            self.قائمة_البنك.addItem("اختر البنك...", 0)

            if البنوك:
                for بنك in البنوك:
                    self.قائمة_البنك.addItem(بنك[1], بنك[0])

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البنوك: {str(e)}")

    def تحميل_بيانات_العميل(self):
        """
        دالة تحميل بيانات العميل للتعديل
        """
        try:
            استعلام = """
            SELECT الاسم_الأول, اللقب, رقم_الهاتف, العنوان, إثبات_شخصي,
                   رقم_البنك, رقم_الحساب, الوكيل, من_طرف, رقم_العقد, حالة_النشاط
            FROM العملاء
            WHERE رقم_العميل = %s
            """

            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.رقم_العميل,))

            if نتيجة:
                بيانات = نتيجة[0]

                self.حقل_الاسم_الأول.setText(بيانات[0] or "")
                self.حقل_اللقب.setText(بيانات[1] or "")
                self.حقل_الهاتف.setText(بيانات[2] or "")
                self.حقل_العنوان.setPlainText(بيانات[3] or "")
                self.حقل_إثبات_شخصي.setText(بيانات[4] or "")

                # تحديد البنك
                if بيانات[5]:
                    فهرس = self.قائمة_البنك.findData(بيانات[5])
                    if فهرس >= 0:
                        self.قائمة_البنك.setCurrentIndex(فهرس)

                self.حقل_رقم_الحساب.setText(بيانات[6] or "")
                self.حقل_الوكيل.setText(بيانات[7] or "")
                self.حقل_من_طرف.setText(بيانات[8] or "")
                self.حقل_رقم_العقد.setText(بيانات[9] or "")
                self.مربع_نشط.setChecked(bool(بيانات[10]))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات العميل: {str(e)}")

    def حفظ_العميل(self):
        """
        دالة حفظ بيانات العميل
        """
        # التحقق من البيانات المطلوبة
        if not self.حقل_الاسم_الأول.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم الأول")
            self.حقل_الاسم_الأول.setFocus()
            return

        if not self.حقل_اللقب.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اللقب")
            self.حقل_اللقب.setFocus()
            return

        try:
            # جمع البيانات
            الاسم_الأول = self.حقل_الاسم_الأول.text().strip()
            اللقب = self.حقل_اللقب.text().strip()
            رقم_الهاتف = self.حقل_الهاتف.text().strip() or None
            العنوان = self.حقل_العنوان.toPlainText().strip() or None
            إثبات_شخصي = self.حقل_إثبات_شخصي.text().strip() or None
            رقم_البنك = self.قائمة_البنك.currentData() if self.قائمة_البنك.currentData() != 0 else None
            رقم_الحساب = self.حقل_رقم_الحساب.text().strip() or None
            الوكيل = self.حقل_الوكيل.text().strip() or None
            من_طرف = self.حقل_من_طرف.text().strip() or None
            رقم_العقد = self.حقل_رقم_العقد.text().strip() or None
            حالة_النشاط = self.مربع_نشط.isChecked()

            if self.نوع_العملية == "إضافة":
                # إضافة عميل جديد
                استعلام = """
                INSERT INTO العملاء
                (الاسم_الأول, اللقب, رقم_الهاتف, العنوان, إثبات_شخصي,
                 رقم_البنك, رقم_الحساب, الوكيل, من_طرف, رقم_العقد, حالة_النشاط)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                معاملات = (الاسم_الأول, اللقب, رقم_الهاتف, العنوان, إثبات_شخصي,
                          رقم_البنك, رقم_الحساب, الوكيل, من_طرف, رقم_العقد, حالة_النشاط)

            else:
                # تعديل عميل موجود
                استعلام = """
                UPDATE العملاء SET
                الاسم_الأول = %s, اللقب = %s, رقم_الهاتف = %s, العنوان = %s,
                إثبات_شخصي = %s, رقم_البنك = %s, رقم_الحساب = %s, الوكيل = %s,
                من_طرف = %s, رقم_العقد = %s, حالة_النشاط = %s
                WHERE رقم_العميل = %s
                """

                معاملات = (الاسم_الأول, اللقب, رقم_الهاتف, العنوان, إثبات_شخصي,
                          رقم_البنك, رقم_الحساب, الوكيل, من_طرف, رقم_العقد,
                          حالة_النشاط, self.رقم_العميل)

            self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            QMessageBox.information(self, "نجح", f"تم {self.نوع_العملية} العميل بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ العميل: {str(e)}")

class نافذة_تفاصيل_عميل(QDialog):
    """
    نافذة عرض تفاصيل العميل
    """

    def __init__(self, parent, رقم_العميل):
        """
        دالة التهيئة لنافذة تفاصيل العميل
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.رقم_العميل = رقم_العميل

        self.إعداد_النافذة()
        self.إنشاء_المحتوى()
        self.تحميل_التفاصيل()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle("تفاصيل العميل")
        self.setFixedSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_المحتوى(self):
        """
        دالة إنشاء محتوى النافذة
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel("تفاصيل العميل")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        عنوان.setAlignment(Qt.AlignCenter)
        التخطيط_الرئيسي.addWidget(عنوان)

        # منطقة التفاصيل
        self.منطقة_التفاصيل = QScrollArea()
        self.منطقة_التفاصيل.setWidgetResizable(True)
        self.منطقة_التفاصيل.setStyleSheet("""
            QScrollArea {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
        """)

        التخطيط_الرئيسي.addWidget(self.منطقة_التفاصيل)

        # زر الإغلاق
        زر_إغلاق = QPushButton("إغلاق")
        زر_إغلاق.setStyleSheet("""
            QPushButton {
                background-color: #7f8c8d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6c7b7d;
            }
        """)
        زر_إغلاق.clicked.connect(self.accept)

        تخطيط_زر = QHBoxLayout()
        تخطيط_زر.addStretch()
        تخطيط_زر.addWidget(زر_إغلاق)
        تخطيط_زر.addStretch()

        التخطيط_الرئيسي.addLayout(تخطيط_زر)

    def تحميل_التفاصيل(self):
        """
        دالة تحميل تفاصيل العميل
        """
        try:
            # الحصول على بيانات العميل
            استعلام_عميل = """
            SELECT ع.*, ب.اسم_البنك
            FROM العملاء ع
            LEFT JOIN البنوك ب ON ع.رقم_البنك = ب.رقم_البنك
            WHERE ع.رقم_العميل = %s
            """

            بيانات_عميل = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_عميل, (self.رقم_العميل,))

            if بيانات_عميل:
                عميل = بيانات_عميل[0]

                # إنشاء widget للتفاصيل
                widget_تفاصيل = QWidget()
                تخطيط_تفاصيل = QVBoxLayout(widget_تفاصيل)
                تخطيط_تفاصيل.setContentsMargins(15, 15, 15, 15)
                تخطيط_تفاصيل.setSpacing(10)

                # البيانات الأساسية
                تخطيط_تفاصيل.addWidget(self.إنشاء_قسم("البيانات الأساسية", [
                    ("رقم العميل", str(عميل[0])),
                    ("الاسم الكامل", f"{عميل[1]} {عميل[2]}"),
                    ("رقم الهاتف", عميل[3] or "غير محدد"),
                    ("العنوان", عميل[4] or "غير محدد"),
                    ("إثبات شخصي", عميل[5] or "غير محدد"),
                    ("الحالة", "نشط" if عميل[10] else "غير نشط"),
                    ("تاريخ التسجيل", str(عميل[11]))
                ]))

                # البيانات المصرفية
                تخطيط_تفاصيل.addWidget(self.إنشاء_قسم("البيانات المصرفية", [
                    ("البنك", عميل[12] or "غير محدد"),
                    ("رقم الحساب", عميل[7] or "غير محدد"),
                    ("الرصيد الحالي", f"{عميل[9]:.2f} ريال")
                ]))

                # بيانات إضافية
                تخطيط_تفاصيل.addWidget(self.إنشاء_قسم("بيانات إضافية", [
                    ("الوكيل", عميل[8] or "غير محدد"),
                    ("من طرف", عميل[13] or "غير محدد"),
                    ("رقم العقد", عميل[14] or "غير محدد")
                ]))

                self.منطقة_التفاصيل.setWidget(widget_تفاصيل)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل العميل: {str(e)}")

    def إنشاء_قسم(self, عنوان_القسم, البيانات):
        """
        دالة إنشاء قسم من التفاصيل
        """
        إطار_قسم = QFrame()
        إطار_قسم.setStyleSheet("""
            QFrame {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #f8f9fa;
                margin: 5px;
            }
        """)

        تخطيط_قسم = QVBoxLayout(إطار_قسم)
        تخطيط_قسم.setContentsMargins(15, 10, 15, 10)
        تخطيط_قسم.setSpacing(8)

        # عنوان القسم
        تسمية_عنوان = QLabel(عنوان_القسم)
        تسمية_عنوان.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            padding: 5px;
            background-color: #ecf0f1;
            border-radius: 4px;
        """)
        تخطيط_قسم.addWidget(تسمية_عنوان)

        # البيانات
        for مفتاح, قيمة in البيانات:
            تخطيط_صف = QHBoxLayout()

            تسمية_مفتاح = QLabel(f"{مفتاح}:")
            تسمية_مفتاح.setStyleSheet("font-weight: bold; color: #34495e;")
            تسمية_مفتاح.setFixedWidth(120)

            تسمية_قيمة = QLabel(str(قيمة))
            تسمية_قيمة.setStyleSheet("color: #2c3e50;")

            تخطيط_صف.addWidget(تسمية_مفتاح)
            تخطيط_صف.addWidget(تسمية_قيمة)
            تخطيط_صف.addStretch()

            تخطيط_قسم.addLayout(تخطيط_صف)

        return إطار_قسم
