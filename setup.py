#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعداد التطبيق
"""

import subprocess
import sys
import os

def install_requirements():
    """
    دالة تثبيت المتطلبات
    """
    print("جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"فشل في تثبيت المتطلبات: {e}")
        return False

def check_mysql():
    """
    دالة فحص MySQL
    """
    print("جاري فحص MySQL...")
    try:
        import mysql.connector
        
        # محاولة الاتصال
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password="kh123456"
        )
        connection.close()
        print("MySQL متاح ويعمل بشكل صحيح")
        return True
        
    except ImportError:
        print("مكتبة mysql-connector-python غير مثبتة")
        return False
    except mysql.connector.Error as e:
        print(f"خطأ في الاتصال بـ MySQL: {e}")
        print("تأكد من:")
        print("1. تشغيل MySQL Server")
        print("2. صحة اسم المستخدم وكلمة المرور")
        print("3. السماح بالاتصال من localhost")
        return False

def setup_database():
    """
    دالة إعداد قاعدة البيانات
    """
    print("جاري إعداد قاعدة البيانات...")
    try:
        from db import تهيئة_قاعدة_البيانات
        
        if تهيئة_قاعدة_البيانات():
            print("تم إعداد قاعدة البيانات بنجاح")
            return True
        else:
            print("فشل في إعداد قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"خطأ في إعداد قاعدة البيانات: {e}")
        return False

def main():
    """
    دالة الإعداد الرئيسية
    """
    print("=== إعداد نظام إدارة المبيعات والمخزون ===")
    print()
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("فشل في تثبيت المتطلبات. توقف الإعداد.")
        return False
    
    print()
    
    # فحص MySQL
    if not check_mysql():
        print("فشل في فحص MySQL. توقف الإعداد.")
        return False
    
    print()
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("فشل في إعداد قاعدة البيانات. توقف الإعداد.")
        return False
    
    print()
    print("=== تم الإعداد بنجاح! ===")
    print()
    print("لتشغيل التطبيق:")
    print("python run.py")
    print()
    print("أو:")
    print("python main.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
