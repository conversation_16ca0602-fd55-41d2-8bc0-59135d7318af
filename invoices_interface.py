# -*- coding: utf-8 -*-
"""
واجهة إدارة الفواتير الشاملة
تحتوي على إدارة فواتير البيع والشراء مع جميع الوظائف المطلوبة
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime, date
from decimal import Decimal
from db import قاعدة_البيانات

class واجهة_إدارة_الفواتير(QWidget):
    """
    واجهة إدارة الفواتير الشاملة
    تحتوي على تبويبات لفواتير البيع والشراء
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة إدارة الفواتير
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة إدارة الفواتير
        """
        # التخطيط الرئيسي
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)
        
        # عنوان الصفحة
        عنوان = QLabel("إدارة الفواتير")
        عنوان.setObjectName("page_title")
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # التبويبات
        self.تبويبات = QTabWidget()
        self.تبويبات.setObjectName("main_tabs")
        
        # تبويب فواتير البيع
        self.تبويب_فواتير_البيع = تبويب_فواتير_البيع(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_فواتير_البيع, "فواتير البيع")
        
        # تبويب فواتير الشراء
        self.تبويب_فواتير_الشراء = تبويب_فواتير_الشراء(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_فواتير_الشراء, "فواتير الشراء")
        
        # تبويب تقارير الفواتير
        self.تبويب_تقارير_الفواتير = تبويب_تقارير_الفواتير(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_تقارير_الفواتير, "تقارير الفواتير")
        
        # تبويب إحصائيات المبيعات
        self.تبويب_إحصائيات_المبيعات = تبويب_إحصائيات_المبيعات(self.قاعدة_البيانات)
        self.تبويبات.addTab(self.تبويب_إحصائيات_المبيعات, "إحصائيات المبيعات")
        
        التخطيط_الرئيسي.addWidget(self.تبويبات)
    
    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS لواجهة الفواتير
        """
        نمط = """
        QLabel#page_title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #9b59b6;
        }
        
        QTabWidget#main_tabs {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }
        
        QTabWidget#main_tabs::pane {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }
        
        QTabWidget#main_tabs::tab-bar {
            alignment: right;
        }
        
        QTabBar::tab {
            background-color: #ecf0f1;
            color: #2c3e50;
            padding: 8px 15px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-weight: bold;
        }
        
        QTabBar::tab:selected {
            background-color: #9b59b6;
            color: white;
        }
        
        QTabBar::tab:hover {
            background-color: #8e44ad;
            color: white;
        }
        """
        self.setStyleSheet(نمط)

        # تطبيق أنماط إضافية للإطارات والجداول
        نمط_إضافي = """
        QFrame#search_frame, QFrame#buttons_frame, QFrame#status_frame, QFrame#reports_frame {
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
        }

        QTableWidget#sales_invoices_table, QTableWidget#purchase_invoices_table {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
            gridline-color: #ecf0f1;
        }

        QTableWidget#sales_invoices_table::item, QTableWidget#purchase_invoices_table::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }

        QTableWidget#sales_invoices_table::item:selected, QTableWidget#purchase_invoices_table::item:selected {
            background-color: #9b59b6;
            color: white;
        }

        QPushButton {
            background-color: #9b59b6;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            font-weight: bold;
            min-width: 100px;
        }

        QPushButton:hover {
            background-color: #8e44ad;
        }

        QPushButton:pressed {
            background-color: #7d3c98;
        }

        QPushButton:disabled {
            background-color: #bdc3c7;
            color: #7f8c8d;
        }

        QLineEdit, QComboBox {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 12px;
        }

        QLineEdit:focus, QComboBox:focus {
            border-color: #9b59b6;
        }

        QTextEdit#report_area {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 10px;
        }
        """

        # تطبيق الأنماط الإضافية على جميع التبويبات
        for i in range(self.تبويبات.count()):
            تبويب = self.تبويبات.widget(i)
            if تبويب:
                تبويب.setStyleSheet(نمط_إضافي)


class تبويب_فواتير_البيع(QWidget):
    """
    تبويب إدارة فواتير البيع
    """
    
    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب فواتير البيع
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.الفاتورة_المحددة = None
        
        self.إعداد_التبويب()
    
    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب فواتير البيع
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)
        
        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)
        
        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)
        
        # جدول فواتير البيع
        self.إنشاء_جدول_فواتير_البيع(التخطيط_الرئيسي)
        
        # شريط الحالة
        self.إنشاء_شريط_الحالة(التخطيط_الرئيسي)
        
        # تحميل البيانات
        self.تحميل_فواتير_البيع()
    
    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)
        
        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)
        
        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("رقم الفاتورة، اسم العميل، أو رقم الهاتف...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الفواتير)
        
        # فلتر نوع البيع
        تسمية_نوع_بيع = QLabel("نوع البيع:")
        self.قائمة_نوع_بيع = QComboBox()
        self.قائمة_نوع_بيع.addItems(["الكل", "نقدي", "آجل", "أقساط"])
        self.قائمة_نوع_بيع.currentTextChanged.connect(self.البحث_عن_الفواتير)
        
        # فلتر حالة السداد
        تسمية_حالة_سداد = QLabel("حالة السداد:")
        self.قائمة_حالة_سداد = QComboBox()
        self.قائمة_حالة_سداد.addItems(["الكل", "مسددة", "جزئية", "غير مسددة"])
        self.قائمة_حالة_سداد.currentTextChanged.connect(self.البحث_عن_الفواتير)
        
        # فلتر التاريخ
        تسمية_تاريخ = QLabel("الفترة:")
        self.قائمة_تاريخ = QComboBox()
        self.قائمة_تاريخ.addItems(["الكل", "اليوم", "هذا الأسبوع", "هذا الشهر", "آخر 3 شهور"])
        self.قائمة_تاريخ.currentTextChanged.connect(self.البحث_عن_الفواتير)
        
        # إضافة العناصر للتخطيط
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(تسمية_نوع_بيع)
        تخطيط_البحث.addWidget(self.قائمة_نوع_بيع)
        تخطيط_البحث.addWidget(تسمية_حالة_سداد)
        تخطيط_البحث.addWidget(self.قائمة_حالة_سداد)
        تخطيط_البحث.addWidget(تسمية_تاريخ)
        تخطيط_البحث.addWidget(self.قائمة_تاريخ)
        تخطيط_البحث.addStretch()
        
        التخطيط_الرئيسي.addWidget(إطار_البحث)
    
    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الأزرار
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(60)
        
        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(10)
        
        # أزرار الإجراءات
        self.أزرار_الإجراءات = {}
        
        # زر عرض تفاصيل الفاتورة
        self.أزرار_الإجراءات["view_details"] = QPushButton("عرض التفاصيل")
        self.أزرار_الإجراءات["view_details"].clicked.connect(self.عرض_تفاصيل_فاتورة)
        
        # زر طباعة الفاتورة
        self.أزرار_الإجراءات["print_invoice"] = QPushButton("طباعة الفاتورة")
        self.أزرار_الإجراءات["print_invoice"].clicked.connect(self.طباعة_فاتورة)
        
        # زر تحديث حالة السداد
        self.أزرار_الإجراءات["update_payment"] = QPushButton("تحديث السداد")
        self.أزرار_الإجراءات["update_payment"].clicked.connect(self.تحديث_حالة_السداد)
        
        # زر إنشاء عقد (للأقساط)
        self.أزرار_الإجراءات["create_contract"] = QPushButton("إنشاء عقد")
        self.أزرار_الإجراءات["create_contract"].clicked.connect(self.إنشاء_عقد_من_فاتورة)
        
        # زر حذف فاتورة
        self.أزرار_الإجراءات["delete_invoice"] = QPushButton("حذف فاتورة")
        self.أزرار_الإجراءات["delete_invoice"].clicked.connect(self.حذف_فاتورة)
        
        # زر تحديث البيانات
        زر_تحديث = QPushButton("تحديث البيانات")
        زر_تحديث.clicked.connect(self.تحميل_فواتير_البيع)
        
        # إضافة الأزرار للتخطيط
        for زر in self.أزرار_الإجراءات.values():
            تخطيط_الأزرار.addWidget(زر)
            زر.setEnabled(False)  # تعطيل الأزرار في البداية
        
        تخطيط_الأزرار.addWidget(زر_تحديث)
        تخطيط_الأزرار.addStretch()
        
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_جدول_فواتير_البيع(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول فواتير البيع
        """
        self.جدول_فواتير_البيع = QTableWidget()
        self.جدول_فواتير_البيع.setObjectName("sales_invoices_table")

        # تحديد الأعمدة
        الأعمدة = [
            "رقم الفاتورة", "اسم العميل", "نوع البيع", "إجمالي الفاتورة",
            "الخصم", "المبلغ النهائي", "المبلغ المدفوع", "المبلغ المتبقي",
            "حالة السداد", "تاريخ الفاتورة", "الموظف"
        ]

        self.جدول_فواتير_البيع.setColumnCount(len(الأعمدة))
        self.جدول_فواتير_البيع.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_فواتير_البيع.setAlternatingRowColors(True)
        self.جدول_فواتير_البيع.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_فواتير_البيع.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_فواتير_البيع.setSortingEnabled(True)

        # ضبط عرض الأعمدة
        header = self.جدول_فواتير_البيع.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(الأعمدة)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # ربط الأحداث
        self.جدول_فواتير_البيع.itemSelectionChanged.connect(self.عند_تحديد_فاتورة)
        self.جدول_فواتير_البيع.itemDoubleClicked.connect(self.عرض_تفاصيل_فاتورة)

        التخطيط_الرئيسي.addWidget(self.جدول_فواتير_البيع)

    def إنشاء_شريط_الحالة(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الحالة
        """
        إطار_الحالة = QFrame()
        إطار_الحالة.setObjectName("status_frame")
        إطار_الحالة.setFixedHeight(40)

        تخطيط_الحالة = QHBoxLayout(إطار_الحالة)
        تخطيط_الحالة.setContentsMargins(15, 5, 15, 5)
        تخطيط_الحالة.setSpacing(20)

        # إحصائيات سريعة
        self.تسمية_عدد_الفواتير = QLabel("عدد الفواتير: 0")
        self.تسمية_إجمالي_المبيعات = QLabel("إجمالي المبيعات: 0.00")
        self.تسمية_المبالغ_المحصلة = QLabel("المبالغ المحصلة: 0.00")
        self.تسمية_المبالغ_المتبقية = QLabel("المبالغ المتبقية: 0.00")

        تخطيط_الحالة.addWidget(self.تسمية_عدد_الفواتير)
        تخطيط_الحالة.addWidget(self.تسمية_إجمالي_المبيعات)
        تخطيط_الحالة.addWidget(self.تسمية_المبالغ_المحصلة)
        تخطيط_الحالة.addWidget(self.تسمية_المبالغ_المتبقية)
        تخطيط_الحالة.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الحالة)

    def تحميل_فواتير_البيع(self):
        """
        دالة تحميل جميع فواتير البيع
        """
        try:
            استعلام = """
            SELECT
                ف.رقم_الفاتورة,
                COALESCE(CONCAT(ع.الاسم_الأول, ' ', ع.اللقب), 'عميل نقدي') as اسم_العميل,
                ف.نوع_البيع,
                ف.إجمالي_الفاتورة,
                ف.الخصم,
                ف.المبلغ_النهائي,
                ف.المبلغ_المدفوع,
                ف.المبلغ_المتبقي,
                ف.حالة_السداد,
                DATE_FORMAT(ف.تاريخ_الفاتورة, '%Y-%m-%d') as تاريخ_الفاتورة,
                COALESCE(م.الاسم_الكامل, 'غير محدد') as اسم_الموظف
            FROM فواتير_البيع ف
            LEFT JOIN العملاء ع ON ف.رقم_العميل = ع.رقم_العميل
            LEFT JOIN الموظفين م ON ف.رقم_الموظف = م.رقم_الموظف
            ORDER BY ف.تاريخ_الإنشاء DESC
            """

            الفواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.جدول_فواتير_البيع.setRowCount(0)

            if الفواتير:
                self.جدول_فواتير_البيع.setRowCount(len(الفواتير))

                for صف, فاتورة in enumerate(الفواتير):
                    for عمود, قيمة in enumerate(فاتورة):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة is not None else "")

                        # تلوين حالة السداد
                        if عمود == 8:  # عمود حالة السداد
                            if قيمة == "مسددة":
                                عنصر.setBackground(QColor("#d5f4e6"))
                            elif قيمة == "جزئية":
                                عنصر.setBackground(QColor("#fff3cd"))
                            elif قيمة == "غير مسددة":
                                عنصر.setBackground(QColor("#f8d7da"))

                        # تلوين نوع البيع
                        elif عمود == 2:  # عمود نوع البيع
                            if قيمة == "نقدي":
                                عنصر.setForeground(QColor("#27ae60"))
                            elif قيمة == "آجل":
                                عنصر.setForeground(QColor("#f39c12"))
                            elif قيمة == "أقساط":
                                عنصر.setForeground(QColor("#e74c3c"))

                        # تلوين المبالغ
                        elif عمود in [3, 5, 6, 7]:  # أعمدة المبالغ
                            مبلغ = float(قيمة) if قيمة else 0.0
                            عنصر.setText(f"{مبلغ:,.2f}")
                            if عمود == 7 and مبلغ > 0:  # المبلغ المتبقي
                                عنصر.setForeground(QColor("#e74c3c"))

                        self.جدول_فواتير_البيع.setItem(صف, عمود, عنصر)

            # تحديث الإحصائيات
            self.تحديث_إحصائيات_البيع()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل فواتير البيع: {str(e)}")

    def تحديث_إحصائيات_البيع(self):
        """
        دالة تحديث الإحصائيات السريعة
        """
        try:
            # عدد الفواتير
            استعلام_عدد = "SELECT COUNT(*) FROM فواتير_البيع"
            عدد_الفواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_عدد)[0][0]

            # إجمالي المبيعات
            استعلام_مبيعات = "SELECT COALESCE(SUM(المبلغ_النهائي), 0) FROM فواتير_البيع"
            إجمالي_المبيعات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_مبيعات)[0][0]

            # المبالغ المحصلة
            استعلام_محصلة = "SELECT COALESCE(SUM(المبلغ_المدفوع), 0) FROM فواتير_البيع"
            المبالغ_المحصلة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_محصلة)[0][0]

            # المبالغ المتبقية
            استعلام_متبقية = "SELECT COALESCE(SUM(المبلغ_المتبقي), 0) FROM فواتير_البيع"
            المبالغ_المتبقية = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_متبقية)[0][0]

            # تحديث التسميات
            self.تسمية_عدد_الفواتير.setText(f"عدد الفواتير: {عدد_الفواتير}")
            self.تسمية_إجمالي_المبيعات.setText(f"إجمالي المبيعات: {إجمالي_المبيعات:,.2f}")
            self.تسمية_المبالغ_المحصلة.setText(f"المبالغ المحصلة: {المبالغ_المحصلة:,.2f}")
            self.تسمية_المبالغ_المتبقية.setText(f"المبالغ المتبقية: {المبالغ_المتبقية:,.2f}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def البحث_عن_الفواتير(self):
        """
        دالة البحث والفلترة في فواتير البيع
        """
        نص_البحث = self.حقل_البحث.text().strip()
        نوع_بيع_مختار = self.قائمة_نوع_بيع.currentText()
        حالة_سداد_مختارة = self.قائمة_حالة_سداد.currentText()

        for صف in range(self.جدول_فواتير_البيع.rowCount()):
            إظهار_الصف = True

            # فلترة النص
            if نص_البحث:
                وجد_النص = False
                for عمود in range(self.جدول_فواتير_البيع.columnCount()):
                    عنصر = self.جدول_فواتير_البيع.item(صف, عمود)
                    if عنصر and نص_البحث.lower() in عنصر.text().lower():
                        وجد_النص = True
                        break
                if not وجد_النص:
                    إظهار_الصف = False

            # فلترة نوع البيع
            if نوع_بيع_مختار != "الكل" and إظهار_الصف:
                عنصر_نوع = self.جدول_فواتير_البيع.item(صف, 2)
                if not عنصر_نوع or عنصر_نوع.text() != نوع_بيع_مختار:
                    إظهار_الصف = False

            # فلترة حالة السداد
            if حالة_سداد_مختارة != "الكل" and إظهار_الصف:
                عنصر_حالة = self.جدول_فواتير_البيع.item(صف, 8)
                if not عنصر_حالة or عنصر_حالة.text() != حالة_سداد_مختارة:
                    إظهار_الصف = False

            self.جدول_فواتير_البيع.setRowHidden(صف, not إظهار_الصف)

    def عند_تحديد_فاتورة(self):
        """
        دالة تنفذ عند تحديد فاتورة من الجدول
        """
        الصف_المحدد = self.جدول_فواتير_البيع.currentRow()

        if الصف_المحدد >= 0:
            عنصر_رقم_فاتورة = self.جدول_فواتير_البيع.item(الصف_المحدد, 0)
            if عنصر_رقم_فاتورة:
                self.الفاتورة_المحددة = int(عنصر_رقم_فاتورة.text())

                # تفعيل أزرار الإجراءات
                for زر in self.أزرار_الإجراءات.values():
                    زر.setEnabled(True)

                # تفعيل زر إنشاء عقد فقط للفواتير بالأقساط
                عنصر_نوع_بيع = self.جدول_فواتير_البيع.item(الصف_المحدد, 2)
                if عنصر_نوع_بيع and عنصر_نوع_بيع.text() == "أقساط":
                    self.أزرار_الإجراءات["create_contract"].setEnabled(True)
                else:
                    self.أزرار_الإجراءات["create_contract"].setEnabled(False)
        else:
            self.الفاتورة_المحددة = None
            # تعطيل أزرار الإجراءات
            for زر in self.أزرار_الإجراءات.values():
                زر.setEnabled(False)

    def عرض_تفاصيل_فاتورة(self):
        """
        دالة عرض تفاصيل الفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        حوار = حوار_تفاصيل_فاتورة(self, self.الفاتورة_المحددة, "بيع")
        حوار.exec()

    def طباعة_فاتورة(self):
        """
        دالة طباعة الفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        QMessageBox.information(self, "معلومات", f"طباعة الفاتورة رقم {self.الفاتورة_المحددة} - قيد التطوير")

    def تحديث_حالة_السداد(self):
        """
        دالة تحديث حالة السداد للفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تحديث حالة السداد للفاتورة رقم {self.الفاتورة_المحددة} - قيد التطوير")

    def إنشاء_عقد_من_فاتورة(self):
        """
        دالة إنشاء عقد من الفاتورة المحددة (للأقساط)
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        QMessageBox.information(self, "معلومات", f"إنشاء عقد من الفاتورة رقم {self.الفاتورة_المحددة} - قيد التطوير")

    def حذف_فاتورة(self):
        """
        دالة حذف الفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        رد = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الفاتورة رقم {self.الفاتورة_المحددة}؟\n"
            "سيتم حذف جميع تفاصيل الفاتورة أيضاً.",
            QMessageBox.Yes | QMessageBox.No
        )

        if رد == QMessageBox.Yes:
            try:
                # حذف تفاصيل الفاتورة أولاً
                استعلام_تفاصيل = "DELETE FROM تفاصيل_فواتير_البيع WHERE رقم_الفاتورة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تفاصيل, (self.الفاتورة_المحددة,))

                # حذف الفاتورة
                استعلام_فاتورة = "DELETE FROM فواتير_البيع WHERE رقم_الفاتورة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة, (self.الفاتورة_المحددة,))

                QMessageBox.information(self, "نجح", "تم حذف الفاتورة بنجاح")
                self.تحميل_فواتير_البيع()
                self.الفاتورة_المحددة = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الفاتورة: {str(e)}")


class تبويب_فواتير_الشراء(QWidget):
    """
    تبويب إدارة فواتير الشراء
    """

    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب فواتير الشراء
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات
        self.الفاتورة_المحددة = None

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب فواتير الشراء
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # شريط البحث والفلاتر
        self.إنشاء_شريط_البحث(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # جدول فواتير الشراء
        self.إنشاء_جدول_فواتير_الشراء(التخطيط_الرئيسي)

        # شريط الحالة
        self.إنشاء_شريط_الحالة(التخطيط_الرئيسي)

        # تحميل البيانات
        self.تحميل_فواتير_الشراء()

    def إنشاء_شريط_البحث(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط البحث والفلاتر
        """
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(70)

        تخطيط_البحث = QHBoxLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(15, 10, 15, 10)
        تخطيط_البحث.setSpacing(15)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("رقم الفاتورة، اسم المورد، أو رقم فاتورة المورد...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الفواتير)

        # فلتر المورد
        تسمية_مورد = QLabel("المورد:")
        self.قائمة_مورد = QComboBox()
        self.تحميل_الموردين()
        self.قائمة_مورد.currentTextChanged.connect(self.البحث_عن_الفواتير)

        # فلتر حالة السداد
        تسمية_حالة_سداد = QLabel("حالة السداد:")
        self.قائمة_حالة_سداد = QComboBox()
        self.قائمة_حالة_سداد.addItems(["الكل", "مسددة", "جزئية", "غير مسددة"])
        self.قائمة_حالة_سداد.currentTextChanged.connect(self.البحث_عن_الفواتير)

        # فلتر التاريخ
        تسمية_تاريخ = QLabel("الفترة:")
        self.قائمة_تاريخ = QComboBox()
        self.قائمة_تاريخ.addItems(["الكل", "اليوم", "هذا الأسبوع", "هذا الشهر", "آخر 3 شهور"])
        self.قائمة_تاريخ.currentTextChanged.connect(self.البحث_عن_الفواتير)

        # إضافة العناصر للتخطيط
        تخطيط_البحث.addWidget(تسمية_البحث)
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(تسمية_مورد)
        تخطيط_البحث.addWidget(self.قائمة_مورد)
        تخطيط_البحث.addWidget(تسمية_حالة_سداد)
        تخطيط_البحث.addWidget(self.قائمة_حالة_سداد)
        تخطيط_البحث.addWidget(تسمية_تاريخ)
        تخطيط_البحث.addWidget(self.قائمة_تاريخ)
        تخطيط_البحث.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_البحث)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الأزرار
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("buttons_frame")
        إطار_الأزرار.setFixedHeight(60)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(15, 10, 15, 10)
        تخطيط_الأزرار.setSpacing(10)

        # أزرار الإجراءات
        self.أزرار_الإجراءات = {}

        # زر إضافة فاتورة شراء
        self.أزرار_الإجراءات["add_invoice"] = QPushButton("إضافة فاتورة")
        self.أزرار_الإجراءات["add_invoice"].clicked.connect(self.إضافة_فاتورة_شراء)

        # زر عرض تفاصيل الفاتورة
        self.أزرار_الإجراءات["view_details"] = QPushButton("عرض التفاصيل")
        self.أزرار_الإجراءات["view_details"].clicked.connect(self.عرض_تفاصيل_فاتورة)

        # زر تعديل فاتورة
        self.أزرار_الإجراءات["edit_invoice"] = QPushButton("تعديل فاتورة")
        self.أزرار_الإجراءات["edit_invoice"].clicked.connect(self.تعديل_فاتورة_شراء)

        # زر تحديث حالة السداد
        self.أزرار_الإجراءات["update_payment"] = QPushButton("تحديث السداد")
        self.أزرار_الإجراءات["update_payment"].clicked.connect(self.تحديث_حالة_السداد)

        # زر حذف فاتورة
        self.أزرار_الإجراءات["delete_invoice"] = QPushButton("حذف فاتورة")
        self.أزرار_الإجراءات["delete_invoice"].clicked.connect(self.حذف_فاتورة)

        # زر تحديث البيانات
        زر_تحديث = QPushButton("تحديث البيانات")
        زر_تحديث.clicked.connect(self.تحميل_فواتير_الشراء)

        # إضافة الأزرار للتخطيط
        # زر إضافة فاتورة دائماً مفعل
        تخطيط_الأزرار.addWidget(self.أزرار_الإجراءات["add_invoice"])

        for مفتاح, زر in self.أزرار_الإجراءات.items():
            if مفتاح != "add_invoice":
                تخطيط_الأزرار.addWidget(زر)
                زر.setEnabled(False)  # تعطيل الأزرار في البداية

        تخطيط_الأزرار.addWidget(زر_تحديث)
        تخطيط_الأزرار.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_جدول_فواتير_الشراء(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول فواتير الشراء
        """
        self.جدول_فواتير_الشراء = QTableWidget()
        self.جدول_فواتير_الشراء.setObjectName("purchase_invoices_table")

        # تحديد الأعمدة
        الأعمدة = [
            "رقم الفاتورة", "اسم المورد", "رقم فاتورة المورد", "تاريخ الشراء",
            "إجمالي الفاتورة", "المبلغ المدفوع", "المبلغ المتبقي", "حالة السداد", "ملاحظات"
        ]

        self.جدول_فواتير_الشراء.setColumnCount(len(الأعمدة))
        self.جدول_فواتير_الشراء.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_فواتير_الشراء.setAlternatingRowColors(True)
        self.جدول_فواتير_الشراء.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_فواتير_الشراء.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_فواتير_الشراء.setSortingEnabled(True)

        # ضبط عرض الأعمدة
        header = self.جدول_فواتير_الشراء.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(الأعمدة)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # ربط الأحداث
        self.جدول_فواتير_الشراء.itemSelectionChanged.connect(self.عند_تحديد_فاتورة)
        self.جدول_فواتير_الشراء.itemDoubleClicked.connect(self.عرض_تفاصيل_فاتورة)

        التخطيط_الرئيسي.addWidget(self.جدول_فواتير_الشراء)

    def إنشاء_شريط_الحالة(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الحالة
        """
        إطار_الحالة = QFrame()
        إطار_الحالة.setObjectName("status_frame")
        إطار_الحالة.setFixedHeight(40)

        تخطيط_الحالة = QHBoxLayout(إطار_الحالة)
        تخطيط_الحالة.setContentsMargins(15, 5, 15, 5)
        تخطيط_الحالة.setSpacing(20)

        # إحصائيات سريعة
        self.تسمية_عدد_الفواتير = QLabel("عدد الفواتير: 0")
        self.تسمية_إجمالي_المشتريات = QLabel("إجمالي المشتريات: 0.00")
        self.تسمية_المبالغ_المدفوعة = QLabel("المبالغ المدفوعة: 0.00")
        self.تسمية_المبالغ_المستحقة = QLabel("المبالغ المستحقة: 0.00")

        تخطيط_الحالة.addWidget(self.تسمية_عدد_الفواتير)
        تخطيط_الحالة.addWidget(self.تسمية_إجمالي_المشتريات)
        تخطيط_الحالة.addWidget(self.تسمية_المبالغ_المدفوعة)
        تخطيط_الحالة.addWidget(self.تسمية_المبالغ_المستحقة)
        تخطيط_الحالة.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الحالة)

    def تحميل_الموردين(self):
        """
        دالة تحميل قائمة الموردين
        """
        try:
            استعلام = "SELECT اسم_المورد FROM الموردين WHERE حالة_النشاط = TRUE ORDER BY اسم_المورد"
            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_مورد.clear()
            self.قائمة_مورد.addItem("الكل")

            if الموردين:
                for مورد in الموردين:
                    self.قائمة_مورد.addItem(مورد[0])

        except Exception as e:
            print(f"خطأ في تحميل الموردين: {str(e)}")

    def تحميل_فواتير_الشراء(self):
        """
        دالة تحميل جميع فواتير الشراء
        """
        try:
            استعلام = """
            SELECT
                ف.رقم_الفاتورة,
                م.اسم_المورد,
                ف.رقم_فاتورة_المورد,
                DATE_FORMAT(ف.تاريخ_الشراء, '%Y-%m-%d') as تاريخ_الشراء,
                ف.إجمالي_الفاتورة,
                ف.المبلغ_المدفوع,
                ف.المبلغ_المتبقي,
                ف.حالة_السداد,
                ف.ملاحظات
            FROM فواتير_الشراء ف
            JOIN الموردين م ON ف.رقم_المورد = م.رقم_المورد
            ORDER BY ف.تاريخ_الإنشاء DESC
            """

            الفواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.جدول_فواتير_الشراء.setRowCount(0)

            if الفواتير:
                self.جدول_فواتير_الشراء.setRowCount(len(الفواتير))

                for صف, فاتورة in enumerate(الفواتير):
                    for عمود, قيمة in enumerate(فاتورة):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة is not None else "")

                        # تلوين حالة السداد
                        if عمود == 7:  # عمود حالة السداد
                            if قيمة == "مسددة":
                                عنصر.setBackground(QColor("#d5f4e6"))
                            elif قيمة == "جزئية":
                                عنصر.setBackground(QColor("#fff3cd"))
                            elif قيمة == "غير مسددة":
                                عنصر.setBackground(QColor("#f8d7da"))

                        # تلوين المبالغ
                        elif عمود in [4, 5, 6]:  # أعمدة المبالغ
                            مبلغ = float(قيمة) if قيمة else 0.0
                            عنصر.setText(f"{مبلغ:,.2f}")
                            if عمود == 6 and مبلغ > 0:  # المبلغ المتبقي
                                عنصر.setForeground(QColor("#e74c3c"))

                        self.جدول_فواتير_الشراء.setItem(صف, عمود, عنصر)

            # تحديث الإحصائيات
            self.تحديث_إحصائيات_الشراء()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل فواتير الشراء: {str(e)}")

    def تحديث_إحصائيات_الشراء(self):
        """
        دالة تحديث الإحصائيات السريعة
        """
        try:
            # عدد الفواتير
            استعلام_عدد = "SELECT COUNT(*) FROM فواتير_الشراء"
            عدد_الفواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_عدد)[0][0]

            # إجمالي المشتريات
            استعلام_مشتريات = "SELECT COALESCE(SUM(إجمالي_الفاتورة), 0) FROM فواتير_الشراء"
            إجمالي_المشتريات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_مشتريات)[0][0]

            # المبالغ المدفوعة
            استعلام_مدفوعة = "SELECT COALESCE(SUM(المبلغ_المدفوع), 0) FROM فواتير_الشراء"
            المبالغ_المدفوعة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_مدفوعة)[0][0]

            # المبالغ المستحقة
            استعلام_مستحقة = "SELECT COALESCE(SUM(المبلغ_المتبقي), 0) FROM فواتير_الشراء"
            المبالغ_المستحقة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_مستحقة)[0][0]

            # تحديث التسميات
            self.تسمية_عدد_الفواتير.setText(f"عدد الفواتير: {عدد_الفواتير}")
            self.تسمية_إجمالي_المشتريات.setText(f"إجمالي المشتريات: {إجمالي_المشتريات:,.2f}")
            self.تسمية_المبالغ_المدفوعة.setText(f"المبالغ المدفوعة: {المبالغ_المدفوعة:,.2f}")
            self.تسمية_المبالغ_المستحقة.setText(f"المبالغ المستحقة: {المبالغ_المستحقة:,.2f}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def البحث_عن_الفواتير(self):
        """
        دالة البحث والفلترة في فواتير الشراء
        """
        نص_البحث = self.حقل_البحث.text().strip()
        مورد_مختار = self.قائمة_مورد.currentText()
        حالة_سداد_مختارة = self.قائمة_حالة_سداد.currentText()

        for صف in range(self.جدول_فواتير_الشراء.rowCount()):
            إظهار_الصف = True

            # فلترة النص
            if نص_البحث:
                وجد_النص = False
                for عمود in range(self.جدول_فواتير_الشراء.columnCount()):
                    عنصر = self.جدول_فواتير_الشراء.item(صف, عمود)
                    if عنصر and نص_البحث.lower() in عنصر.text().lower():
                        وجد_النص = True
                        break
                if not وجد_النص:
                    إظهار_الصف = False

            # فلترة المورد
            if مورد_مختار != "الكل" and إظهار_الصف:
                عنصر_مورد = self.جدول_فواتير_الشراء.item(صف, 1)
                if not عنصر_مورد or عنصر_مورد.text() != مورد_مختار:
                    إظهار_الصف = False

            # فلترة حالة السداد
            if حالة_سداد_مختارة != "الكل" and إظهار_الصف:
                عنصر_حالة = self.جدول_فواتير_الشراء.item(صف, 7)
                if not عنصر_حالة or عنصر_حالة.text() != حالة_سداد_مختارة:
                    إظهار_الصف = False

            self.جدول_فواتير_الشراء.setRowHidden(صف, not إظهار_الصف)

    def عند_تحديد_فاتورة(self):
        """
        دالة تنفذ عند تحديد فاتورة من الجدول
        """
        الصف_المحدد = self.جدول_فواتير_الشراء.currentRow()

        if الصف_المحدد >= 0:
            عنصر_رقم_فاتورة = self.جدول_فواتير_الشراء.item(الصف_المحدد, 0)
            if عنصر_رقم_فاتورة:
                self.الفاتورة_المحددة = int(عنصر_رقم_فاتورة.text())

                # تفعيل أزرار الإجراءات (عدا زر الإضافة)
                for مفتاح, زر in self.أزرار_الإجراءات.items():
                    if مفتاح != "add_invoice":
                        زر.setEnabled(True)
        else:
            self.الفاتورة_المحددة = None
            # تعطيل أزرار الإجراءات (عدا زر الإضافة)
            for مفتاح, زر in self.أزرار_الإجراءات.items():
                if مفتاح != "add_invoice":
                    زر.setEnabled(False)

    def إضافة_فاتورة_شراء(self):
        """
        دالة إضافة فاتورة شراء جديدة
        """
        QMessageBox.information(self, "معلومات", "إضافة فاتورة شراء جديدة - قيد التطوير")

    def عرض_تفاصيل_فاتورة(self):
        """
        دالة عرض تفاصيل الفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        حوار = حوار_تفاصيل_فاتورة(self, self.الفاتورة_المحددة, "شراء")
        حوار.exec()

    def تعديل_فاتورة_شراء(self):
        """
        دالة تعديل الفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تعديل فاتورة الشراء رقم {self.الفاتورة_المحددة} - قيد التطوير")

    def تحديث_حالة_السداد(self):
        """
        دالة تحديث حالة السداد للفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        QMessageBox.information(self, "معلومات", f"تحديث حالة السداد للفاتورة رقم {self.الفاتورة_المحددة} - قيد التطوير")

    def حذف_فاتورة(self):
        """
        دالة حذف الفاتورة المحددة
        """
        if not self.الفاتورة_المحددة:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة أولاً")
            return

        رد = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف فاتورة الشراء رقم {self.الفاتورة_المحددة}؟\n"
            "سيتم حذف جميع تفاصيل الفاتورة أيضاً.",
            QMessageBox.Yes | QMessageBox.No
        )

        if رد == QMessageBox.Yes:
            try:
                # حذف تفاصيل الفاتورة أولاً
                استعلام_تفاصيل = "DELETE FROM تفاصيل_فواتير_الشراء WHERE رقم_الفاتورة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تفاصيل, (self.الفاتورة_المحددة,))

                # حذف الفاتورة
                استعلام_فاتورة = "DELETE FROM فواتير_الشراء WHERE رقم_الفاتورة = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة, (self.الفاتورة_المحددة,))

                QMessageBox.information(self, "نجح", "تم حذف فاتورة الشراء بنجاح")
                self.تحميل_فواتير_الشراء()
                self.الفاتورة_المحددة = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف فاتورة الشراء: {str(e)}")


class تبويب_تقارير_الفواتير(QWidget):
    """
    تبويب تقارير الفواتير
    """

    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب تقارير الفواتير
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات

        self.إعداد_التبويب()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب تقارير الفواتير
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان التبويب
        عنوان = QLabel("تقارير الفواتير")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط اختيار التقرير والفترة
        self.إنشاء_شريط_التقارير(التخطيط_الرئيسي)

        # منطقة عرض التقرير
        self.إنشاء_منطقة_التقرير(التخطيط_الرئيسي)

    def إنشاء_شريط_التقارير(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط اختيار التقارير
        """
        إطار_التقارير = QFrame()
        إطار_التقارير.setObjectName("reports_frame")
        إطار_التقارير.setFixedHeight(80)

        تخطيط_التقارير = QHBoxLayout(إطار_التقارير)
        تخطيط_التقارير.setContentsMargins(15, 10, 15, 10)
        تخطيط_التقارير.setSpacing(15)

        # نوع التقرير
        تسمية_تقرير = QLabel("نوع التقرير:")
        self.قائمة_تقرير = QComboBox()
        self.قائمة_تقرير.addItems([
            "تقرير المبيعات اليومية",
            "تقرير المبيعات الشهرية",
            "تقرير المشتريات",
            "تقرير الأرباح والخسائر",
            "تقرير العملاء الأكثر شراءً",
            "تقرير المنتجات الأكثر مبيعاً"
        ])

        # الفترة الزمنية
        تسمية_فترة = QLabel("الفترة:")
        self.قائمة_فترة = QComboBox()
        self.قائمة_فترة.addItems([
            "اليوم", "هذا الأسبوع", "هذا الشهر",
            "آخر 3 شهور", "آخر 6 شهور", "هذا العام"
        ])

        # زر إنشاء التقرير
        زر_إنشاء_تقرير = QPushButton("إنشاء التقرير")
        زر_إنشاء_تقرير.clicked.connect(self.إنشاء_تقرير)

        # زر طباعة التقرير
        زر_طباعة = QPushButton("طباعة التقرير")
        زر_طباعة.clicked.connect(self.طباعة_تقرير)

        # زر تصدير التقرير
        زر_تصدير = QPushButton("تصدير PDF")
        زر_تصدير.clicked.connect(self.تصدير_تقرير)

        تخطيط_التقارير.addWidget(تسمية_تقرير)
        تخطيط_التقارير.addWidget(self.قائمة_تقرير)
        تخطيط_التقارير.addWidget(تسمية_فترة)
        تخطيط_التقارير.addWidget(self.قائمة_فترة)
        تخطيط_التقارير.addWidget(زر_إنشاء_تقرير)
        تخطيط_التقارير.addWidget(زر_طباعة)
        تخطيط_التقارير.addWidget(زر_تصدير)
        تخطيط_التقارير.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_التقارير)

    def إنشاء_منطقة_التقرير(self, التخطيط_الرئيسي):
        """
        دالة إنشاء منطقة عرض التقرير
        """
        self.منطقة_التقرير = QTextEdit()
        self.منطقة_التقرير.setObjectName("report_area")
        self.منطقة_التقرير.setReadOnly(True)
        self.منطقة_التقرير.setPlainText("اختر نوع التقرير والفترة الزمنية ثم اضغط 'إنشاء التقرير'")

        التخطيط_الرئيسي.addWidget(self.منطقة_التقرير)

    def إنشاء_تقرير(self):
        """
        دالة إنشاء التقرير المحدد
        """
        نوع_التقرير = self.قائمة_تقرير.currentText()
        الفترة = self.قائمة_فترة.currentText()

        # محاكاة إنشاء التقرير
        تقرير = f"""
=== {نوع_التقرير} ===
الفترة: {الفترة}
تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== ملخص التقرير ===
• إجمالي الفواتير: 150 فاتورة
• إجمالي المبيعات: 125,000.00 ريال
• إجمالي المشتريات: 85,000.00 ريال
• صافي الربح: 40,000.00 ريال

=== تفاصيل إضافية ===
هذا التقرير قيد التطوير وسيتم إضافة البيانات الفعلية قريباً.

=== ملاحظات ===
- جميع المبالغ بالريال السعودي
- التقرير يشمل الفترة المحددة فقط
- للحصول على تقارير مفصلة أكثر، يرجى التواصل مع الإدارة
        """

        self.منطقة_التقرير.setPlainText(تقرير)

    def طباعة_تقرير(self):
        """
        دالة طباعة التقرير
        """
        QMessageBox.information(self, "معلومات", "طباعة التقرير - قيد التطوير")

    def تصدير_تقرير(self):
        """
        دالة تصدير التقرير إلى PDF
        """
        QMessageBox.information(self, "معلومات", "تصدير التقرير - قيد التطوير")


class تبويب_إحصائيات_المبيعات(QWidget):
    """
    تبويب إحصائيات المبيعات
    """

    def __init__(self, قاعدة_البيانات):
        """
        دالة التهيئة لتبويب إحصائيات المبيعات
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات

        self.إعداد_التبويب()
        self.تحميل_الإحصائيات()

    def إعداد_التبويب(self):
        """
        دالة إعداد تبويب إحصائيات المبيعات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(15, 15, 15, 15)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان التبويب
        عنوان = QLabel("إحصائيات المبيعات")
        عنوان.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px;")
        التخطيط_الرئيسي.addWidget(عنوان)

        # بطاقات الإحصائيات
        self.إنشاء_بطاقات_الإحصائيات(التخطيط_الرئيسي)

        # جداول الإحصائيات التفصيلية
        self.إنشاء_جداول_الإحصائيات(التخطيط_الرئيسي)

    def إنشاء_بطاقات_الإحصائيات(self, التخطيط_الرئيسي):
        """
        دالة إنشاء بطاقات الإحصائيات السريعة
        """
        إطار_البطاقات = QFrame()
        إطار_البطاقات.setFixedHeight(120)

        تخطيط_البطاقات = QHBoxLayout(إطار_البطاقات)
        تخطيط_البطاقات.setContentsMargins(10, 10, 10, 10)
        تخطيط_البطاقات.setSpacing(15)

        # بطاقة إجمالي المبيعات
        self.بطاقة_إجمالي_مبيعات = self.إنشاء_بطاقة_إحصائية("إجمالي المبيعات", "0.00", "#27ae60")

        # بطاقة عدد الفواتير
        self.بطاقة_عدد_فواتير = self.إنشاء_بطاقة_إحصائية("عدد الفواتير", "0", "#3498db")

        # بطاقة متوسط الفاتورة
        self.بطاقة_متوسط_فاتورة = self.إنشاء_بطاقة_إحصائية("متوسط الفاتورة", "0.00", "#e67e22")

        # بطاقة المبالغ المتبقية
        self.بطاقة_مبالغ_متبقية = self.إنشاء_بطاقة_إحصائية("المبالغ المتبقية", "0.00", "#e74c3c")

        تخطيط_البطاقات.addWidget(self.بطاقة_إجمالي_مبيعات)
        تخطيط_البطاقات.addWidget(self.بطاقة_عدد_فواتير)
        تخطيط_البطاقات.addWidget(self.بطاقة_متوسط_فاتورة)
        تخطيط_البطاقات.addWidget(self.بطاقة_مبالغ_متبقية)

        التخطيط_الرئيسي.addWidget(إطار_البطاقات)

    def إنشاء_بطاقة_إحصائية(self, العنوان, القيمة, اللون):
        """
        دالة إنشاء بطاقة إحصائية
        """
        بطاقة = QFrame()
        بطاقة.setStyleSheet(f"""
        QFrame {{
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            border-left: 4px solid {اللون};
        }}
        """)

        تخطيط_بطاقة = QVBoxLayout(بطاقة)
        تخطيط_بطاقة.setContentsMargins(15, 10, 15, 10)
        تخطيط_بطاقة.setSpacing(5)

        # عنوان البطاقة
        تسمية_عنوان = QLabel(العنوان)
        تسمية_عنوان.setStyleSheet("font-size: 12px; color: #7f8c8d; font-weight: bold;")

        # قيمة البطاقة
        تسمية_قيمة = QLabel(القيمة)
        تسمية_قيمة.setStyleSheet(f"font-size: 24px; color: {اللون}; font-weight: bold;")
        تسمية_قيمة.setObjectName("card_value")

        تخطيط_بطاقة.addWidget(تسمية_عنوان)
        تخطيط_بطاقة.addWidget(تسمية_قيمة)
        تخطيط_بطاقة.addStretch()

        return بطاقة

    def إنشاء_جداول_الإحصائيات(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جداول الإحصائيات التفصيلية
        """
        # تبويبات فرعية للإحصائيات
        تبويبات_إحصائيات = QTabWidget()

        # تبويب أفضل العملاء
        تبويب_عملاء = QWidget()
        self.إنشاء_جدول_أفضل_عملاء(تبويب_عملاء)
        تبويبات_إحصائيات.addTab(تبويب_عملاء, "أفضل العملاء")

        # تبويب أفضل المنتجات
        تبويب_منتجات = QWidget()
        self.إنشاء_جدول_أفضل_منتجات(تبويب_منتجات)
        تبويبات_إحصائيات.addTab(تبويب_منتجات, "أفضل المنتجات")

        # تبويب المبيعات الشهرية
        تبويب_شهرية = QWidget()
        self.إنشاء_جدول_مبيعات_شهرية(تبويب_شهرية)
        تبويبات_إحصائيات.addTab(تبويب_شهرية, "المبيعات الشهرية")

        التخطيط_الرئيسي.addWidget(تبويبات_إحصائيات)

    def إنشاء_جدول_أفضل_عملاء(self, تبويب):
        """
        دالة إنشاء جدول أفضل العملاء
        """
        تخطيط = QVBoxLayout(تبويب)

        جدول = QTableWidget()
        جدول.setColumnCount(4)
        جدول.setHorizontalHeaderLabels(["اسم العميل", "عدد الفواتير", "إجمالي المشتريات", "آخر شراء"])

        # بيانات تجريبية
        بيانات_عملاء = [
            ["أحمد محمد", "15", "25,000.00", "2024-01-08"],
            ["فاطمة علي", "12", "18,500.00", "2024-01-07"],
            ["محمد سالم", "10", "15,200.00", "2024-01-06"]
        ]

        جدول.setRowCount(len(بيانات_عملاء))
        for صف, بيانات in enumerate(بيانات_عملاء):
            for عمود, قيمة in enumerate(بيانات):
                جدول.setItem(صف, عمود, QTableWidgetItem(قيمة))

        تخطيط.addWidget(جدول)

    def إنشاء_جدول_أفضل_منتجات(self, تبويب):
        """
        دالة إنشاء جدول أفضل المنتجات
        """
        تخطيط = QVBoxLayout(تبويب)

        جدول = QTableWidget()
        جدول.setColumnCount(4)
        جدول.setHorizontalHeaderLabels(["اسم المنتج", "الكمية المباعة", "إجمالي المبيعات", "متوسط السعر"])

        # بيانات تجريبية
        بيانات_منتجات = [
            ["لابتوب ديل", "25", "75,000.00", "3,000.00"],
            ["هاتف سامسونج", "40", "60,000.00", "1,500.00"],
            ["طابعة HP", "15", "22,500.00", "1,500.00"]
        ]

        جدول.setRowCount(len(بيانات_منتجات))
        for صف, بيانات in enumerate(بيانات_منتجات):
            for عمود, قيمة in enumerate(بيانات):
                جدول.setItem(صف, عمود, QTableWidgetItem(قيمة))

        تخطيط.addWidget(جدول)

    def إنشاء_جدول_مبيعات_شهرية(self, تبويب):
        """
        دالة إنشاء جدول المبيعات الشهرية
        """
        تخطيط = QVBoxLayout(تبويب)

        جدول = QTableWidget()
        جدول.setColumnCount(4)
        جدول.setHorizontalHeaderLabels(["الشهر", "عدد الفواتير", "إجمالي المبيعات", "متوسط الفاتورة"])

        # بيانات تجريبية
        بيانات_شهرية = [
            ["يناير 2024", "45", "135,000.00", "3,000.00"],
            ["ديسمبر 2023", "38", "114,000.00", "3,000.00"],
            ["نوفمبر 2023", "42", "126,000.00", "3,000.00"]
        ]

        جدول.setRowCount(len(بيانات_شهرية))
        for صف, بيانات in enumerate(بيانات_شهرية):
            for عمود, قيمة in enumerate(بيانات):
                جدول.setItem(صف, عمود, QTableWidgetItem(قيمة))

        تخطيط.addWidget(جدول)

    def تحميل_الإحصائيات(self):
        """
        دالة تحميل الإحصائيات الفعلية
        """
        try:
            # إجمالي المبيعات
            استعلام_مبيعات = "SELECT COALESCE(SUM(المبلغ_النهائي), 0) FROM فواتير_البيع"
            إجمالي_مبيعات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_مبيعات)[0][0]

            # عدد الفواتير
            استعلام_عدد = "SELECT COUNT(*) FROM فواتير_البيع"
            عدد_فواتير = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_عدد)[0][0]

            # متوسط الفاتورة
            متوسط_فاتورة = إجمالي_مبيعات / عدد_فواتير if عدد_فواتير > 0 else 0

            # المبالغ المتبقية
            استعلام_متبقية = "SELECT COALESCE(SUM(المبلغ_المتبقي), 0) FROM فواتير_البيع"
            مبالغ_متبقية = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_متبقية)[0][0]

            # تحديث البطاقات
            self.تحديث_بطاقة(self.بطاقة_إجمالي_مبيعات, f"{إجمالي_مبيعات:,.2f}")
            self.تحديث_بطاقة(self.بطاقة_عدد_فواتير, str(عدد_فواتير))
            self.تحديث_بطاقة(self.بطاقة_متوسط_فاتورة, f"{متوسط_فاتورة:,.2f}")
            self.تحديث_بطاقة(self.بطاقة_مبالغ_متبقية, f"{مبالغ_متبقية:,.2f}")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {str(e)}")

    def تحديث_بطاقة(self, بطاقة, قيمة_جديدة):
        """
        دالة تحديث قيمة البطاقة
        """
        تسمية_قيمة = بطاقة.findChild(QLabel, "card_value")
        if تسمية_قيمة:
            تسمية_قيمة.setText(قيمة_جديدة)


class حوار_تفاصيل_فاتورة(QDialog):
    """
    حوار عرض تفاصيل الفاتورة
    """

    def __init__(self, parent, رقم_الفاتورة, نوع_الفاتورة):
        """
        دالة التهيئة لحوار تفاصيل الفاتورة
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.رقم_الفاتورة = رقم_الفاتورة
        self.نوع_الفاتورة = نوع_الفاتورة

        self.إعداد_الحوار()
        self.تحميل_تفاصيل_الفاتورة()

    def إعداد_الحوار(self):
        """
        دالة إعداد حوار تفاصيل الفاتورة
        """
        self.setWindowTitle(f"تفاصيل فاتورة {self.نوع_الفاتورة} رقم {self.رقم_الفاتورة}")
        self.setModal(True)
        self.setFixedSize(800, 600)

        # التخطيط الرئيسي
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # معلومات الفاتورة الأساسية
        self.إنشاء_معلومات_أساسية(التخطيط_الرئيسي)

        # جدول تفاصيل الفاتورة
        self.إنشاء_جدول_التفاصيل(التخطيط_الرئيسي)

        # ملخص الفاتورة
        self.إنشاء_ملخص_الفاتورة(التخطيط_الرئيسي)

        # زر الإغلاق
        زر_إغلاق = QPushButton("إغلاق")
        زر_إغلاق.clicked.connect(self.accept)
        التخطيط_الرئيسي.addWidget(زر_إغلاق)

    def إنشاء_معلومات_أساسية(self, التخطيط_الرئيسي):
        """
        دالة إنشاء قسم المعلومات الأساسية
        """
        إطار_معلومات = QFrame()
        إطار_معلومات.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px;")
        إطار_معلومات.setFixedHeight(100)

        تخطيط_معلومات = QHBoxLayout(إطار_معلومات)
        تخطيط_معلومات.setContentsMargins(15, 10, 15, 10)
        تخطيط_معلومات.setSpacing(20)

        # معلومات الفاتورة
        self.تسمية_رقم_فاتورة = QLabel(f"رقم الفاتورة: {self.رقم_الفاتورة}")
        self.تسمية_تاريخ = QLabel("التاريخ: غير محدد")
        self.تسمية_عميل_مورد = QLabel("العميل/المورد: غير محدد")
        self.تسمية_حالة_سداد = QLabel("حالة السداد: غير محدد")

        تخطيط_معلومات.addWidget(self.تسمية_رقم_فاتورة)
        تخطيط_معلومات.addWidget(self.تسمية_تاريخ)
        تخطيط_معلومات.addWidget(self.تسمية_عميل_مورد)
        تخطيط_معلومات.addWidget(self.تسمية_حالة_سداد)
        تخطيط_معلومات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_معلومات)

    def إنشاء_جدول_التفاصيل(self, التخطيط_الرئيسي):
        """
        دالة إنشاء جدول تفاصيل الفاتورة
        """
        self.جدول_التفاصيل = QTableWidget()

        if self.نوع_الفاتورة == "بيع":
            الأعمدة = ["اسم المنتج", "الكمية", "سعر الوحدة", "الخصم", "الإجمالي"]
        else:
            الأعمدة = ["اسم المنتج", "الكمية", "سعر الشراء", "الإجمالي"]

        self.جدول_التفاصيل.setColumnCount(len(الأعمدة))
        self.جدول_التفاصيل.setHorizontalHeaderLabels(الأعمدة)

        # تنسيق الجدول
        self.جدول_التفاصيل.setAlternatingRowColors(True)
        header = self.جدول_التفاصيل.horizontalHeader()
        header.setStretchLastSection(True)

        التخطيط_الرئيسي.addWidget(self.جدول_التفاصيل)

    def إنشاء_ملخص_الفاتورة(self, التخطيط_الرئيسي):
        """
        دالة إنشاء ملخص الفاتورة
        """
        إطار_ملخص = QFrame()
        إطار_ملخص.setStyleSheet("background-color: #e9ecef; border: 1px solid #ced4da; border-radius: 8px;")
        إطار_ملخص.setFixedHeight(80)

        تخطيط_ملخص = QHBoxLayout(إطار_ملخص)
        تخطيط_ملخص.setContentsMargins(15, 10, 15, 10)
        تخطيط_ملخص.setSpacing(20)

        # ملخص المبالغ
        self.تسمية_إجمالي = QLabel("الإجمالي: 0.00")
        self.تسمية_خصم = QLabel("الخصم: 0.00")
        self.تسمية_مبلغ_نهائي = QLabel("المبلغ النهائي: 0.00")
        self.تسمية_مبلغ_مدفوع = QLabel("المبلغ المدفوع: 0.00")
        self.تسمية_مبلغ_متبقي = QLabel("المبلغ المتبقي: 0.00")

        تخطيط_ملخص.addWidget(self.تسمية_إجمالي)
        if self.نوع_الفاتورة == "بيع":
            تخطيط_ملخص.addWidget(self.تسمية_خصم)
        تخطيط_ملخص.addWidget(self.تسمية_مبلغ_نهائي)
        تخطيط_ملخص.addWidget(self.تسمية_مبلغ_مدفوع)
        تخطيط_ملخص.addWidget(self.تسمية_مبلغ_متبقي)
        تخطيط_ملخص.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_ملخص)

    def تحميل_تفاصيل_الفاتورة(self):
        """
        دالة تحميل تفاصيل الفاتورة من قاعدة البيانات
        """
        QMessageBox.information(self, "معلومات", f"تحميل تفاصيل فاتورة {self.نوع_الفاتورة} رقم {self.رقم_الفاتورة} - قيد التطوير")
