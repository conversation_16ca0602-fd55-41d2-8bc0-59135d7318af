# واجهة إدارة البنوك والفروع

## نظرة عامة
تم تطوير واجهة شاملة لإدارة البنوك والفروع في نظام إدارة المبيعات والمخزون. تتضمن الواجهة نظام شجرة متقدم لعرض البنوك الرئيسية والفروع التابعة لها.

## الميزات الرئيسية

### 1. نظام الشجرة التفاعلي
- عرض البنوك الرئيسية والفروع في شكل شجرة منظمة
- تلوين مختلف للبنوك الرئيسية والفروع
- إمكانية توسيع وطي الفروع
- عرض حالة النشاط لكل بنك/فرع

### 2. إدارة البنوك الرئيسية
- إضافة بنوك رئيسية جديدة
- تعديل بيانات البنوك الموجودة
- حذف البنوك (مع التحقق من الارتباطات)
- تفعيل/إلغاء تفعيل البنوك

### 3. إدارة الفروع
- إضافة فروع للبنوك الرئيسية
- ربط الفروع بالبنوك الرئيسية
- إدارة مستقلة لكل فرع

### 4. شريط الأدوات المتقدم
- أزرار سريعة للعمليات الأساسية
- تصميم عصري مع ألوان مميزة
- أيقونات واضحة لكل عملية

### 5. نموذج التفاصيل المحدث
- عرض وتعديل تفاصيل البنك المحدد
- حقول منظمة ومنسقة مع البيانات الجديدة
- التحقق من صحة البيانات
- حقول اختيارية للهاتف والعنوان

## هيكل قاعدة البيانات المحدث

### جدول البنوك
```sql
CREATE TABLE البنوك (
    رقم_البنك INT AUTO_INCREMENT PRIMARY KEY,
    اسم_البنك VARCHAR(100) NOT NULL,
    بنك_رئيسي INT DEFAULT NULL,
    رقم_حساب_الشركة VARCHAR(50),
    رقم_الهاتف VARCHAR(20),
    العنوان TEXT,
    حالة_النشاط BOOLEAN DEFAULT TRUE,
    تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (بنك_رئيسي) REFERENCES البنوك(رقم_البنك),
    INDEX idx_اسم_بنك (اسم_البنك),
    INDEX idx_هاتف_بنك (رقم_الهاتف)
);
```

## الحقول المتاحة

### الحقول الأساسية (مطلوبة)
- **اسم البنك**: اسم البنك أو الفرع (مطلوب)
- **البنك الرئيسي**: للفروع فقط (اختياري للبنوك الرئيسية)
- **الحالة**: تفعيل أو إلغاء تفعيل البنك

### الحقول الاختيارية (جديدة)
- **رقم حساب الشركة**: رقم حساب الشركة في البنك (اختياري)
- **رقم الهاتف**: رقم هاتف البنك أو الفرع (اختياري)
- **العنوان**: عنوان البنك أو الفرع (اختياري)

### مميزات الحقول الجديدة
- عرض معلومات إضافية في tooltip عند التمرير على البنك في الشجرة
- إمكانية ترك الحقول فارغة دون تأثير على عمل النظام
- تحسين تجربة المستخدم بمعلومات أكثر تفصيلاً

## الوظائف المتاحة

### إضافة بنك رئيسي
1. اضغط على زر "إضافة بنك رئيسي"
2. أدخل اسم البنك (مطلوب)
3. أدخل رقم الحساب والهاتف والعنوان (اختياري)
4. اضغط "حفظ"

### إضافة فرع
1. اضغط على زر "إضافة فرع"
2. أدخل اسم الفرع (مطلوب)
3. اختر البنك الرئيسي من القائمة
4. أدخل البيانات الإضافية (اختياري)
5. اضغط "حفظ"

### تعديل بنك/فرع
1. حدد البنك من الشجرة
2. اضغط زر "تعديل"
3. عدل البيانات المطلوبة
4. اضغط "حفظ"

### حذف بنك/فرع
1. حدد البنك من الشجرة
2. اضغط زر "حذف"
3. أكد عملية الحذف

## التحقق من الأمان
- منع حذف البنوك المرتبطة بعملاء أو عقود
- التحقق من عدم تكرار أسماء البنوك
- التحقق من صحة البيانات المدخلة

## التصميم والواجهة
- تصميم RTL كامل للغة العربية
- ألوان متناسقة ومريحة للعين
- أزرار واضحة مع أيقونات مميزة
- تخطيط مرن ومتجاوب

## الملفات المتأثرة
- `other_interfaces.py`: الواجهة الرئيسية للبنوك
- `db.py`: هيكل قاعدة البيانات
- `main.py`: ربط الواجهة بالتطبيق الرئيسي

## المتطلبات التقنية
- Python 3.8+
- PySide6
- MySQL
- قاعدة بيانات Sales_system

## ملاحظات التطوير
- تم استخدام نمط MVC في التصميم
- كود منظم ومعلق باللغة العربية
- معالجة شاملة للأخطاء
- واجهة سهلة الاستخدام والصيانة
